package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.hystrix.CommoditySaleStatisticsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 产品销售汇总（日报、月报）
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = CommoditySaleStatisticsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface CommoditySaleStatisticsClient {

    /**
     * 产品销售汇总监控--比较
     * @param idto
     * @return
     */
    @RequestMapping(value = "/statistical/commoditySaleStatistics/commoditySaleStatisticsCompareInfo", method = RequestMethod.POST)
    ProductSaleStatisticsMonitorODTO commoditySaleStatisticsCompareInfo(@RequestBody CommoditySaleStatisticsMakeUpIDTO idto);

    /**
     * 产品销售汇总监控--补偿
     * @param commoditySaleStatisticsMakeUpIDTO
     * @return
     */
    @RequestMapping(value = "/statistical/commoditySaleStatistics/commoditySaleStatisticsMakeUp", method = RequestMethod.POST)
    void commoditySaleStatisticsMakeUp(@RequestBody CommoditySaleStatisticsMakeUpIDTO commoditySaleStatisticsMakeUpIDTO);

    /**
     * 产品销售汇总_数据查询
     * @param commoditySaleStatisticsIDTO
     * @return
     */
    @RequestMapping(value = "/statistical/commoditySaleStatistics/queryCommoditySaleStatistics", method = RequestMethod.POST)
    PageInfo<CommoditySaleStatisticsODTO> queryCommoditySaleStatistics(@RequestBody CommoditySaleStatisticsIDTO commoditySaleStatisticsIDTO);

    /**
     * 产品销售汇总--月度定时汇总
     * @return
     */
    @RequestMapping(value = "/statistical/commoditySaleStatistics/commoditySaleStatisticsMonth", method = RequestMethod.GET)
    void commoditySaleStatisticsMonth();

    @RequestMapping(value = "/statistical/commoditySaleStatistics/modifyOrderCompany", method = RequestMethod.POST)
    Integer modifyOrderCompanyByStore(@RequestParam("storeId") Long storeId, @RequestParam("companyId") Long companyId);

    @PostMapping("/statistics/client/queryLatestCommoditySaleTopN")
    List<LatestCommoditySaleStatisticsODTO> queryLatestCommoditySaleTopN(@RequestBody LatestCommoditySaleStatisticsIDTO idto);

    @PostMapping("/statistics/client/queryCommoditySaleQuantityByOrderTimeAndCommIds")
    List<CommoditySaleQuantityQueryODTO> queryCommoditySaleQuantityByOrderTimeAndCommIds(@RequestBody CommoditySaleQuantityQueryIDTO idto);
}
