package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.orderreport.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 产品销售汇总查询条件
 * @date 2019/09/03
 */
@Data
public class CommoditySaleStatisticsIDTO extends Pagination {

    @ApiModelProperty(value = "商品工厂ID(t_factory表主键)")
    private Long factoryId;

    @ApiModelProperty(value = "商品生产组ID(t_factory_workshop表主键)")
    private Long workshopId;

    @ApiModelProperty(value = "商品一级分类ID")
    private Long cateId1;

    @ApiModelProperty(value = "商品Id")
    private Long commodityId;

    @ApiModelProperty(value = "商品车间ID")
    private Long flowshopId;

    @ApiModelProperty(value = "送货日期-开始 日报")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "送货日期-结束 日报")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "送货日期-开始 月报")
    private String startDateStr;

    @ApiModelProperty(value = "送货日期-结束 月报")
    private String endDateStr;

    @ApiModelProperty(value = "产品销售汇总(商品) 和 产品销售汇总(月报) 接口共用，类型区分")
    private Integer paramType;

    @ApiModelProperty(value = "督导id")
    private Long supervisorId;

    private Long loginUserId;

    @ApiModelProperty("公司id")
    private Long companyId;

    @ApiModelProperty("14-排除计划销售订单（营销视角） 15-排除全国销售订单（工厂视角）")
    private Integer filterBusinessType;
}
