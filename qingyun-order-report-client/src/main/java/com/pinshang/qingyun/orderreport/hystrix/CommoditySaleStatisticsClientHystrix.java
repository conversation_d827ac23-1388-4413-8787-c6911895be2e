package com.pinshang.qingyun.orderreport.hystrix;


import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.service.CommoditySaleStatisticsClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CommoditySaleStatisticsClientHystrix implements FallbackFactory<CommoditySaleStatisticsClient> {
    @Override
    public CommoditySaleStatisticsClient create(Throwable throwable) {
        return new CommoditySaleStatisticsClient() {
            @Override
            public ProductSaleStatisticsMonitorODTO commoditySaleStatisticsCompareInfo(CommoditySaleStatisticsMakeUpIDTO idto) {
                return null;
            }

            @Override
            public PageInfo<CommoditySaleStatisticsODTO> queryCommoditySaleStatistics(CommoditySaleStatisticsIDTO commoditySaleStatisticsIDTO) {
                return null;
            }

            @Override
            public void commoditySaleStatisticsMonth() {

            }

            @Override
            public void commoditySaleStatisticsMakeUp(CommoditySaleStatisticsMakeUpIDTO commoditySaleStatisticsMakeUpIDTO) {

            }

            @Override
            public Integer modifyOrderCompanyByStore(Long storeId, Long companyId) {
                return null;
            }

            @Override
            public List<LatestCommoditySaleStatisticsODTO> queryLatestCommoditySaleTopN(LatestCommoditySaleStatisticsIDTO idto) {
                return null;
            }

            @Override
            public List<CommoditySaleQuantityQueryODTO> queryCommoditySaleQuantityByOrderTimeAndCommIds(CommoditySaleQuantityQueryIDTO idto) {
                return null;
            }
        };
    }

}
