package com.pinshang.qingyun.order.manage.controller;/**
 * @Author: sk
 * @Date: 2025/7/16
 */

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.manage.dto.commodity.PlanCommodityODTO;
import com.pinshang.qingyun.order.manage.dto.commodity.PlanCommodityQueryIDTO;
import com.pinshang.qingyun.order.manage.dto.commodity.PlanCommoditySaveIDTO;
import com.pinshang.qingyun.order.manage.service.PlanCommodityService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月16日 下午1:40
 */
@Slf4j
@RequestMapping("/planCommodity")
@RestController
public class PlanCommodityController {

    @Autowired
    private PlanCommodityService planCommodityService;


    @ApiOperation(value = "查询全部计划商品")
    @RequestMapping(value = "/queryAllPlanCommodityList",method = RequestMethod.POST)
    public PageInfo<PlanCommodityODTO> queryAllPlanCommodityList(@RequestBody PlanCommodityQueryIDTO vo) {
        PageInfo<PlanCommodityODTO> pageInfo = planCommodityService.queryPlanCommodityList(vo);
        return pageInfo;
    }

    @ApiOperation(value = "计划商品分页")
    @PostMapping("/queryPlanCommodityList")
    public PageInfo<PlanCommodityODTO> queryPlanCommodityList(@RequestBody PlanCommodityQueryIDTO vo) {
        return planCommodityService.queryPlanCommodityList(vo);
    }


    @ApiOperation(value = "批量添加商品", notes = "批量添加商品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/batchSavePalnCommodity")
    public Boolean batchSavePalnCommodity(@RequestBody PlanCommoditySaveIDTO saveIDTO) {
        return planCommodityService.batchSavePalnCommodity(saveIDTO);
    }

    @ApiOperation(value = "批量删除商品", notes = "批量删除商品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/batchDeletePalnCommodity")
    public Boolean batchDeletePalnCommodity(@RequestBody PlanCommoditySaveIDTO saveIDTO) {
        return planCommodityService.batchDeletePalnCommodity(saveIDTO);
    }


    @ApiOperation(value = "手动推送至顺丰", notes = "手动推送至顺丰", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/handPushToTms")
    public Boolean handPushToTms(@RequestBody PlanCommoditySaveIDTO saveIDTO) {
        return planCommodityService.handPushToTms(saveIDTO);
    }
}
