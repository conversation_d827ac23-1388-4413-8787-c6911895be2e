package com.pinshang.qingyun.order.manage.dto;/**
 * @Author: sk
 * @Date: 2025/7/16
 */

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年07月16日 下午1:50
 */
@Data
public class PlanCommodityODTO {

    @ApiModelProperty("商品id")
    private String commodityId;

    @ApiModelProperty("同步状态 1=已同步  0=未同步")
    private Integer syncStatus;

    @ApiModelProperty("同步时间")
    private String syncTime;

}
