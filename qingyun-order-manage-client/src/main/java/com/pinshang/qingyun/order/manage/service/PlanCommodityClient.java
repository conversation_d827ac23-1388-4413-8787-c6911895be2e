package com.pinshang.qingyun.order.manage.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.manage.dto.PlanCommodityODTO;
import com.pinshang.qingyun.order.manage.dto.PlanCommodityQueryIDTO;
import com.pinshang.qingyun.order.manage.hystrix.PlanCommodityClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @Date 2024/12/02 10:52
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_MANAGE_SERVICE, fallbackFactory = PlanCommodityClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface PlanCommodityClient {

    /**
     * 查询全部计划商品
     * @return
     */
    @RequestMapping(value = "/planCommodity/queryAllPlanCommodityList",method = RequestMethod.POST)
    PageInfo<PlanCommodityODTO> queryAllPlanCommodityList(@RequestBody PlanCommodityQueryIDTO idto);

}
