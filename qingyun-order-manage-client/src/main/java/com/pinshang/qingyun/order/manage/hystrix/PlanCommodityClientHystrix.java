package com.pinshang.qingyun.order.manage.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.manage.dto.PlanCommodityODTO;
import com.pinshang.qingyun.order.manage.dto.PlanCommodityQueryIDTO;
import com.pinshang.qingyun.order.manage.service.PlanCommodityClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/02 10:52
 */
@Component
@Slf4j
public class PlanCommodityClientHystrix implements FallbackFactory<PlanCommodityClient> {
    @Override
    public PlanCommodityClient create(Throwable throwable) {
        return new PlanCommodityClient() {

            @Override
            public PageInfo<PlanCommodityODTO> queryAllPlanCommodityList(PlanCommodityQueryIDTO idto) {
                return null;
            }
        };
    }
}
