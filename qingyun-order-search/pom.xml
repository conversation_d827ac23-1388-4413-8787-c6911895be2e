<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-order-parent</artifactId>
        <version>3.6.8-UP-SNAPSHOT</version>
    </parent>
    <artifactId>qingyun-order-search</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>qingyun-order-search-client</module>
        <module>qingyun-order-search-service</module>
    </modules>
    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <qingyun.box.version>2.0.8-SNAPSHOT</qingyun.box.version>
        <qingyun.base.version>3.6.0-SNAPSHOT</qingyun.base.version>
        <qingyun.common.version>3.2.7-SNAPSHOT</qingyun.common.version>
        <qingyun.purchase.version>2.0.3-SNAPSHOT</qingyun.purchase.version>
        <qingyun.product.version>3.2.9-SNAPSHOT</qingyun.product.version>
        <qingyun.shop.version>3.4.2-SNAPSHOT</qingyun.shop.version>
        <qingyun.storage.version>3.1.9-UP-SNAPSHOT</qingyun.storage.version>
        <qingyun.supplier.version>2.0.5-SNAPSHOT</qingyun.supplier.version>
        <qingyun.sync.version>2.1.4-SNAPSHOT</qingyun.sync.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-box</artifactId>
                <version>${qingyun.box.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base</artifactId>
                <version>${qingyun.base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-shop-client</artifactId>
                <version>${qingyun.shop.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-product-client</artifactId>
                <version>${qingyun.product.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-order-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-order-report-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-sync</artifactId>
                <version>${qingyun.sync.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>4.5.3</version>
            </dependency>
        </dependencies>

    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://192.168.0.31:9001/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://192.168.0.31:9001/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
