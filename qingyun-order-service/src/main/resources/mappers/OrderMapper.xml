<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.OrderMapper">


	<select id="findFrozenProductListByProductIds" parameterType="java.util.List" resultType="com.pinshang.qingyun.order.mapper.entry.order.FrozenProductEntry">
		select
		c.`id` productId ,
		c.`commodity_name` productName,
		c.`commodity_is_quick_freeze` frozen,
		c.`sales_box_capacity`
		from
		t_commodity c where c.id in
		<foreach collection="productIds" index="index" item="productId" open="(" separator="," close=")">
			#{productId}
		</foreach>
	</select>

	<select id="findXdFrozenProductListByProductIds" parameterType="java.util.List" resultType="com.pinshang.qingyun.order.mapper.entry.order.FrozenProductEntry">
		select
		c.`id` productId ,
		c.`commodity_name` productName,
		c.`commodity_is_quick_freeze` frozen,
		c.`xd_sales_box_capacity` salesBoxCapacity
		from
		t_commodity c where c.id in
		<foreach collection="productIds" index="index" item="productId" open="(" separator="," close=")">
			#{productId}
		</foreach>
	</select>


	<select id="findStoreDurationByStoreId" parameterType="java.lang.String" resultType="com.pinshang.qingyun.order.mapper.entry.store.StoreDurationEntry">
			select s.`store_id`,s.`store_code`,s.`begin_time`,s.`end_time` from `t_store_duration` s where s.`store_id` =#{storeId}
	</select>


	<select id="findGiftModelByStoreId" parameterType="java.util.Map" resultType="com.pinshang.qingyun.order.model.gift.GiftModel">
			<![CDATA[
					select
						distinct gm.id,
						gm.company_id,
						gm.gift_model_code,
						gm.gift_model_name,
						gm.begin_date,
						gm.end_date,
						gm.gift_model_type,
						gm.status,
						gm.remark,
						gm.commmdity_codes,
						gm.create_id,
						gm.create_time,
						gm.create_name,
						gm.update_id,
						gm.update_time
					from
					`t_gift_model` gm ,
					`t_gift_model_scope` ms ,
					`t_store_settlement` ss ,
					`t_product_price_model` ppm ,
					`t_store` s
					where 1=1
					and gm.`id` = ms.`gift_model_id`
					and ss.`product_price_model_id` = ppm.`id`
					and ss.`store_id` = s.`id`
					and gm.`status` = 1
					and #{orderTime} between gm.`begin_date` and gm.`end_date`
					and (ms.`type_id` = ss.`settlement_customer_id` or ms.`type_id` = ppm.`id` or ms.`type_id` = s.`id`)
					and s.`id` =#{storeId}
			]]>
	</select>


	<select id="findGiftProductByGiftModelConditionId" parameterType="java.lang.Long" resultType="com.pinshang.qingyun.order.model.gift.GiftProduct">
		select 	p.id,
			p.gift_model_condition_id,
			p.commodity_id,
			p.commodity_code,
			p.commodity_number,
			p.commodity_max_number,
			p.commodity_price,
			p.status,
			p.remark
	from `t_gift_product` p where p.`gift_model_condition_id` = #{giftModelConditionId}
	</select>


	<select id="findOrderProductNumber" parameterType="java.util.Map" resultType="java.math.BigDecimal">
		select  sum(l.`commodity_num`) from `t_order` o  inner join `t_order_list_gift` l  on l.`order_id` = o.`id` where 1=1 and o.`order_status` = 0 and o.`order_time` =#{orderTime} and l.`commodity_id` =#{productId} group by l.`commodity_id`
	</select>


	<select id="findOrderListByPage" parameterType="com.pinshang.qingyun.order.vo.order.OrderListRequestVo" resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderListEntry">
		select
			o.`order_id` order_id,
			o.sub_order_id,
			o.`supplier_id`,
			o.`order_time`,
			o.create_time,
			o.`order_code`,
			o.`order_amount`,
			o.`order_status`,
			o.logistics_status,
			o.`logistics_model` ,
			o.order_type  ,
			o.real_name create_name,
			o.delivery_batch_remark,
			o.create_id,
			o.delivery_batch,
			o.warehouse_id,
		    o.stall_id
		from (
			select
				o.`id` order_id,
				so.id as sub_order_id,
				so.`supplier_id`,
				o.`order_time` ,
				o.`order_code`,
				o.`order_amount`,
				o.`order_status`,
				ro.`status` logistics_status,
				so.`logistics_model` ,
				o.order_type,
				o.create_id,
				o.create_time,
				u.employee_name AS real_name,
				o.delivery_batch_remark,
				o.delivery_batch,
				so.warehouse_id,
				o.stall_id
			from `t_order` o
			inner join `t_sub_order` so on so.`order_id` = o.`id`
			inner join `t_md_receive_order` ro on ro.`sub_order_id` = so.`id`
			left join t_employee_user u on u.user_id = o.create_id
			where 1=1
			and o.`store_id` =#{storeId}
			<if test="orderCode !=null and orderCode!='' "> and o.`order_code` = #{orderCode} </if>
			<!--<if test="orderType !=null and orderType !='' "> and o.`order_type` =#{orderType} </if>-->
			<if test="orderType != null ">
				<choose>
					<when test="orderType == 10">
						and o.`order_type` in(10,21,22,23,24,25,26,27,28,29,30,31,32,33,34,40)
					</when>
					<otherwise>
						and o.`order_type` =#{orderType}
					</otherwise>
				</choose>
			</if>
			<if test="startTime != null and startTime!='' "> <![CDATA[ and o.`order_time`  >= #{startTime} ]]> </if>
			<if test="endTime!=null and endTime !='' "> <![CDATA[ and o.`order_time`  <= #{endTime} ]]> </if>
			<if test="orderId !=null">
				and o.`id` =  #{orderId}
			</if>
			<if test="!isInternal and (consignmentId == null or consignmentId == 0)">
				and o.consignment_id = -1
			</if>
			<if test="!isInternal and consignmentId != null and consignmentId > 0 ">
				and o.consignment_id = #{consignmentId}
			</if>
			<if test="stallId != null">
				and o.stall_id = #{stallId}
			</if>
			<if test="stallIdList != null and stallIdList.size > 0">
				AND o.stall_id IN
				<foreach collection="stallIdList" index="index" item="stallid" open="(" separator="," close=")">
					#{stallid}
				</foreach>
			</if>

			union

			select
				o.`id` order_id,
				so.id as sub_order_id,
				so.`supplier_id`,
				o.`order_time`,
				o.`order_code`,
				o.`order_amount`,
				o.`order_status`,
				so.`receive_status` logistics_status,
				so.`logistics_model`,
				o.order_type,
				o.create_id,
				o.create_time,
				u.employee_name AS real_name,
				o.delivery_batch_remark,
				o.delivery_batch,
				'' as warehouse_id,
				o.stall_id
			from `t_order` o
			inner join `t_md_preorder` so on so.`order_id` = o.`id`
			left join t_employee_user u on u.user_id = o.create_id
			where 1=1
			and o.`store_id` =#{storeId}
			<if test="orderCode !=null and orderCode!='' "> and o.`order_code` = #{orderCode} </if>
			<!--<if test="orderType !=null and orderType !='' "> and o.`order_type` =#{orderType} </if>-->
			<if test="orderType != null ">
				<choose>
					<when test="orderType == 10">
						and o.`order_type` in(10,21,22,23,24,25,26,27,28,29,30,31,32,33,34)
					</when>
					<otherwise>
						and o.`order_type` =#{orderType}
					</otherwise>
				</choose>
			</if>
			<if test="startTime != null and startTime!='' "> <![CDATA[ and o.`order_time`  >= #{startTime} ]]> </if>
			<if test="endTime!=null and endTime !='' "> <![CDATA[ and o.`order_time`  <= #{endTime} ]]> </if>
			<if test="orderId !=null">
				and o.`id` =  #{orderId}
			</if>
			<if test="!isInternal and (consignmentId == null or consignmentId == 0)">
				and 1=1
			</if>
			<if test="!isInternal and consignmentId != null and consignmentId > 0">
				and 1=2
			</if>
			<if test="stallId != null">
				and o.stall_id = #{stallId}
			</if>
			<if test="stallIdList != null and stallIdList.size > 0">
				AND o.stall_id IN
				<foreach collection="stallIdList" index="index" item="stallid" open="(" separator="," close=")">
					#{stallid}
				</foreach>
			</if>
		) o
		where 1=1
		<if test="orderStatus!=null">
			and o.`logistics_status` = #{orderStatus}
		</if>
		<if test="createId!=null and createId !='' ">
			and o.`create_id` = #{createId}
		</if>
		<if test="deliveryBatch!=null and deliveryBatch !='' ">
			and o.`delivery_batch` = #{deliveryBatch}
		</if>
		group by o.`order_code`
		order by o.order_time desc, o.`create_time` desc
	</select>

	<select id="findPreOrderListByPage" parameterType="com.pinshang.qingyun.order.vo.order.OrderListRequestVo" resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderListEntry">
		select
		p.`id` order_id,
		p.`order_time`,
		p.create_time,
		p.`order_code`,
		p.`total_price` order_amount,
		p.`order_status`,
		p.`receive_status` as logisticsStatus,
		p.`approving_time`,
		p.`approving_id`,
		p.`real_order_code`,
		u.employee_name create_name,
		p.supplier_id,
		p.stall_id
		from `t_md_preorder` p
		left join t_employee_user u on u.user_id = p.create_id
		where  p.`store_id` =#{storeId}
		and p.`enterprise_id` =#{enterpriseId}
		<if test="orderCode !=null and orderCode!='' ">
			and p.`order_code` like concat('%',#{orderCode},'%')
		</if>
		<if test="startTime != null and startTime!='' ">
			<![CDATA[ and p.`order_time`  >= #{startTime} ]]>
		</if>
		<if test="endTime!=null and endTime !='' ">
			<![CDATA[ and p.`order_time`  <= #{endTime} ]]>
		</if>
		<if test="createId!=null and createId !='' ">
			AND p.create_id = #{createId}
		</if>
		<if test="orderStatus!=null ">
			AND p.receive_status = #{orderStatus}
		</if>
		<if test="stallId != null">
			and p.stall_id = #{stallId}
		</if>
		<if test="stallIdList != null and stallIdList.size > 0">
			AND p.stall_id IN
			<foreach collection="stallIdList" index="index" item="stallid" open="(" separator="," close=")">
				#{stallid}
			</foreach>
		</if>
		order by p.order_time DESC ,p.`create_time` desc
	</select>

	<select id="orderReplenishmentList" parameterType="com.pinshang.qingyun.order.vo.order.OrderReplenishmentListVo" resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderReplenishmentListEntry">
		SELECT
		po.id,
		tms.shop_code shopCode,
		tms.shop_name AS shopName,
		po.logistics_model,
		po.order_code as subOrderCode,
		po.real_order_code orderCode,
		po.total_price,
		po.receive_status as status,
		po.order_time,
		po.create_time,
		u.employee_name create_name
		FROM
		t_md_preorder po
		LEFT JOIN t_employee_user u ON po.create_id = u.user_id
		LEFT JOIN t_md_shop tms ON tms.store_id = po.store_id
		INNER JOIN t_order t on t.id=po.order_id AND t.mode_type=2
		WHERE 1=1
		<if test="shopId != null and shopId != ''">
			AND tms.id = #{shopId}
		</if>
		<if test="subOrderCode !=null and subOrderCode !='' ">
			and po.order_code = #{subOrderCode}
		</if>
		<if test="createUser !=null and createUser !='' ">
			and (u.employee_name like concat('%',#{createUser},'%') or u.employee_code like concat('%',#{createUser},'%'))
		</if>
		<if test="orderCode !=null and orderCode !='' ">
			and po.real_order_code = #{orderCode}
		</if>
		AND po.order_status in (1,2)
		AND po.logistics_model = 0

		<if test="enterpriseId !=null and enterpriseId !='' ">
			and po.enterprise_id = #{enterpriseId}
		</if>
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and po.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		ORDER BY po.id DESC
	</select>

	<select id="findOrderItemsByOrderId" parameterType="java.lang.Long" resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderItemEntry">
		select
			c.`commodity_code`,
			c.`commodity_name`,
			c.`commodity_spec`,
			l.`commodity_price`,
			l.`commodity_num`,
		    l.remark,
			di.`option_name` commodityUnit,
			CONVERT((l.`commodity_price` * l.`commodity_num`), DECIMAL(10,2)) total_price,
			IFNULL(c.commodity_package_spec,1) commodityPackageSpec
		from `t_order_list` l
		inner join `t_commodity` c  on c.`id` = l.`commodity_id`
		left  join `t_dictionary` di on di.`id` = c.`commodity_unit_id`
		where l.`order_id` =#{orderId}
	</select>


	<select id="findPreOrderItemsByOrderId" parameterType="java.lang.Long" resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderItemEntry">
		select
		    c.commodity_code,
			c.`commodity_name`,
			c.`commodity_spec`,
			i.`price` commodity_price,
			i.`require_quantity` commodity_num,
			di.`option_name` commodityUnit,
			CONVERT((i.`price` * i.`require_quantity`), DECIMAL(10,2)) total_price,
			IFNULL(c.commodity_package_spec,1) commodityPackageSpec
		from `t_md_preorder_item` i
		inner join `t_commodity` c  on c.`id` = i.`commodity_id`
		left  join `t_dictionary` di on di.`id` = c.`commodity_unit_id`
		where i.`preorder_id` =#{orderId}
	</select>





	<update id="updateOrderStatusByParameter" parameterType="java.util.Map">
		update `t_order` set `order_status` =#{orderStatus},update_time = NOW()
		<if test="null != cancelReasonId">
			,cacel_reason_id = #{cancelReasonId}
		</if>
		<if test="null != processStatus">
			,process_status = #{processStatus}
		</if>
		 where `id` = #{orderId} and order_status != 2
	</update>

	<select id="selectOrderInfoByOrderId" parameterType="java.lang.Long" resultType="com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry">
        SELECT
			so.logistics_model logisticsModel,
			soi.commodity_id commodityId
		FROM
			t_sub_order so
		LEFT JOIN t_sub_order_item soi ON soi.sub_order_id = so.id
		WHERE
			so.order_id = #{orderId}
	</select>

	<select id="findOrderProductByOrderId" parameterType="java.lang.Long" resultType="com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry">
		<![CDATA[
			select
				comm.`id` commodityId,
				comm.`commodity_code`,
				comm.`commodity_name`,
				comm.`commodity_spec`,
				comm.`commodity_remark`,
				l.`type` `commodity_type`,
				l.`commodity_num` commodityNumber,
				comm.`commodity_state`,
				comm.`commodity_package_kind` packed_type,
				comm.`commodity_is_quick_freeze` frozen ,
				comm.`storage_condition` storageCondition,
				comm.`commodity_describe` commodityDesc,
				d.`option_code` newProduct,
				l.`commodity_price`
			from
				   `t_order_list` l
			inner join `t_order` o  on o.`id` = l.`order_id`
			inner join `t_commodity` comm on comm.`id` = l.`commodity_id`
			left  join `t_dictionary` d on d.`id` = comm.`commodity_cycle_id`
			where 1=1
			and o.`id` =#{orderId}
		]]>
	</select>

	<select id="findPreOrderProductByOrderId" parameterType="java.lang.Long" resultType="com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry">
			select
				comm.`id` commodityId,
				comm.`commodity_code`,
				comm.`commodity_name`,
				comm.`commodity_spec`,
				l.`require_quantity` commodityNumber,
				comm.`commodity_state`,
				comm.`commodity_package_kind` packed_type,
				comm.`commodity_is_quick_freeze` frozen ,
				comm.`storage_condition` storageCondition,
				comm.`commodity_describe` commodityDesc,
				l.`price` commodity_price,
				l.type commodityType
			from  `t_md_preorder_item` l
			inner join  `t_md_preorder` o  on o.`id` = l.`preorder_id`
			inner join `t_commodity` comm on comm.`id` = l.`commodity_id`
			where 1=1
			and o.`id` =#{orderId}
	</select>


	<update id="updateReceiveOrderBySubOrderId" parameterType="java.util.Map">
		update `t_md_receive_order` set `status` =#{orderStatus} where `sub_order_id` =#{subOrderId}
	</update>

	<select id="findStoreById" parameterType="java.lang.String" resultType="com.pinshang.qingyun.order.mapper.entry.store.StoreEntry">
			select s.* from t_store s where s.`id` =#{storeId}
	</select>

	<select id="findEmployeeById" parameterType="java.lang.Long" resultType="com.pinshang.qingyun.order.mapper.entry.store.EmployeeEntry">
			SELECT e.id, e.`enterprise_id`, e.`employee_code`, e.`employee_name` FROM t_employee_user e WHERE e.employee_id =#{employeeId}
	</select>

	<select id="findOutstandingShopList" resultType="com.pinshang.qingyun.order.mapper.entry.store.OutstandingShopEntry">
	  SELECT
      IFNULL(sd.end_time,'') AS endTime,
      s.store_code AS storeCode,
      s.store_name AS storeName,
      IFNULL(s.store_linkman,'') AS storeLinkman,
      IFNULL(s.linkman_mobile,'') AS linkmanTel,
      IFNULL(s.not_order_remark,'') AS notOrderRemark
	  FROM  t_store s
	  LEFT JOIN t_store_duration sd ON sd.store_id = s.id
	  WHERE
  	  s.store_status=0 AND (s.region_manager_id = #{employeeId} OR s.supervisor_id = #{employeeId})
  	  AND NOT EXISTS(
  		SELECT ss.store_code FROM t_store ss
		JOIN t_order o ON   o.store_id = ss.id AND o.order_status=0  AND o.order_time = DATE_FORMAT(#{orderTime},'%Y-%m-%d')
		WHERE ss.id = s.id )
	  ORDER BY sd.end_time, s.store_code
	</select>

	<select id="queryModifyDeliverDateList" parameterType="com.pinshang.qingyun.order.vo.order.ModifyDeliverDateVo"
			resultType="com.pinshang.qingyun.order.mapper.entry.order.ModifyDeliverDateEntry">
		SELECT
		o.order_time,
		tms.shop_code,
		tms.shop_name,
		o.order_amount,
		CASE o.delivery_batch
		WHEN 0 THEN '无需批次配送'
		WHEN 1 THEN '1配'
		WHEN 2 THEN '2配'
		WHEN 3 THEN '3配'
		WHEN 8 THEN '新开店'
		WHEN 9 THEN '临时批次'
		END AS delivery_batch,
		tu.employee_name AS real_name,
		'未发货' AS delivery_status,
		o.id AS orderId,
		o.order_code
		FROM t_order o
		LEFT JOIN t_sub_order tso ON o.id = tso.order_id
		LEFT JOIN t_md_shop tms ON o.store_id = tms.store_id
		LEFT JOIN t_employee_user tu ON o.create_id = tu.user_id
		WHERE o.order_time >= #{orderTime2}
		<if test="shopId != null and shopId !=''"> AND o.store_id = (SELECT store_id FROM t_md_shop WHERE id = #{shopId}) </if>
		<if test="orderTime != null and orderTime !=''"> AND o.order_time = #{orderTime} </if>
		<if test="createId != null and createId !=''"> AND o.create_id IN (SELECT user_id FROM t_employee_user WHERE (employee_code LIKE concat('%', #{createId}, '%') OR employee_name LIKE concat('%', #{createId}, '%') ))  </if>
		<if test="orderCode != null and orderCode !=''"> AND o.order_code = #{orderCode} </if>
		<if test="deliveryBatch != null"> AND o.delivery_batch = #{deliveryBatch} </if>
		AND o.order_status = 0
		AND tso.logistics_model IN (1, 2)
		AND tms.shop_code IS NOT NULL
		ORDER BY o.order_time DESC
	</select>

	<update id="modifyDeliverDate">
		UPDATE t_order SET order_time = #{date},update_time = NOW() WHERE order_code = #{orderCode}
	</update>

	<select id="findOrderInfo" resultType="com.pinshang.qingyun.order.model.order.Order">
		SELECT
		  o.id,
		  o.order_code,
		  o.create_id,
		  (SELECT employee_name FROM t_employee_user WHERE user_id = o.create_id) AS createUserName,
		  o.create_time,
		  o.store_id,
		  o.order_time
		FROM t_order o
		WHERE o.order_code = #{orderCode}
	</select>


	<select id="querySettleOrder"  resultType="com.pinshang.qingyun.order.vo.order.SettleOrderVo">
		SELECT
		t.id sourceId,
		t.order_code sourceCode,
		t.store_id storeId,
		t.order_time orderTime,
		t.create_time createTime,
		tl.commodity_id commodityId,
		IFNULL(tl.commodity_num,0) number,
		IFNULL(tl.commodity_num,0) deliveryNumber,
		IFNULL(tl.commodity_price,0) unitPrice,
		IFNULL(tl.total_price,0) totalPrice,
		tl.id as itemId,
	    t.consignment_id
		FROM t_order t
		LEFT JOIN t_order_list_gift tl ON tl.order_id = t.id
		WHERE t.order_code = #{orderCode}

		UNION ALL

		SELECT
		t.id sourceId,
		t.order_code sourceCode,
		t.store_id storeId,
		t.create_time orderTime,
		t.create_time createTime,
		tl.commodity_id commodityId,
        IFNULL(tl.return_quantity,0) number,
		IFNULL(tl.real_return_quantity,0) deliveryNumber,
		IFNULL(tl.price,0) unitPrice,
		IFNULL(tl.total_price,0) totalPrice,
		tl.id as itemId,
		t.consignment_id
		FROM
		t_sale_return_order t
		LEFT JOIN  t_sale_return_order_item tl ON tl.sale_return_order_id = t.id
		WHERE t.order_code = #{orderCode}

		UNION ALL

		SELECT
		t.id sourceId,
		t.order_code sourceCode,
		t.store_id storeId,
		t.order_time orderTime,
		t.create_time createTime,
		tl.commodity_id commodityId,
		CASE tl.status  WHEN 0 THEN IFNULL(tl.require_quantity,0) WHEN 2 THEN IFNULL(tl.real_receive_quantity,0) END as  number,
		CASE tl.status  WHEN 0 THEN IFNULL(tl.require_quantity,0) WHEN 2 THEN IFNULL(tl.real_receive_quantity,0) END as  deliveryNumber,
		IFNULL(tl.price,0) unitPrice,
		IFNULL(tl.total_price,0) totalPrice,
		tl.id as itemId,
		-1
		FROM t_md_preorder t
		LEFT JOIN t_md_preorder_item  tl ON tl.preorder_id = t.id
		WHERE
		t.order_code = #{orderCode}

	</select>


	<!--结算明细报表-->
	<select id="settlementDetailsReport" resultType="com.pinshang.qingyun.order.mapper.entry.shop.SettlementDetailsReportEntry">

		SELECT
			*
		FROM
		(
			(
			SELECT
				ms.id AS shopId,
				ms.shop_code AS shopCode,
				ms.shop_name AS shopName,
				o.order_time AS orderTime,
				NULL AS settleTime,
				ord.order_type,
				ord.stall_id,
				o.id as subOrderId,
				o.sub_order_code AS subOrderCode,
				NULL AS preOrderCode,
				ca.cate_name AS categoryName,
				c.id AS commodityId,
				c.bar_code AS barCode,
				c.commodity_code AS commodityCode,
				c.commodity_name AS commodityName,
				c.commodity_spec AS commoditySpec,
				c.commodity_first_id AS commodityFirstId,
				c.commodity_second_id AS commoditySecondId,
				c.commodity_third_id AS commodityThirdId,
				d.option_name AS commodityUnitName,
				oi.price AS price,
				oi.quantity AS quantity,
				oi.real_delivery_quantity AS realDeliveryQuantity,
                (oi.price * oi.real_delivery_quantity)AS settlePrice,
				o.logistics_model AS logisticsModel,
				s.store_type_id AS storeTypeId,
				(
				SELECT
					group_concat(cbc.bar_code ORDER BY cbc.id)
				FROM
					t_commodity co
					LEFT JOIN t_commodity_bar_code cbc ON cbc.commodity_id = co.id
				WHERE
					cbc.commodity_id = c.id
				) AS barCodes
			FROM
				t_sub_order o
				INNER JOIN t_sub_order_item oi ON oi.sub_order_id = o.id
				INNER JOIN t_order ord ON ord.id = o.order_id
				INNER JOIN t_store s ON s.id = ord.store_id
				INNER JOIN t_md_shop ms ON ms.store_id = s.id
				INNER JOIN t_commodity c ON c.id = oi.commodity_id
				LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
				LEFT JOIN t_category ca ON ca.id = c.commodity_first_id
			WHERE
				oi.real_delivery_quantity IS NOT NULL
				<include refid="searchConditions"></include>
				<if test="null != subOrderIdList and subOrderIdList.size > 0">
					and o.id in
					<foreach collection="subOrderIdList" item="code" open="(" close=")" separator=",">
						#{code}
					</foreach>
				</if>
			)
			UNION All
			(
			SELECT
				ms.id AS shopId,
				ms.shop_code AS shopCode,
				ms.shop_name AS shopName,
				o.order_time AS orderTime,
				o.approving_time AS settleTime,
				ord.order_type,
				ord.stall_id,
                null as subOrderId,
				o.real_order_code AS subOrderCode,
				o.order_code AS preOrderCode,
				ca.cate_name AS categoryName,
				c.id AS commodityId,
				c.bar_code AS barCode,
				c.commodity_code AS commodityCode,
				c.commodity_name AS commodityName,
				c.commodity_spec AS commoditySpec,
				c.commodity_first_id AS commodityFirstId,
				c.commodity_second_id AS commoditySecondId,
				c.commodity_third_id AS commodityThirdId,
				d.option_name AS commodityUnitName,
				oi.price AS price,
				oi.require_quantity AS quantity,
				oi.real_receive_quantity AS realDeliveryQuantity,
                (oi.price * oi.real_receive_quantity) AS settlePrice,
				o.logistics_model AS logisticsModel,
				s.store_type_id AS storeTypeId,
				(
				SELECT
					group_concat(cbc.bar_code ORDER BY cbc.id)
					FROM
					t_commodity co
					LEFT JOIN t_commodity_bar_code cbc ON cbc.commodity_id = co.id
					WHERE
					cbc.commodity_id = c.id
				) AS barCodes
			FROM
				t_md_preorder o
				INNER JOIN t_md_preorder_item oi ON oi.preorder_id = o.id
				INNER JOIN t_store s ON s.id = o.store_id
				INNER JOIN t_md_shop ms ON ms.store_id = s.id
				LEFT JOIN t_order ord ON ord.id = o.order_id
				INNER JOIN t_commodity c ON c.id = oi.commodity_id
				LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
				LEFT JOIN t_category ca ON ca.id = c.commodity_first_id
			WHERE
				o.receive_status = 3
				AND o.order_status != 0
				AND oi. STATUS = 2
				<include refid="searchConditions"></include>
				<if test="settleTimeStart !=null and settleTimeEnd != null and settleTimeStart != '' and settleTimeEnd != '' ">
					and o.approving_time BETWEEN #{settleTimeStart} and #{settleTimeEnd}
				</if>
			)
		) a
		<include refid="settlementDetailsReportSearchConditions"></include>
		ORDER BY
			a.shopCode ASC,a.orderTime DESC,
			a.subOrderCode desc ,length (a.commodityCode) asc ,a.commodityCode asc
	</select>


	<!--查询结算总金额-->
	<select id="findTotalSettlePrice" resultType="java.math.BigDecimal">
		SELECT
			SUM(a.settlePrice)
		FROM
			(
				(
				SELECT
					o.sub_order_code AS subOrderCode,
					NULL AS preOrderCode,
                    ROUND((oi.price * oi.real_delivery_quantity),2) AS settlePrice
				FROM
					t_sub_order o
					INNER JOIN t_sub_order_item oi ON oi.sub_order_id = o.id
					INNER JOIN t_order ord ON ord.id = o.order_id
					INNER JOIN t_store s ON s.id = ord.store_id
					INNER JOIN t_md_shop ms ON ms.store_id = s.id
					INNER JOIN t_commodity c ON c.id = oi.commodity_id
				WHERE
					oi.real_delivery_quantity IS NOT NULL
					<include refid="searchConditions"></include>
					<if test="null != subOrderIdList and subOrderIdList.size > 0">
						and o.id in
						<foreach collection="subOrderIdList" item="code" open="(" close=")" separator=",">
							#{code}
						</foreach>
					</if>
				)
			UNION All
			(
				SELECT
					o.real_order_code AS subOrderCode,
					o.order_code AS preOrderCode,
                    ROUND((oi.price * oi.real_receive_quantity),2) AS settlePrice
				FROM
					t_md_preorder o
					INNER JOIN t_md_preorder_item oi ON oi.preorder_id = o.id
					INNER JOIN t_commodity c ON c.id = oi.commodity_id
					INNER JOIN t_store s ON s.id = o.store_id
					INNER JOIN t_md_shop ms ON ms.store_id = s.id
					LEFT JOIN t_order ord ON ord.id = o.order_id
				WHERE
					o.receive_status = 3
					AND o.order_status != 0
					AND oi.STATUS = 2
					<include refid="searchConditions"></include>
					<if test="settleTimeStart !=null and settleTimeEnd != null and settleTimeStart != '' and settleTimeEnd != '' ">
						and o.approving_time BETWEEN #{settleTimeStart} and #{settleTimeEnd}
					</if>
				)
			) a
		<include refid="settlementDetailsReportSearchConditions"></include>
	</select>

	<!--结算清单报表-搜索条件-->
	<sql id="settlementDetailsReportSearchConditions">
		<where>
			<if test="subOrderCode != null and subOrderCode != ''">
				and a.subOrderCode like concat('%',#{subOrderCode},'%')
			</if>
			<if test="preOrderCode != null and preOrderCode != ''">
				and a.preOrderCode like concat('%',#{preOrderCode},'%')
			</if>
		</where>
	</sql>

	<!--公共搜索条件-->
	<sql id="searchConditions">
		<if test="shopId != null">
			and ms.id = #{shopId}
		</if>
		<if test="deliveryTimeStart !=null and deliveryTimeEnd != null and deliveryTimeStart != '' and deliveryTimeEnd != '' ">
			and o.order_time BETWEEN #{deliveryTimeStart} and #{deliveryTimeEnd}
		</if>
		<if test="logisticsModel != null">
			and o.logistics_model = #{logisticsModel}
		</if>
		<if test="categoryId != null">
			and ((c.commodity_first_id = #{categoryId}) or (c.commodity_second_id = #{categoryId}) or (c.commodity_third_id = #{categoryId}))
		</if>
		<if test="barCode!=null and barCode !='' ">
			AND c.id = (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode})
		</if>

		<if test="commodityCode != null and commodityCode != ''">
			and
			(
			c.commodity_code like concat('%',#{commodityCode},'%')
			or
			c.commodity_name like concat('%',#{commodityCode},'%')
			or
			c.commodity_spec like concat('%',#{commodityCode},'%')
			)
		</if>
		<if test="null != consignmentId">
			and ord.consignment_id = #{consignmentId}
		</if>
		<if test="null != orderType">
			<choose>
				<when test="orderType == 0">
					and (ord.order_type is null or ord.order_type = '')
				</when>
				<when test="orderType != 0">
					and ord.order_type = #{orderType}
				</when>
			</choose>
		</if>
		<if test="null != stallId">
			and ord.stall_id = #{stallId}
		</if>
	</sql>

	<select id="queryChangePriceOrderList"  resultType="com.pinshang.qingyun.order.model.order.Order">
		select
            o.`id`,
            o.`order_time`,
            o.`order_code`,
            o.`order_amount`,
            o.`order_status`,
            o.store_id,
            o.total_amount,
            o.final_amount,
            o.create_id,
            o.create_time
        from `t_order` o
        -- INNER join `t_sub_order` so on so.`order_id` = o.`id`
        where 1=1
        and o.`store_id` = #{storeId} and o.change_price_status = 1
        and o.order_status = 0
        and o.order_time >= #{orderTime}
        -- AND so.status = 0
		and case
		  when o.`order_time` <![CDATA[ > ]]> date_add(date_format(NOW(), '%Y-%m-%d'), interval 1 day)  THEN  1 = 1
		  when o.`order_time` = date_add(date_format(NOW(), '%Y-%m-%d'), interval 1 day) and  date_format(NOW(), '%H:%i') <![CDATA[ < ]]> ( SELECT GROUP_CONCAT(t.option_value,':00') FROM t_dictionary t where t.option_code = 'ChangePriceFinalHour' ) then 1 = 1
		end
	</select>


	<select id="getXdProposedOrderSettingEntry"  resultType="com.pinshang.qingyun.order.mapper.entry.order.XdProposedOrderSettingEntry">
		select
		  t.sale_ratio,
		  t.stock_ratio,
		  t.defective_ratio,
		  t.loss1_ratio,
		  t.loss2_ratio,
		  t.day1,
		  t.day2,
		  t.day3,
		  t.day4,
		  t.day5,
		  t.day6,
		  t.day7
		 FROM  t_xd_proposed_order_setting t

	</select>


	<select id="queryOrderYearEntryList"  resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderYearEntry">
		 SELECT
			 md.store_id storeId,
			 xca.commodity_id commodityId,
			 COUNT(xca.commodity_id) quantity

	     FROM t_xs_commodity_appointment xca
		 INNER  JOIN t_commodity t ON t.id = xca.commodity_id
		 LEFT JOIN t_md_shop md ON md.id = xca.shop_id
		  WHERE xca.`status` = 0
		  AND xca.appoint_time BETWEEN #{beginTime} AND #{endTime}
		 AND t.commodity_code in(0035412,0035413)
		GROUP BY xca.shop_id,xca.commodity_id

	</select>


	<select id="queryUseGiftModelCondition" resultType="com.pinshang.qingyun.order.model.gift.GiftModelCondition">
		select 	c.id,
			c.gift_model_id,
			c.condition_type,
			c.condition_value,
			c.status,
			c.remark
		from `t_gift_model_condition` c
		where c.`gift_model_id` =#{giftModelId} and c.condition_value &lt;= #{applyConditionValue}
		order by c.condition_value desc,c.id desc
		limit 1
	</select>

	<select id="queryOrderItem4Copy" parameterType="java.lang.Long" resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderItemEntry">

			select
				l.commodity_id commodityId,
				l.total_price totalPrice,
				l.commodity_price commodityPrice,
				l.type AS commodityType,
				l.`commodity_num` commodityNum
			from
				   `t_order_list` l
			where l.order_id =#{orderId} and l.type = 1
	</select>
	<select id="queryOrder4App" resultType="com.pinshang.qingyun.order.dto.xda.XdaOrder4AppODTO">

			SELECT
		        tor.id as orderId,
				tor.order_code orderCode,
				tor.order_amount summation,
				tor.real_total_price AS realAmount,
				case tor.order_status when 2
				then tor.order_status
				else tor.process_status
				end processStatus,
				tor.order_status orderStatus,
				tor.order_time orderTime,
				tor.create_time orderCreateTime,
				count(tol.commodity_id) varietySum
			FROM
				t_order tor
			INNER JOIN t_order_list tol ON tor.id = tol.order_id AND tol.type = 1
			where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
			and tor.order_status in (0,2) and tor.order_type = 8
				<if test="null != param.storeId">
					and	tor.store_id = #{param.storeId}
				</if>
				<if test="null != param.orderCode and '' != param.orderCode">
					and	tor.order_code = #{param.orderCode}
				</if>
				<if test="null != param.processStatus and '' != param.processStatus">
					and	tor.process_status = #{param.processStatus} and tor.order_status = 0
				</if>
			GROUP BY tor.id
			order by tor.order_time desc,tor.create_time desc
	</select>
	<select id="queryOrder4AppV2" resultType="com.pinshang.qingyun.order.dto.xda.XdaOrder4AppODTO">

		SELECT
		tor.id as orderId,
		tor.order_code orderCode,
		tor.order_amount summation,
		tor.real_total_price AS realAmount,
		case tor.order_status when 2
		then tor.order_status
		else tor.process_status
		end processStatus,
		tor.order_status orderStatus,
		tor.order_time orderTime,
		tor.create_time orderCreateTime,
		count(tol.commodity_id) varietySum
		FROM
		t_order tor
		INNER JOIN t_order_list tol ON tor.id = tol.order_id AND tol.type in(1,2,5)
		where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
		and tor.order_status in (0,2) and tor.order_type = 8
		<if test="null != param.storeId">
			and	tor.store_id = #{param.storeId}
		</if>
		<if test="null != param.orderCode and '' != param.orderCode">
			and	tor.order_code = #{param.orderCode}
		</if>
		<if test="null != param.processStatus and '' != param.processStatus">
			and	tor.process_status = #{param.processStatus} and tor.order_status = 0
		</if>
		GROUP BY tor.id
		order by tor.order_time desc,tor.create_time desc
	</select>
	<select id="queryOrderDetailByCodeV2" resultType="com.pinshang.qingyun.order.dto.xda.v2.XdaOrderAppV2ODTO">
		SELECT
		tor.id as orderId,
		tor.order_code AS orderCode, -- 订单编码
		tor.order_amount AS summation, -- 实付金额
		tor.real_total_price AS realAmount, -- 实发总金额
		case tor.order_status when 2
		then tor.order_status
		else tor.process_status
		end processStatus,
		tor.order_status AS orderStatus,
		tor.order_time AS orderTime,
		tor.create_time AS orderCreateTime, -- 下单时间
		sum(original_total_price) AS totalOriginPrice, -- 原价总和
		tor.order_type AS orderType,
		count(tol.commodity_id) AS varietySum
		FROM
		t_order tor
		INNER JOIN t_order_list tol ON tor.id = tol.order_id AND tol.type in(1,2,5)
		where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
		and tor.order_status in (0,2)
		<if test="null != param.storeId">
			and	tor.store_id = #{param.storeId}
		</if>
		<if test="null != param.orderCode and '' != param.orderCode">
			and	tor.order_code = #{param.orderCode}
		</if>
		<if test="null != param.processStatus and '' != param.processStatus">
			and	tor.process_status = #{param.processStatus} and tor.order_status = 0
		</if>
		GROUP BY tor.id
		order by tor.order_time desc,tor.create_time desc
	</select>
	<select id="queryOrder4PfApp" resultType="com.pinshang.qingyun.order.dto.pf.PfOrder4AppODTO">

			SELECT
		        tor.id as orderId,
				tor.order_code orderCode,
				tor.order_amount summation,
				tor.real_total_price AS realAmount,
				case tor.order_status when 2
				then tor.order_status
				else tor.process_status
				end processStatus,
				tor.order_status orderStatus,
				tor.order_time orderTime,
				tor.create_time orderCreateTime,
				tor.freight_amount freightAmount,
				count(tol.commodity_id) varietySum
			FROM
				t_order tor
			INNER JOIN t_order_list tol ON tor.id = tol.order_id AND tol.type = 1
			where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
			and tor.order_status in (0,2) and tor.order_type = 9
				<if test="null != param.storeId">
					and	tor.store_id = #{param.storeId}
				</if>
				<if test="null != param.orderCode and '' != param.orderCode">
					and	tor.order_code = #{param.orderCode}
				</if>
				<if test="null != param.processStatus and '' != param.processStatus">
					and	tor.process_status = #{param.processStatus} and tor.order_status = 0
				</if>
			GROUP BY tor.id
			order by tor.order_time desc,tor.create_time desc
	</select>
	
	<select id="queryOrderByOrderTime"  resultType="com.pinshang.qingyun.order.model.order.Order">
		select
            o.`id`,
            o.`order_time`,
            o.`order_code`,
            o.`order_amount`,
            o.`order_status`,
            o.store_id,
            o.total_amount,
            o.final_amount,
            o.create_id,
            o.create_time
        from `t_order` o
        where o.`store_id` = #{storeId}
        and o.order_status = 0
        and o.order_time = #{orderTime}
		and o.order_type = 8
	</select>

	<select id="findOrderBySubOrderId" parameterType="Long" resultType="com.pinshang.qingyun.order.vo.order.OrderChangePriceVo">
		SELECT
		  	DISTINCT
			tso.id as sub_order_id,
			tor.order_time,
			tor.change_price_status
		FROM t_sub_order tso
		LEFT JOIN t_order tor ON tso.order_id = tor.id
		WHERE tso.id IN <foreach collection="subOrderIds" item="id" open="(" separator="," close=")">#{id}</foreach>
	</select>


	<select id="queryPrintOrderInfo"  resultType="com.pinshang.qingyun.order.dto.XdReceiveDocCommodityODTO">
		select
			md.shop_code,
			md.shop_name,
			c.commodity_code,
			c.commodity_name,
			c.commodity_spec,
			sum(soi.quantity) quantity,
			sum(soi.real_delivery_quantity) realDeliveryQuantity
		from `t_order` o
		 INNER join `t_sub_order` so on so.`order_id` = o.`id`
		 INNER join t_sub_order_item soi on soi.sub_order_id = so.id
		 left join t_md_shop md on o.store_id = md.store_id
		 left join t_commodity c on c.id = soi.commodity_id
		where  o.order_time = #{orderTime} and o.`store_id` = #{storeId}
		  and o.order_status = 0
		 GROUP BY  soi.commodity_id
         order by c.commodity_first_id,c.commodity_second_id
	</select>


	<select id="queryPrintOrderCodeList"  resultType="java.lang.String">
		select
			DISTINCT o.order_code
		from `t_order` o
		 INNER join `t_sub_order` so on so.`order_id` = o.`id`
		 INNER join t_sub_order_item soi on soi.sub_order_id = so.id
		where  o.order_time = #{orderTime} and o.`store_id` = #{storeId}
		  and o.order_status = 0
	</select>

    <select id="getExportLastMonthLineGroupList"
            resultType="com.pinshang.qingyun.order.bo.ExportLastMonthLineGroupBO">
		select #{month} as month,d.option_name as lineGroupName,sum(o.final_amount) as orderAllAmount
		from t_order o
				 inner join t_store s on o.store_id = s.id  and o.order_status = 0
				 inner join t_distribution_line dl on dl.id = s.store_line_id
				 inner join t_dictionary d  on d.id = dl.delivery_time_id
		where o.order_time between #{startOrderTime} and #{endOrderTime}
		group by dl.delivery_time_id ;
	</select>


	<select id="getCommodityDeliveryQuantity"  resultType="java.math.BigDecimal">
		select
			sum(soi.real_delivery_quantity)
		from `t_order` o
		 INNER join `t_sub_order` so on so.`order_id` = o.`id`
		 INNER join t_sub_order_item soi on soi.sub_order_id = so.id
		 INNER JOIN t_md_shop md on md.store_id = o.store_id
		where  o.order_time = #{orderTime}
         and md.id = #{shopId} and soi.commodity_id = #{commodityId}
		<if test="stallId != null">
			and	o.stall_id = #{stallId}
		</if>
	</select>

	<select id="getCommodityDeliveryQuantityList"  resultType="com.pinshang.qingyun.order.mapper.entry.order.SaleReturnItemEntry">
		select
		    soi.commodity_id,
			ifnull(sum(soi.real_delivery_quantity),0) deliveryQuantity
		from `t_order` o
			 INNER join `t_sub_order` so on so.`order_id` = o.`id`
			 INNER join t_sub_order_item soi on soi.sub_order_id = so.id
			 INNER JOIN t_md_shop md on md.store_id = o.store_id
		where  o.order_time = #{orderTime}
		  and md.id = #{shopId}
		  and soi.commodity_id  IN
		  <foreach collection="commodityIdList" item="id" open="(" separator="," close=")">
		      #{id}
		  </foreach>

		<if test="stallId != null">
			and	o.stall_id = #{stallId}
		</if>
		 group by soi.commodity_id

	</select>


    <select id="getSubOrderId" resultType="java.lang.Long">
		select so.id from t_order o inner join t_sub_order so on o.id = so.order_id
									inner join t_sub_order_item soi on soi.sub_order_id = so.id
		<where>
		      <if test="orderTime != null and orderTime != ''">
				and  o.order_time = #{orderTime}
			  </if>
			  <if test="orderStartTime != null and orderEndTime != null and orderStartTime != '' and orderEndTime != ''">
				and o.order_time between #{orderStartTime} and #{orderEndTime}
			  </if>
			   and so.status =1 and soi.real_delivery_quantity is null
		</where>
	</select>


	<!--送货单列表查询-->
	<select id="findDeliveryBillPageInfoByParams" resultType="com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryBillEntry">
		SELECT
		o.id AS orderId,
		o.order_code,
		o.order_time,
		o.store_id,
		s.store_code,
		s.store_name,
		s.store_line_id,
		dl.line_name as LineName,
		s.deliveryman_id,
		om.delivery_man_name deliveryManName
		FROM
		t_order o
		LEFT JOIN t_order_mirror om ON o.id = om.order_id
		LEFT JOIN t_store s ON s.id = o.store_id
		LEFT JOIN t_distribution_line dl ON dl.id = s.store_line_id
		LEFT JOIN t_employee_user eu ON eu.id = s.deliveryman_id
		<where>
			and order_status=0
			<if test="orderStartTime != null and orderEndTime != null and orderStartTime != '' and orderEndTime != ''">
				and o.order_time between #{orderStartTime} and #{orderEndTime}
			</if>
			<if test="null != storeTypeId" >
				and s.store_type_id = #{storeTypeId}
			</if>
			<if test="null != lineId" >
				and s.store_line_id = #{lineId}
			</if>
			<if test="null != storeId" >
				and o.store_id = #{storeId}
			</if>
			<if test="null != orderCode and orderCode != '' " >
				and o.order_code = #{orderCode}
			</if>
			<if test="null != deliverymanId" >
				and s.deliveryman_id = #{deliverymanId}
			</if>
		</where>

	</select>

	<select id="selectPrintViewById" resultType="com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryOrderStkPrintEntry">
		SELECT
			s.store_code storeCode,
			s.store_name storeName,
			o.order_code orderNo,
			o.order_time orderTime,
			om.delivery_man_name deliverymanName,
			u.employee_name createName,
			d.line_name storeLineName
		FROM
			t_order o
		LEFT JOIN t_order_mirror om ON o.id = om.order_id
		LEFT JOIN t_store s ON o.store_id = s.id
		LEFT JOIN t_employee_user u ON u.user_id = o.create_id
		LEFT JOIN t_distribution_line d ON d.id = s.store_line_id
		where o.id=#{id}
	</select>



	<select id="selectItemPrintViewByOrderId" resultType="com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryOrderItemStkPrintEntry">
		SELECT
			cate.cate_name cateName,
			c.commodity_code commodityCode,
			c.bar_code barCode,
			c.commodity_spec spec,
			d.option_name unit,
			o.real_quantity stkNumber,
			o.commodity_num number,
			c.commodity_name productName,
			(o.commodity_num-o.real_quantity) subNumber
		FROM
			t_order_list_gift o LEFT JOIN t_commodity c on o.commodity_id = c.id
		LEFT JOIN t_category cate on c.commodity_first_id = cate.id
		LEFT JOIN t_dictionary d on c.commodity_unit_id = d.id
		where o.order_id=#{orderId}
	</select>


	<delete id="deleteShopOrderedQuantity" >
		delete from t_md_shop_ordered_quantity
		where  order_time = #{orderTime} and shop_id = #{shopId}
	</delete>

	<insert id="insertShopOrderedQuantity">
		Insert into t_md_shop_ordered_quantity(shop_id,commodity_id,order_time,quantity,
											   create_id,create_time)

		SELECT
			md.id shopId,
			soi.commodity_id,
			o.order_time,
			sum(soi.quantity),
			-1,
			NOW()
		FROM t_order o
				 inner JOIN t_sub_order so on so.order_id = o.id
				 inner JOIN t_sub_order_item soi on soi.sub_order_id = so.id
				 INNER JOIN t_md_shop md on md.store_id = o.store_id
				 LEFT JOIN t_md_receive_order ro on ro.sub_order_id = soi.sub_order_id
		WHERE o.order_status = 0 and so.status != 2 and ro.`status` = 0
		  and o.order_time = #{orderTime}
		  and md.id = #{shopId}
		 and o.order_type not in
		<foreach collection="orderTypeList" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		GROUP BY soi.commodity_id
	</insert>

	<select id="queryOrderDetailByOrderCode"  resultType="com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO">
		SELECT
			md.id shopId,
			soi.commodity_id,
			o.order_time,
			sum(soi.quantity) quantity
		FROM t_order o
		inner JOIN t_sub_order so on so.order_id = o.id
		inner JOIN t_sub_order_item soi on soi.sub_order_id = so.id
		INNER JOIN t_md_shop md on md.store_id = o.store_id
		LEFT JOIN t_md_receive_order ro on ro.sub_order_id = soi.sub_order_id
		WHERE o.order_code = #{orderCode}
		and o.order_type not in
		<foreach collection="orderTypeList" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		GROUP by soi.commodity_id
	</select>

	<select id="queryShopOrderedQuantityList"  resultType="com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO">
		SELECT
		    t.id,
			t.shop_id,
			t.commodity_id,
			t.order_time,
			t.quantity
		FROM t_md_shop_ordered_quantity t
		WHERE  t.shop_id = #{shopId} and t.order_time = #{orderTime}
		and t.commodity_id  in
		<foreach collection="commodityIdList" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</select>

	<insert id="batchInsertShopOrderQuantity" >
		INSERT INTO t_md_shop_ordered_quantity (shop_id, commodity_id, order_time, quantity, create_id, create_time)
		VALUES
		<foreach collection="insertList" item="item" index="index" separator=",">
			(#{item.shopId}, #{item.commodityId}, #{item.orderTime}, #{item.quantity},-1, now())
		</foreach>
	</insert>
	<update id="batchUpdateShopOrderQuantity">
		<foreach collection="updateList" item="item" index="index" separator=";" open="" close="">
			UPDATE t_md_shop_ordered_quantity
			SET
			 quantity = #{item.quantity}
			WHERE
			id = #{item.id}
		</foreach>
	</update>



	<select id="selectShopOrderQuantity"  resultType="com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO">
		select
			t.commodity_id,
			sum(t.quantity) quantity
		from t_md_shop_ordered_quantity t
		where  t.order_time > #{orderTime}
		and t.shop_id = #{shopId}
		and t.commodity_id  IN
		<foreach collection="commodityIdList" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
		group by t.commodity_id
	</select>

	<select id="selectShopCommodityVarietyTotal" resultType="com.pinshang.qingyun.order.dto.ShopCountStockPageODTO"
			parameterType="com.pinshang.qingyun.order.dto.ShopCountStockPageIDTO">
		SELECT
		shopName,
		shopId,
		storeId,
		orderTime,
		COUNT(commodity_id) AS varietyTotal
		FROM
		(
		SELECT
		ms.`shop_name` AS shopName,
		ms.`id` AS shopId,
		o.`store_id` AS storeId,
		o.`order_time` AS orderTime,
		soi.`commodity_id`
		FROM
		t_sub_order_item soi
		INNER JOIN t_sub_order so
		ON so.`id` = soi.`sub_order_id`
		INNER JOIN t_order o
		ON o.`id` = so.`order_id`
		LEFT JOIN t_md_shop ms
		ON ms.store_id = o.store_id
		WHERE o.`order_time` >= #{beginTime}
		AND o.`order_time` &lt; #{endTime}
		<if test="null != shopId">
		AND ms.`id` = #{shopId}
		</if>
		<if test=" null == shopId and null != shopIdList and shopIdList.size() > 0">
		AND ms.`id` IN
			<foreach collection="shopIdList" separator="," open="(" close=")" item="item">
				#{item}
			</foreach>
		</if>
		AND o.order_status = 0
		AND (soi.real_delivery_quantity IS NOT NULL AND soi.real_delivery_quantity > 0)
		GROUP BY
		ms.`id`,
		o.`order_time`,
		soi.`commodity_id`
		) a
		GROUP BY shopId,orderTime
		ORDER BY orderTime DESC, shopId DESC
	</select>

	<select id="selectShopCommodityVarietyDetail" resultType="com.pinshang.qingyun.order.dto.ShopCountStockDetailItemODTO"
			parameterType="com.pinshang.qingyun.order.dto.ShopCountStockDetailIDTO">
		SELECT
		ms.`id` AS shopId,
		o.`order_time` AS orderTime,
		soi.`commodity_id` commodityId,
		IFNULL(sum(soi.`real_delivery_quantity`), 0) AS varietyTotal
		FROM
		t_sub_order_item soi
		INNER JOIN t_sub_order so
		ON so.`id` = soi.`sub_order_id`
		INNER JOIN t_order o
		ON o.`id` = so.`order_id`
		LEFT JOIN t_md_shop ms
		ON ms.store_id = o.store_id
		WHERE o.`order_time` = #{orderTime}
		AND ms.`id` = #{shopId}
		<if test="null != commodityId">
			AND soi.`commodity_id` = #{commodityId}
		</if>
		<if test="barCode != null and barCode !='' ">
			and soi.commodity_id = (SELECT  commodity_id FROM t_commodity_bar_code WHERE  bar_code = #{barCode})
		</if>
		AND o.order_status = 0
		GROUP BY
		ms.`id`,
		o.`order_time`,
		soi.`commodity_id`
		HAVING SUM(soi.`real_delivery_quantity`) > 0
		ORDER BY soi.`commodity_id`
	</select>

	<select id="queryShopOrderedQuantity" resultType="com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO">
		select
			t.shop_id,
			t.commodity_id,
			t.quantity
		from t_md_shop_ordered_quantity t
		where  t.order_time = #{orderTime}
		and t.commodity_id  IN
		<foreach collection="commodityIdList" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>


	<delete id="deleteCommodityFreshOrders" >
		delete from t_commodity_fresh_order
		where create_time  between CONCAT(#{orderTime},' 00:00:00') and CONCAT(#{orderTime},' 23:59:59')
	</delete>

	<insert id="batchInsertCommodityFreshOrders">
		INSERT INTO t_commodity_fresh_order (shop_id, commodity_id, quantity, ordered_status, create_id, create_time, update_id, update_time)

		SELECT
			md.id shopId,
			soi.commodity_id,
			sum(soi.quantity),
		    0,
			-1,NOW(),-1,NOW()
		FROM t_order o
				 inner JOIN t_sub_order so on so.order_id = o.id
				 inner JOIN t_sub_order_item soi on soi.sub_order_id = so.id
				 INNER JOIN t_md_shop md on md.store_id = o.store_id
		WHERE o.order_status = 0 and so.status != 2 and md.shop_type = 5
		  and o.create_time  between CONCAT(#{orderTime},' 00:00:00') and CONCAT(#{orderTime},' 23:59:59')
		AND soi.commodity_id in
		<foreach collection="commodityIdList" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		GROUP BY md.id,soi.commodity_id
	</insert>

	<select id="queryCommodityFreshList" resultType="com.pinshang.qingyun.order.dto.CommodityFreshOrderODTO">
		select
			DISTINCT
		    md.store_id,
			t.shop_id,
			t.commodity_id,
			t.quantity
		from t_commodity_fresh_order t
		left join t_md_shop md on md.id = t.shop_id
		left join t_commodity tc on tc.id = t.commodity_id
		where  t.ordered_status = 0 and tc.status = 1 and tc.commodity_state = 1
		and md.shop_status in (1,2)
		and t.create_time between CONCAT(#{orderTime},' 00:00:00') and CONCAT(#{orderTime},' 23:59:59')
	</select>

	<select id="queryOrderedFreshList" resultType="com.pinshang.qingyun.order.dto.CommodityFreshOrderODTO">
		SELECT
		    distinct
			md.id shopId,
			soi.commodity_id
		FROM t_order o
		inner JOIN t_sub_order so on so.order_id = o.id
		inner JOIN t_sub_order_item soi on soi.sub_order_id = so.id
		INNER JOIN t_md_shop md on md.store_id = o.store_id
		WHERE o.order_status = 0 and so.status != 2 and md.shop_type = 5
		and o.create_time between CONCAT(#{orderTime},' 00:00:00') and CONCAT(#{orderTime},' 23:59:59')
		AND soi.commodity_id in
		<foreach collection="commodityIdList" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</select>
	<select id="queryOrderXdaApp" resultType="com.pinshang.qingyun.order.dto.xda.v2.XdaOrderAppV2ODTO">
		SELECT
		tor.id as orderId,
		tor.order_code orderCode,
		tor.order_amount summation,
		tor.real_total_price AS realAmount,
		case tor.order_status when 2
		then tor.order_status
		else tor.process_status
		end processStatus,
		tor.order_status orderStatus,
		tor.order_time orderTime,
		tor.create_time orderCreateTime,
		tor.order_type AS orderType,
		count(tol.commodity_id) varietySum
		FROM
		t_order tor
		INNER JOIN t_order_list tol ON tor.id = tol.order_id AND tol.type in(1,2,5)
		where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
		and tor.order_status in (0,2)
		<if test="null != param.storeId">
			and	tor.store_id = #{param.storeId}
		</if>
		<if test="null != param.orderCode and '' != param.orderCode">
			and	tor.order_code = #{param.orderCode}
		</if>
		<if test="null != param.processStatus and '' != param.processStatus">
			and	tor.process_status = #{param.processStatus} and tor.order_status = 0
		</if>
		GROUP BY tor.id
		order by tor.order_time desc,tor.create_time desc

	</select>
	<select id="queryOrderItemCopyV2"
			resultType="com.pinshang.qingyun.order.mapper.entry.order.V2.OrderItemV2Entry">
		select
			l.commodity_id commodityId,
			l.total_price totalPrice,
			l.commodity_price commodityPrice,
			l.type AS commodityType,
			l.`commodity_num` commodityNum
		from
			`t_order_list` l
		where l.order_id =#{orderId} and l.type in(1,5) and l.comb_type in (1,2)

	</select>
	<select id="queryOrderXdaAppV3" resultType="com.pinshang.qingyun.order.dto.xda.v3.XdaOrderAppV3ODTO">
		SELECT
		tor.id as orderId,
		tor.order_code orderCode,
		tor.order_amount summation,
		tor.real_total_price AS realAmount,
		case tor.order_status when 2
		then tor.order_status
		else tor.process_status
		end processStatus,
		tor.order_status orderStatus,
		tor.order_time orderTime,
		tor.create_time orderCreateTime,
		tor.order_type AS orderType,
		count(DISTINCT tol.commodity_id) varietySum
		FROM
		t_order tor
		INNER JOIN t_order_list tol ON tor.id = tol.order_id AND tol.type in(1,2,5)
		where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
		and tor.order_status in (0,2)
		<if test="null != param.storeId">
			and	tor.store_id = #{param.storeId}
		</if>
		<if test="null != param.orderCode and '' != param.orderCode">
			and	tor.order_code = #{param.orderCode}
		</if>
		<if test="null != param.processStatus and '' != param.processStatus">
			and	tor.process_status = #{param.processStatus} and tor.order_status = 0
		</if>
		GROUP BY tor.id
		order by tor.order_time desc,tor.create_time desc

	</select>
	<select id="queryOrderXdaAppV4" resultType="com.pinshang.qingyun.order.dto.xda.v4.XdaOrderAppV4ODTO">
		SELECT
			tor.id as orderId,
			tor.order_code orderCode,
			tor.order_amount summation,
			tor.real_total_price AS realAmount,
			tor.order_duration_time AS orderDurationTime,
			case tor.order_status when 2
			then tor.order_status
			else tor.process_status
			end processStatus,
			tor.order_status orderStatus,
			tor.order_time orderTime,
			tor.create_time orderCreateTime,
			tor.order_type AS orderType,
			count(DISTINCT tol.commodity_id) varietySum,
			tor.delivery_time_range deliveryTimeRange,
			tor.business_type businessType
		FROM
		t_order tor
		INNER JOIN t_order_list tol ON tor.id = tol.order_id AND tol.type in(1,2,5)
		where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
		and tor.order_status in (0,2)
		and tol.comb_type in (1,2)
		<if test="null != param.storeId">
			and	tor.store_id = #{param.storeId}
		</if>
		<if test="null != param.orderCode and '' != param.orderCode">
			and	tor.order_code = #{param.orderCode}
		</if>
		<if test="null != param.processStatus and '' != param.processStatus">
			and	tor.process_status = #{param.processStatus} and tor.order_status = 0
		</if>
		GROUP BY tor.id
		order by tor.order_time desc,tor.create_time desc

	</select>
	<select id="queryOrderDetailByCodeV3" resultType="com.pinshang.qingyun.order.dto.xda.v3.XdaOrderAppV3ODTO">
		SELECT
		tor.id as orderId,
		tor.order_code AS orderCode, -- 订单编码
		tor.order_amount AS summation, -- 实付金额
		tor.real_total_price AS realAmount, -- 实发总金额
		case tor.order_status when 2
		then tor.order_status
		else tor.process_status
		end processStatus,
		tor.order_status AS orderStatus,
		tor.order_time AS orderTime,
		tor.create_time AS orderCreateTime, -- 下单时间
		sum(original_total_price) AS totalOriginPrice, -- 原价总和
		tor.order_type AS orderType,
		count(distinct tol.commodity_id) AS varietySum
		FROM
		t_order tor
		INNER JOIN t_order_list tol ON tor.id = tol.order_id AND tol.type in(1,2,5)
		where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
		and tor.order_status in (0,2)
		<if test="null != param.storeId">
			and	tor.store_id = #{param.storeId}
		</if>
		<if test="null != param.orderCode and '' != param.orderCode">
			and	tor.order_code = #{param.orderCode}
		</if>
		<if test="null != param.processStatus and '' != param.processStatus">
			and	tor.process_status = #{param.processStatus} and tor.order_status = 0
		</if>
		GROUP BY tor.id
		order by tor.order_time desc,tor.create_time desc
	</select>
	<select id="queryOrderDetailByCodeV4" resultType="com.pinshang.qingyun.order.dto.xda.v4.XdaOrderAppV4ODTO">
		SELECT
			tor.id as orderId,
			tor.order_code AS orderCode, -- 订单编码
			tor.order_amount AS summation, -- 实付金额
			tor.real_total_price AS realAmount, -- 实发总金额
			tor.freight_amount AS freightAmount,
			tor.order_duration_time AS orderDurationTime,
			tor.business_type AS businessType,
			case tor.order_status when 2
			then tor.order_status
			else tor.process_status
			end processStatus,
			tor.order_status AS orderStatus,
			tor.order_time AS orderTime,
			tor.create_time AS orderCreateTime, -- 下单时间
			sum(original_total_price) AS totalOriginPrice, -- 原价总和
			tor.order_type AS orderType,
			count(distinct tol.commodity_id) AS varietySum,
			tor.order_remark remark,
			sum(coupon_discount_amount) AS couponAmount,
			tor.delivery_time_range as deliveryTimeRange
		FROM
		t_order tor
		INNER JOIN t_order_list tol ON tor.id = tol.order_id AND tol.type in(1,2,5)
		where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
		and tor.order_status in (0,2)
		and tol.comb_type in (1,2)
		<if test="null != param.storeId">
			and	tor.store_id = #{param.storeId}
		</if>
		<if test="null != param.orderCode and '' != param.orderCode">
			and	tor.order_code = #{param.orderCode}
		</if>
		<if test="null != param.processStatus and '' != param.processStatus">
			and	tor.process_status = #{param.processStatus} and tor.order_status = 0
		</if>
		GROUP BY tor.id
		order by tor.order_time desc,tor.create_time desc
	</select>
	<update id="updateCommodityFreshOrdered">
		update `t_commodity_fresh_order` set `ordered_status` = 1,update_time = now(),update_id = -1
		where shop_id = #{shopId}
		  and commodity_id in
		<foreach collection="commodityIdList" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		and create_time between CONCAT(#{orderTime},' 00:00:00') and CONCAT(#{orderTime},' 23:59:59')
	</update>

	<select id="queryOrderSyncToDcList"
			parameterType="com.pinshang.qingyun.order.vo.order.OrderSyncToDcReqVo"
			resultType="com.pinshang.qingyun.order.vo.order.OrderSyncToDcRespVo" >
		SELECT
			o.id,
			o.order_code,
			o.order_status,
			o.process_status
		FROM
			t_order o
		<where>
			<if test="orderCodeList != null and orderCodeList.size() > 0">
				AND o.order_code in
				<foreach collection="orderCodeList" open="(" close=")" separator="," item="item">
					#{item}
				</foreach>
			</if>
			<if test="startTime != '' and startTime != null and endTime != '' and endTime != null">
				AND o.order_time BETWEEN #{startTime} AND #{endTime}
			</if>
			<if test="deliveryBatch != null">
				AND o.delivery_batch = #{deliveryBatch}
			</if>
			<if test="orderIdList != null and orderIdList.size() > 0">
				AND o.id in
				<foreach collection="orderIdList" open="(" close=")" separator="," item="item">
					#{item}
				</foreach>
			</if>
			<if test="currentTime != null">
				AND o.create_time <![CDATA[ <= ]]> #{currentTime}
			</if>
			<if test="orderTime != '' and orderTime != null ">
				AND o.order_time = #{orderTime}
			</if>
			<if test="startCreateTime != null and startCreateTime != ''">
				AND o.create_time <![CDATA[ >= ]]> #{startCreateTime}
			</if>
			<if test="endCreateTime != null and endCreateTime != '' ">
				AND o.create_time <![CDATA[ <= ]]> #{endCreateTime}
			</if>
		</where>
	</select>
	<select id="queryOrderSyncToDcListV2" resultType="com.pinshang.qingyun.order.vo.order.OrderSyncToDcRespVo">
		SELECT
		DISTINCT so.order_id AS id,
		o.order_code,
		o.order_status,
		o.process_status
		FROM
		t_sub_order so
		INNER JOIN t_order o ON so.order_id=o.id
		<where>
			<if test="orderCodeList != null and orderCodeList.size() > 0">
				AND o.order_code in
				<foreach collection="orderCodeList" open="(" close=")" separator="," item="item">
					#{item}
				</foreach>
			</if>
			<if test="startTime != '' and startTime != null and endTime != '' and endTime != null">
				AND o.order_time BETWEEN #{startTime} AND #{endTime}
			</if>
			<if test="deliveryBatch != null">
				AND o.delivery_batch = #{deliveryBatch}
			</if>
			<if test="orderIdList != null and orderIdList.size() > 0">
				AND o.id in
				<foreach collection="orderIdList" open="(" close=")" separator="," item="item">
					#{item}
				</foreach>
			</if>
			<if test="currentTime != null">
				AND o.create_time <![CDATA[ <= ]]> #{currentTime}
			</if>
			<if test="orderTime != '' and orderTime != null ">
				AND o.order_time = #{orderTime}
			</if>
			<if test="startCreateTime != null and startCreateTime != ''">
				AND o.create_time <![CDATA[ >= ]]> #{startCreateTime}
			</if>
			<if test="endCreateTime != null and endCreateTime != '' ">
				AND o.create_time <![CDATA[ <= ]]> #{endCreateTime}
			</if>
		</where>
	</select>


	<select id="selectCountByOrderStatus" resultType="com.pinshang.qingyun.order.dto.tob.ToBOrderDataODTO">

		select
		t1.commodity_id as commodityId, t3.order_time as orderTime,
		SUM(CASE WHEN t3.order_status in (0, 2) THEN t1.quantity ELSE 0 END) AS realBTotalQuantity,
		SUM(CASE WHEN t3.order_status = 2 THEN t1.quantity ELSE 0 END) AS realBCancelQuantity,
		CEIL(SUM(CASE WHEN t3.order_status in (0, 2) THEN t1.quantity ELSE 0 END) / tc.commodity_package_spec) AS realBTotalNumber,
		CEIL(SUM(CASE WHEN t3.order_status = 2 THEN t1.quantity ELSE 0 END) / tc.commodity_package_spec) AS realBCancelNumber
		from t_sub_order_item t1
		left join t_sub_order t2 on t1.sub_order_id = t2.id
		left join t_order t3 on t2.order_id = t3.id
		left join t_commodity tc on t1.commodity_id = tc.id
		<where>
			<if test="orderStatus != null and orderStatus.size() > 0">
				and t3.order_status in
				<foreach collection="orderStatus" item="item" index="index" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="startOrderTime != null and startOrderTime != ''">
				<![CDATA[ AND t3.order_time >= #{startOrderTime,jdbcType=VARCHAR} ]]>
			</if>
			<if test="endOrderTime != null and endOrderTime != ''">
				<![CDATA[ AND t3.order_time <= #{endOrderTime,jdbcType=VARCHAR} ]]>
			</if>
			<if test="orderTimes != null and orderTimes.size() > 0">
				and t3.order_time in
				<foreach collection="orderTimes" item="item" index="index" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="commodityIds != null and commodityIds.size() > 0">
				and t1.commodity_id in
				<foreach collection="commodityIds" item="item" index="index" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		group by t1.commodity_id, t3.order_time
	</select>


	<select id="queryXdaSpecialPriceOrderedQuantity" resultType="com.pinshang.qingyun.order.dto.xda.CreOrderItemDTO">
		select
		    ol.commodity_id,
		    sum(ol.commodity_num) quantity
		from t_order o
		left join t_order_list ol on ol.order_id = o.id
		where o.order_time = #{orderTime} and o.order_status = 0
		and o.store_id =  #{storeId}
		and o.order_type = 8
		and ol.special_price > 0
		<if test="commodityIdList != null and commodityIdList.size() > 0">
			AND ol.commodity_id in
			<foreach collection="commodityIdList" open="(" close=")" separator="," item="item">
				#{item}
			</foreach>
		</if>
		group by ol.commodity_id

	</select>
	<update id="updateSettleOrderDateByDate">
		UPDATE
		    t_order o,
			t_store_type_desc std
		SET
			o.settle_order_time = CASE
									WHEN std.settle_bill_calc_type = 0 AND std.settle_bill_calc_type = 4 THEN o.order_time
									WHEN std.settle_bill_calc_type = 2 AND std.settle_bill_calc_type = 3 THEN o.real_order_time
									WHEN std.settle_bill_calc_type = 1 AND o.order_time &gt; o.real_order_time and o.real_sub_order_done_status=1 THEN o.real_order_time
									WHEN std.settle_bill_calc_type = 1 AND o.order_time &lt; o.real_order_time and o.real_sub_order_done_status=1 THEN o.order_time
									   ELSE
									   o.order_time
								   END
	   WHERE
	   		o.order_status=0
		   AND o.order_time =#{orderTime}
		   AND o.store_type_id = std.id
	</update>

	<update id="updateStoreTypeIdByDate">
		update t_order o,t_store s
		set o.store_type_id=s.store_type_id
		where o.store_id=s.id and o.store_type_id is null and o.order_time=#{orderTime}
	</update>


	<select id="countCurrentDayOrder" resultType="java.lang.Integer">
		select
		  count(1)
		from t_order o
		where o.create_time between #{beginTime} and #{endTime}
		and o.store_id =  #{storeId}
	</select>


    <select id="selectOrderByOrderIdList" resultType="com.pinshang.qingyun.order.mapper.entry.order.SubOrderInfoListEntry">
        SELECT
            o.id AS orderId,
            o.business_type
        FROM
            t_order o
        WHERE
        o.id IN
        <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")">
            #{orderId}
        </foreach>
        AND o.order_status =0
        AND (
            o.real_sub_order_done_status IS NULL
            OR o.real_sub_order_done_status != 1
        )
    </select>

    <select id="selectOrderIds" resultType="com.pinshang.qingyun.order.mapper.entry.order.SubOrderInfoListEntry">
        SELECT
            o.id AS orderId,
            o.business_type
        FROM
            t_order o
        WHERE
            o.update_time BETWEEN #{startTime} AND #{endTime}
        AND o.order_status =0
        AND (
            o.real_sub_order_done_status IS NULL
            OR o.real_sub_order_done_status != 1
        )
    </select>
	<select id="syncOrderList" resultType="com.pinshang.qingyun.order.dto.sync.SyncOrderODTO">
		SELECT
			o.id AS orderId,
			o.order_code AS orderCode,
			o.order_time AS orderTime,
			o.order_type AS orderType,
			o.total_amount AS totalAmount,
			o.final_amount AS finalAmount,
			o.order_amount AS orderAmount,
			o.freight_amount AS freightAmount,
			o.logistics_center_id AS logisticsCenterId,
			o.business_type AS businessType,
			o.logistics_carrier_code AS logisticsCarrierCode,
			o.direct_status AS directStatus,
			o.delivery_time_range AS deliveryTimeRange,
			o.driver_id AS driverId,
			o.store_type_id AS storeTypeId,
			o.print_num AS printNum,
			o.print_type AS printType,
			o.order_remark AS orderRemark,
			o.process_status AS processStatus,
			o.order_duration_time AS orderDurationTime,
			o.real_total_price AS realTotalPrice,
			o.real_order_time AS realOrderTime,
			o.consignment_id AS consignmentId,
			o.cacel_reason_id AS cacelReasonId,
			o.presale_status AS presaleStatus,
			o.logistics_center AS logisticsCenter,
			o.stall_id AS stallId
		FROM
		t_order o
		WHERE
		o.update_time BETWEEN #{bTime} AND #{eTime}
		and o.order_type NOT IN (14,15)
		<if test="null != businessTypeList and businessTypeList.size > 0">
			AND o.business_type IN
			<foreach collection="businessTypeList" index="index" item="businessType" open="(" separator="," close=")">
				#{businessType}
			</foreach>
		</if>
	</select>
	<select id="syncOrderListByOrderCodeList" resultType="com.pinshang.qingyun.order.dto.sync.SyncOrderODTO">
		SELECT
		o.id AS orderId,
		o.company_id AS companyId,
		o.store_id AS storeId,
		o.delivery_batch AS deliveryBatch,
		o.mode_type AS modeType,
		o.order_status AS orderStatus,
		o.create_id AS createId,
		o.create_time AS createTime,
		o.update_id AS updateId,
		o.update_time AS updateTime,
		o.order_code AS orderCode,
		o.order_time AS orderTime,
		o.order_type AS orderType,
		o.total_amount AS totalAmount,
		o.final_amount AS finalAmount,
		o.order_amount AS orderAmount,
		o.freight_amount AS freightAmount,
		o.business_type AS businessType,
		o.logistics_carrier_code AS logisticsCarrierCode,
		o.direct_status AS directStatus,
		o.delivery_time_range AS deliveryTimeRange,
		o.driver_id AS driverId,
		o.store_type_id AS storeTypeId,
		o.print_num AS printNum,
		o.print_type AS printType,
		o.order_remark AS orderRemark,
		o.process_status AS processStatus,
		o.order_duration_time AS orderDurationTime,
		o.real_total_price AS realTotalPrice,
		o.real_order_time AS realOrderTime,
		o.consignment_id AS consignmentId,
		o.cacel_reason_id AS cacelReasonId,
		o.presale_status AS presaleStatus,
		o.logistics_center AS logisticsCenter,
		o.stall_id AS stallId
		FROM
		t_order o
		<where>
			<if test="null != orderCodeList and orderCodeList.size > 0">
				AND o.order_code IN
				<foreach collection="orderCodeList" index="index" item="orderCode" open="(" separator="," close=")">
					#{orderCode}
				</foreach>
			</if>
		</where>
	</select>
    <select id="countOrderNumberByStoreId" resultType="java.lang.Integer">
		select count(*) from t_order o
		where
		      o.order_status = 0
		  and o.store_id =#{storeId}
		  and o.order_time = #{orderTime}
	</select>
    <select id="findCommodityByOrderId" resultType="com.pinshang.qingyun.order.model.commodity.Commodity">
		select
			c.*,
			ppml.`commodity_price`,
			l.`comb_type` combType,
			l.`commodity_num` product_number ,
			l.`commodity_price` product_price,
			l.`remark` product_remark,
			l.`price_promotion_id`,
			dic.option_name  commodityUnitName,
			(case when cg.id is null then '否' else '是' end) as isRoundedGoods
		from  `t_order` o
				  inner join `t_order_list` l on o.`id` = l.`order_id`
				  inner join `t_store_settlement` s  on s.`store_id` = o.`store_id`
				  inner join `t_product_price_model` ppm  on ppm.`id` = s.`product_price_model_id`
				  inner join `t_product_price_model_list` ppml  on ppml.`product_price_model_id` = ppm.`id`
				  inner join `t_commodity` c  on ppml.`commodity_id` = c.`id` and l.`commodity_id` = c.`id`
				  left join t_commodity_freeze_group cg  on cg.commodity_id = c.id
				  left join t_dictionary dic on dic.id = c.commodity_unit_id
		where 1=1
		  and o.`id` = #{orderId}
		  and l.`type` =#{productType}
		<if test="orderTime !=null and orderTime !=''">
			and o.`order_time` = #{orderTime}
		</if>
		order by l.id
	</select>
	<select id="findStoreSearch" resultType="com.pinshang.qingyun.order.model.order.Order">
		SELECT
		o.id,
		o.order_code,
		o.store_id,
		o.create_time,
		o.order_remark,
		o.order_amount,
		o.order_status,
		o.order_time,
		s.store_code,
		s.store_name,
		CASE
		WHEN o.business_type = 10 then o.driver_id
		ELSE om.delivery_man_id
		END AS deliveryman_id,
		CASE
		WHEN o.business_type = 10 then du.employee_name
		ELSE om.delivery_man_name
		END AS deliveryman_name,
		om.supervision_id		AS supervisor_id,
		om.supervision_name		AS supervisor_name,
		o.create_id,
		ifnull(u.employee_name,s.store_name) AS createUserName,
		o.order_type,
		d.id as lineGroupId,
		d.option_name,
		s.business_type
		FROM
		t_order o
		LEFT JOIN t_store s ON o.store_id = s.id
		LEFT JOIN t_employee_user u ON u.user_id = o.create_id
		LEFT JOIN t_distribution_line dl on dl.id = s.store_line_id
		LEFT JOIN t_dictionary d on d.id = dl.delivery_time_id
		LEFT JOIN t_order_mirror om ON om.order_id = o.id
		LEFT JOIN t_employee_user du ON du.user_id = o.driver_id

		where
		1 = 1 and o.order_status in (0,1,2)
		<if test="storeId != null">
		and o.`store_id` = #{storeId}
		</if>
		<if test="storeCode != null and storeCode !='' ">
		and s.store_code LIKE concat('%',#{storeCode},'%')
		</if>
		<if test="storeName != null and storeName !='' ">
		and s.`store_name` like concat('%',#{storeName},'%')
		</if>
	  <choose>
		<when test="orderTimeStart != null and orderTimeStart !='' and orderTimeEnd!=null and orderTimeEnd != ''">
			and o.`order_time` between #{orderTimeStart} and #{orderTimeEnd}
		</when>
	   <when test="orderTimeStart != null and orderTimeStart !='' ">
		   and o.`order_time` &gt; #{orderTimeStart}
	   </when>
	  <when test="orderTimeEnd != null and orderTimeEnd !=''">
		  and o.`order_time` &lt; #{orderTimeEnd}
	  </when>
	  <otherwise></otherwise>
	  </choose>

		<if test="orderStatus != null">
		and o.`order_status` = #{orderStatus}
		</if>
		order by o.create_time desc


	</select>
    <select id="findOrderDeliveryFinishList"
            resultType="com.pinshang.qingyun.order.manage.delivery.dto.OrderDeliveryFinishDTO">
		SELECT
		o.id as orderId,
		o.store_id,
		o.order_code,
		o.store_type_id AS orderStoreTypeId,
		sto.store_type_id,
		o.order_type,
		o.order_time
		FROM
		t_order o
		LEFT JOIN t_store sto ON sto.id=o.store_id
		WHERE
		o.id IN
		<foreach collection="orderIds" item="id" separator="," open="(" close=")">
			#{id}
		</foreach>
		AND o.order_status=0
		AND o.real_sub_order_done_status=1
	</select>
	<select id="findPdaOrder" resultType="com.pinshang.qingyun.order.mapper.entry.pda.PdaOrderQueryEntry">
		select
		soi.commodity_id AS commodityId,
		soi.quantity AS commodityNum,
		soi.real_receive_quantity AS realQuantity
		from `t_order` o
		inner join `t_sub_order` so on so.`order_id` = o.`id`
		inner join `t_sub_order_item` soi on soi.sub_order_id = so.id
		inner join `t_md_shop` ms on o.store_id = ms.store_id
		inner join `t_md_receive_order` ro on ro.`sub_order_id` = so.`id`
		where  ms.`id` =#{shopId}
		and so.`logistics_model` in (1,2)
		and ro.`status` in (0,3)
	    and o.`order_time`  = #{orderTime}
		and soi.`commodity_id` = #{commodityId}
	</select>
	<select id="queryOrder" resultType="com.pinshang.qingyun.order.mapper.entry.job.StoreOrderAmountEntry">
		select o.store_id,sum(o.order_amount) as orderAmount,sum(o.freight_amount) as freightAmount from t_order o
		inner join t_store_settlement ss
		on o.store_id  = ss.store_id
		where ss.collect_status = #{collectStatus}
		and o.order_status = #{orderStatus}
		and o.create_time  between
		    CONCAT(#{dateStr},' 00:00:00') and CONCAT(#{dateStr},' 23:59:59')
		group by o.store_id
	</select>


	<select id="queryOrderCouponList" resultType="com.pinshang.qingyun.order.dto.xda.MtCouponDayStatisticsODTO">
		SELECT
			t.id orderId,
			t.order_amount usedTotalOrderAmount,
			ol.coupon_id couponId,
			ol.coupon_discount_amount discountTotalAmount
		FROM t_order t
			LEFT JOIN t_order_list ol on ol.order_id = t.id
		where t.create_time BETWEEN #{beginTime} and #{endTime}
		  and ol.coupon_id > 0 and t.order_status = 0
	</select>
    <select id="queryStoreOftenBuyProductDTO"
            resultType="com.pinshang.qingyun.order.dto.StoreOftenBuyProductDTO">
		SELECT
			l.commodity_id,
			sum(CEIL(l.commodity_num / IFNULL(c.commodity_package_spec,1))) totalNum
		from t_order t
				 LEFT JOIN t_order_list l on l.order_id = t.id
				 LEFT JOIN t_commodity c on c.id = l.commodity_id
		where t.order_status = 0
		  and t.store_id = #{storeId}
		  <if test = "beginDate !=null and beginDate != ''">
			  and t.create_time BETWEEN concat(#{beginDate},' 00:00:00') and concat(#{endDate},' 23:59:59')
		  </if>
		GROUP BY l.commodity_id
		order by totalNum desc
			limit 150;

	</select>


	<select id="queryOrderPrice" resultType="java.math.BigDecimal">
		SELECT
			ol.price
		FROM t_order t
			LEFT JOIN t_sub_order so on so.order_id = t.id
		    left join t_sub_order_item ol on ol.sub_order_id = so.id
		    left join t_md_receive_order ro on ro.sub_order_id = so.id
		where t.`order_time` = #{orderTime} and t.store_id = #{storeId}
		  and t.order_status = 0
		  and ol.commodity_id = #{commodityId}
		  and ol.price > 0
		  and ro.status = 3
		  limit 1
	</select>

    <select id="selectDirectDeliveryOrderList" resultType="com.pinshang.qingyun.order.dto.finance.DirectDeliveryOrderODTO">
        SELECT
            o.order_code AS businessCode,
            DATE_FORMAT(o.order_time, '%Y-%m-%d') AS businessDate,
          	o.order_time AS businessTime,
            o.store_id,
            o.stall_id,
            i.commodity_id,
            i.real_quantity AS quantity,
            i.real_total_price AS amount,
            i.id AS businessOrderItemId,
            #{vo.businessType} AS businessType
        FROM
            t_order o
                INNER JOIN t_order_list i ON i.order_id = o.id
        <where>
            o.order_time BETWEEN STR_TO_DATE(#{vo.businessDate},'%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(#{vo.businessDate},'%Y-%m-%d %H:%i:%s')
        <if test="vo.businessOrderItemId!=null">
            AND i.id > #{vo.businessOrderItemId}
        </if>
          AND o.order_status = 0
          AND o.direct_status = 1
        </where>
        ORDER BY i.id ASC
        LIMIT #{vo.limitQuantity}
    </select>


	<select id="selectOrderListByBusinessTypeIsNullOrLogisticsCenterIsNull"
			resultType="com.pinshang.qingyun.order.dto.sync.SyncOrderODTO">
		SELECT
			o.id AS orderId,
			o.logistics_center_id AS logisticsCenterId,
			o.business_type AS businessType
		FROM
			t_order o
		WHERE
			o.update_time BETWEEN #{beginTime} AND #{endTime}
		  AND o.order_type NOT IN (14,15)
		  AND (o.business_type IS NOT NULL OR o.logistics_center_id IS NOT NULL)
	</select>


	<update id="updateCompleteOrderByParams" >
		update `t_order` set `process_status` = 19, update_time = NOW()
		where `order_time` = #{dateTime}
		  and order_status = 0
		  and process_status = 15
		  and (business_type is null or business_type not in(10, 13, 14, 15) )
	</update>



	<select id="queryOrderByOrderCode" resultType="com.pinshang.qingyun.order.dto.OrderInfoODTO">
		SELECT
			t.store_id,
			t.order_time,
			t.stall_id,
			t.delivery_batch
		from t_order t
		where t.order_code = #{orderCode}

	</select>

</mapper>
