<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SubOrderMapper">

    <sql id="querySO2DOEntry">
        SELECT
        o.store_id,
        so.id as subOrderId,
        so.enterprise_id,
        so.warehouse_id,
        so.logistics_model,
        so.variety_total,
        so.supplier_id,
        o.id AS referOrderId,
        o.delivery_batch,
        o.order_time,
        o.order_code AS referOrderCode,
        ms.shop_type,
        ms.id AS shopId,
        o.order_type,
        s.store_type_id,
        so.presale_status AS presaleStatus,
        so.stock_type AS stockType,
        IFNULL(o.business_type,0) AS businessType,
        o.stall_id
        FROM
        t_sub_order so
        INNER JOIN t_order o ON o.id = so.order_id
        <if test=" businessType == null or businessType == 0 or businessType == 14">
            AND IF(o.order_duration_time IS NULL, 1, o.order_duration_time <![CDATA[ <= ]]> NOW())
        </if>
        INNER JOIN t_store s ON o.store_id = s.id
        LEFT JOIN t_md_shop ms ON s.id = ms.store_id
    </sql>
    <!-- 子单转DO：查询子单要生成DO的数据 -->
    <select id="listSubOrder2DeliveryOrder"
            resultType="com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderListEntry">
        <include refid="querySO2DOEntry"/>
        WHERE
        o.id IN
        <foreach collection="orderIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND so.status =0
        <if test=" businessType == null or businessType == 0">
            AND (o.business_type is null OR o.business_type = 0)
        </if>
        <if test=" businessType != null and businessType != 0">
            AND o.business_type = #{businessType}
        </if>
        <if test=" warehouseId != null ">
            AND so.warehouse_id = #{warehouseId}
        </if>
    </select>
    <select id="listSubOrder2DeliveryOrderConfirm"
            resultType="com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderListEntry">
        <include refid="querySO2DOEntry"/>
        where so.id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND so.status = 1
        <if test=" businessType == null or businessType == 0">
            AND (o.business_type is null OR o.business_type = 0)
        </if>
        <if test=" businessType != null and businessType != 0">
            AND o.business_type = #{businessType}
        </if>
    </select>

    <!-- 子单转DO：查询要生成DO单明细的数据 -->
    <select id="listSubOrder2DeliveryOrderItem"
            resultType="com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderItemEntry">
        SELECT
        soi.commodity_id,
        soi.sub_order_id,
        soi.target_commodity_id,
        soi.convert_status,
        soi.source_ratio,
        soi.target_ratio,
        <choose>
            <when test="businessType != null and businessType==13">
                0 AS price,
                SUM(soi.quantity) as quantity,
                SUM(soi.target_quantity) AS targetQuantity
            </when>
            <otherwise>
                soi.price,
                soi.id AS orderItemId,
                soi.quantity,
                soi.target_quantity
            </otherwise>
        </choose>
        FROM
        t_sub_order_item soi
        INNER JOIN t_sub_order so ON soi.sub_order_id = so.id AND so. STATUS = #{subStatus}
        INNER JOIN t_order o ON so.order_id = o.id
        INNER JOIN t_store s ON o.store_id = s.id
        WHERE
        so.id IN
        <foreach collection="subOrderIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test=" businessType == null or businessType == 0 ">
            AND (o.business_type is null OR o.business_type = 0)
        </if>
        <if test=" businessType != null and  businessType != 13 and businessType != 0 ">
            AND o.business_type = #{businessType}
        </if>
        <if test=" businessType != null and businessType == 13">
            AND o.business_type = 13
            GROUP BY
            soi.commodity_id,
            soi.sub_order_id,
            soi.target_commodity_id,
            soi.convert_status,
            soi.source_ratio,
            soi.target_ratio
        </if>
    </select>

    <select id="queryUnDeliverySubOrderList"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.SubOrderListEntry">
        SELECT
        so.id,
        so.warehouse_id,
        so.logistics_model,
        so.variety_total,
        so.order_id,
        o.order_code,
        s.store_code,
        s.store_name,
        o.create_time,
        o.update_time,
        o.delivery_batch,
        o.order_time,
        o.logistics_center_id,
        o.logistics_center AS logisticsCenterName,
        o.order_type,
        IFNULL(o.business_type,0) AS deliveryOrderType,
        o.stall_id AS stallId
        <if test=" (businessType == null or businessType == 0) and (lineGroupId != null or (deliveryTime != '' and deliveryTime != null))">
            ,dt.end_time AS deliveryTime
        </if>
        FROM
        t_sub_order so
        INNER JOIN t_order o ON o.id = so.order_id
        <if test=" businessType == null or businessType == 0 or businessType == 14">
            AND IF(o.order_duration_time IS NULL,1,o.order_duration_time <![CDATA[ <= ]]> NOW())
        </if>
        INNER JOIN t_store s ON o.store_id = s.id
        <if test=" storeTypeId != null ">
            AND s.store_type_id = #{storeTypeId}
        </if>
        LEFT JOIN t_shift_store_type_id ssti ON ssti.store_id = s.id
        <if test=" (businessType == null or businessType == 0) and (lineGroupId != null or (deliveryTime != '' and deliveryTime != null))">
            LEFT JOIN t_distribution_line dl ON s.store_line_id = dl.id
            LEFT JOIN t_delivery_time dt ON dl.delivery_time_id = dt.line_group_id
        </if>
        WHERE
        o.order_time <![CDATA[ <= ]]>  date_add(DATE(now()), INTERVAL 1 DAY)
        <if test=" businessType == null or businessType == 0">
            AND (o.business_type is null OR o.business_type = 0)
            -- 判断客户是否是老客户切换成“鲜达”客户类型，
            -- 如果是，则需要送货日期为当前时间前31天内与鲜达实发开始时间的交集，
            -- 如果不是，则需送货日期为当前时间前31天内
            AND <![CDATA[ if(ssti.xda_real_delivery_begin_date IS NOT NULL AND ssti.xda_real_delivery_begin_date != '' AND s.store_type_id = #{xdaStoreTypeId},
                    o.order_time >= ssti.xda_real_delivery_begin_date AND o.order_time >= DATE_SUB(DATE(now()), INTERVAL 31 DAY),
                    o.order_time >= DATE_SUB(DATE(now()), INTERVAL 31 DAY))]]>
            <if test="deliveryTime != '' and deliveryTime != null">
                AND dt.end_time = #{deliveryTime}
            </if>
            <if test=" lineGroupId != null ">
                AND dt.line_group_id = #{lineGroupId}
            </if>
        </if>
        <if test=" businessType != null and businessType == 10">
            AND o.business_type = 10
            <if test="logisticsCenterId != null">
                AND o.logistics_center_id = #{logisticsCenterId}
            </if>
        </if>
        <if test=" businessType != null and businessType == 13">
            AND o.business_type = 13
        </if>
        <if test=" businessType != null and businessType == 14">
            AND o.business_type = 14
        </if>
        <if test="orderTime !=null">
            AND o.order_time = #{orderTime}
        </if>
        <if test=" orderCode != '' and orderCode != null ">
            AND o.order_code = #{orderCode}
        </if>
        <if test=" warehouseId != null ">
            AND so.warehouse_id = #{warehouseId}
        </if>
        AND o.order_status = 0
        AND so.status = 0
        <if test=" deliveryBatch != null ">
            AND o.delivery_batch = #{deliveryBatch}
        </if>
        ORDER BY s.id
    </select>

    <select id="querySubOrderItemList" parameterType="com.pinshang.qingyun.order.vo.order.SubOrderSearchVo"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.SubOrderItemListEntry">
        SELECT i.commodity_id,
               c.commodity_code,
               c.commodity_name,
               c.commodity_spec,
               i.quantity,
               Ceiling(i.quantity / c.commodity_package_spec) as number,
               IFNULL(i.convert_status, 0)                    AS convertStatus,
               i.target_commodity_id,
               c2.commodity_code                              AS targetCommodityCode,
               c2.commodity_name                              AS targetCommodityName,
               c2.commodity_spec                              AS targetCommoditySpec,
               i.target_quantity,
               case
                   when i.convert_status = 1 then Ceiling(i.target_quantity / c2.commodity_package_spec)
                   else null end                              as targetNumber
        FROM t_sub_order so
                 INNER JOIN t_sub_order_item i ON i.sub_order_id = so.id
                 LEFT JOIN t_commodity c ON c.id = i.commodity_id
                 LEFT JOIN t_commodity c2 ON c2.id = i.target_commodity_id
        WHERE so.id = #{id}
    </select>

    <select id="findItemsBySubOrderId" parameterType="java.lang.Long"
            resultType="com.pinshang.qingyun.order.vo.order.PickSubOrderItemRespVo">
        select soi.id,
               soi.commodity_id,
               soi.quantity
        from t_sub_order_item soi
        where soi.sub_order_id = #{subOrderId}
    </select>

    <select id="findShopListByStoreIds" parameterType="java.util.List"
            resultType="com.pinshang.qingyun.order.model.shop.Shop">
        select
        md.id id,
        md.store_id,
        md.shop_type
        from t_md_shop md
        where md.store_id IN
        <foreach collection="storeIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="findItemsBySubOrderIds" parameterType="java.util.List"
            resultType="com.pinshang.qingyun.order.vo.order.PickSubOrderItemRespVo">
        select
        soi.id,
        soi.commodity_id,
        soi.quantity,
        soi.sub_order_id AS subOrderId,
        o.order_time
        from t_sub_order_item soi
        LEFT JOIN t_sub_order so on so.id = soi.sub_order_id
        LEFT JOIN t_order o on o.id = so.order_id
        where soi.sub_order_id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <update id="batchUpdateDeliveryQuantity" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE t_sub_order_item SET real_delivery_quantity = #{item.realDeliveryQuantity}, real_receive_quantity =
            #{item.realReceiveQuantity}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateXDDeliveryQuantity" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE t_sub_order_item SET real_delivery_quantity = #{item.realDeliveryQuantity}, real_receive_quantity =
            #{item.realReceiveQuantity}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateCombDeliveryQuantity" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE t_sub_order_item SET real_delivery_quantity = #{item.realDeliveryQuantity}, real_receive_quantity =
            #{item.realDeliveryQuantity}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateCombDeliveryQuantityNOReceive" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE t_sub_order_item SET real_delivery_quantity = #{item.realDeliveryQuantity}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="getTotalPriceBySubOrderId" resultType="java.math.BigDecimal">
        select
        <choose>
            <when test="logisticsModel == 1">
                sum(real_delivery_quantity*price)
            </when>
            <otherwise>
                sum(real_receive_quantity*price)
            </otherwise>
        </choose>
        from t_sub_order_item where sub_order_id = #{subOrderId}
    </select>

    <select id="getSubOrderItemInfoById"
            resultType="com.pinshang.qingyun.order.mapper.entry.shop.SubOrderItemInfoEntry">
		<![CDATA[
        select c.commodity_code,
               soi.commodity_id,
               soi.quantity,
               soi.price,
               soi.real_receive_quantity
        from t_sub_order_item soi
                 inner join t_commodity c on c.id = soi.commodity_id
        where soi.sub_order_id = #{subOrderId}
          and IFNULL(soi.real_receive_quantity, 0) <> IFNULL(soi.quantity, 0)
        ]]>
	</select>

    <select id="getSubOrderInfoById" resultType="com.pinshang.qingyun.order.mapper.entry.shop.SubOrderInfoEntry">
        select o.order_code,
               so.order_time,
               u.employee_phone as mobile,
               s.store_code,
               o.store_id
        from t_sub_order so
                 inner join t_order o on o.id = so.order_id
                 left join t_employee_user u on o.create_id = u.user_id
                 inner join t_store s on s.id = o.store_id
        where so.id = #{subOrderId}
          and so.logistics_model = 0
    </select>
    <resultMap id="OrderVoMap" type="com.pinshang.qingyun.order.vo.order.OrderVo">
        <id column="sourceId" property="sourceId"/>
        <result column="order_code" property="sourceCode"/>
        <result column="source_type" property="sourceType"/>
        <result column="order_type" property="orderType"/>
        <result column="store_id" property="storeId"/>
        <result column="order_time" property="orderTime"/>
        <result column="create_time" property="createTime"/>
        <result column="deliveryTime" property="deliveryTime"/>
        <result column="consignment_id" property="consignmentId"/>
        <result column="business_type" property="businessType"/>
        <collection property="items" ofType="com.pinshang.qingyun.order.vo.order.ItemVo">
            <result column="sourceIdOfItem" property="sourceId"/>
            <result column="itemId" property="itemId"/>
            <result column="commodity_id" property="commodityId"/>
            <result column="quantity" property="number"/>
            <result column="unitPrice" property="unitPrice"/>
            <result column="total_price" property="totalPrice"/>
            <result column="real_receive_quantity" property="deliveryNumber"/>
            <result column="deliveryUnitPrice" property="deliveryUnitPrice"/>
            <result column="deliveryTotalPrice" property="deliveryTotalPrice"/>
        </collection>
    </resultMap>
    <select id="listOrderDetail" resultMap="OrderVoMap">
        SELECT o.id AS sourceId,
        o.order_code,
        CASE tso.logistics_model
        WHEN 0 THEN 'ZS_ORDER'
        WHEN 1 THEN 'PS_ORDER'
        WHEN 2 THEN 'ZT_ORDER'
        END source_type,
        o.order_type,
        o.store_id,
        o.order_time,
        o.create_time,
        o.company_id companyId,
        now() AS deliveryTime,
        o.consignment_id,
        o.id AS sourceIdOfItem,
        tsoi.id AS itemId,
        tsoi.commodity_id,
        tsoi.quantity,
        tsoi.price AS unitPrice,
        tsoi.total_price,
        ifnull(tsoi.real_delivery_quantity, 0) as real_receive_quantity,
        tsoi.price AS deliveryUnitPrice,
        ifnull(tsoi.real_delivery_quantity, 0) * tsoi.price AS deliveryTotalPrice,
        o.business_type
        FROM t_sub_order tso
        LEFT JOIN t_order o ON tso.order_id = o.id
        LEFT JOIN t_sub_order_item tsoi ON tso.id = tsoi.sub_order_id
        LEFT JOIN t_md_shop tms ON o.store_id = tms.store_id
        WHERE tso.logistics_model IN (1, 2)
        AND tso.id = #{subOrderId}
        <if test="excludeOrderTypeList != null and excludeOrderTypeList.size > 0">
            AND o.order_type not in
            <foreach collection="excludeOrderTypeList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="listOrderDetailByOrderId" resultMap="OrderVoMap">
        SELECT o.id AS sourceId,
        o.order_code,
        CASE tso.logistics_model
        WHEN 0 THEN 'ZS_ORDER'
        WHEN 1 THEN 'PS_ORDER'
        WHEN 2 THEN 'ZT_ORDER'
        END source_type,
        o.order_type,
        o.store_id,
        o.order_time,
        o.create_time,
        o.company_id companyId,
        now() AS deliveryTime,
        o.consignment_id,
        o.id AS sourceIdOfItem,
        tsoi.id AS itemId,
        tsoi.commodity_id,
        tsoi.quantity,
        tsoi.price AS unitPrice,
        tsoi.total_price,
        ifnull(tsoi.real_delivery_quantity, 0) as real_receive_quantity,
        tsoi.price AS deliveryUnitPrice,
        ifnull(tsoi.real_delivery_quantity, 0) * tsoi.price AS deliveryTotalPrice,
        o.business_type
        FROM t_order o
        LEFT JOIN t_sub_order tso ON tso.order_id = o.id
        LEFT JOIN t_sub_order_item tsoi ON tso.id = tsoi.sub_order_id
        LEFT JOIN t_md_shop tms ON o.store_id = tms.store_id
        WHERE tso.logistics_model IN (1, 2)
        AND o.id = #{orderId}
        <if test="excludeOrderTypeList != null and excludeOrderTypeList.size > 0">
            AND o.order_type not in
            <foreach collection="excludeOrderTypeList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryOrderListDetailByOrderId" resultMap="OrderVoMap">
        SELECT o.id                                             AS sourceId,
               o.order_code,
               o.order_type,
               o.store_id,
               o.order_time,
               o.create_time,
               o.company_id                                        companyId,
               now()                                            AS deliveryTime,
               o.consignment_id,
               o.id                                             AS sourceIdOfItem,
               ol.id                                            AS itemId,
               ol.commodity_id,
               ol.commodity_num                                    quantity,
               ol.commodity_price                               AS unitPrice,
               ol.total_price,
               ifnull(ol.real_quantity, 0)                      as real_receive_quantity,
               ol.commodity_price                               AS deliveryUnitPrice,
               ifnull(ol.real_quantity, 0) * ol.commodity_price AS deliveryTotalPrice,
               o.business_type
        FROM t_order o
                 LEFT JOIN t_order_list ol ON o.id = ol.order_id
                 LEFT JOIN t_md_shop tms ON o.store_id = tms.store_id
        where o.id = #{orderId}
    </select>


    <update id="cancelSubOrder">
        UPDATE t_sub_order
        SET `status` = 2
        WHERE order_id = #{orderId}
    </update>


    <select id="getOrderInfoList" parameterType="java.util.List"
            resultType="com.pinshang.qingyun.order.dto.OrderInfoODTO">
        SELECT
        o.order_code,
        o.order_status,
        o.order_time,
        o.store_id,
        soi.commodity_id,
        so.logistics_model,
        soi.quantity,
        soi.total_price,
        soi.price,
        soi.real_delivery_quantity,
        soi.real_receive_quantity,
        o.create_id,
        o.create_time,
        o.update_id,
        o.update_time,

        soi.id order_item_id,
        mro.`status`
        -- fw.workshop_code,
        -- fw.workshop_name,
        -- f.factory_code,
        -- f.factory_name,

        -- d.option_name commodity_unit_name,
        -- t.employee_name real_name,
        -- soi.`commodity_id`,o.create_id
        from t_order o
        inner join `t_sub_order` so on so.`order_id` = o.`id`
        inner join `t_sub_order_item` soi on soi.`sub_order_id` = so.`id`
        LEFT JOIN t_md_receive_order AS mro ON mro.sub_order_id = so.id
        inner join t_md_shop md on md.store_id = o.store_id
        -- LEFT join `t_commodity` c on c.`id` = soi.`commodity_id`
        -- LEFT JOIN t_factory_workshop fw ON c.new_workshop_id = fw.id
        -- LEFT JOIN t_factory f ON c.commodity_factory_id = f.id
        -- left join `t_dictionary` d on d.`id` = c.`commodity_unit_id`
        -- LEFT JOIN t_employee_user t ON t.user_id = o.create_id
        WHERE so.status != 2
        and so.`id` in
        <foreach collection="subOrderIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getStockOutSubOrderList"
            resultType="com.pinshang.qingyun.order.vo.subOrder.StockOutJobSubOrderRespVo">
        select so.id as subOrderId, SUM(IFNULL(soi.real_delivery_quantity, 0)) as realDeliveryQuantity
        from t_sub_order so
        INNER JOIN t_sub_order_item soi on so.id = soi.sub_order_id
        WHERE so.id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        GROUP BY so.id
    </select>

    <update id="updateOrderProcessStatus">
        UPDATE t_order o
        INNER JOIN t_sub_order so ON o.id = so.order_id
        SET o.process_status = #{processStatus},o.update_time = NOW()
        WHERE so.id IN
        <foreach collection="subOrderIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="findOrderRealDeliveryFinishList"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderRealDeliveryFinishEntry">
        SELECT
        o.id as orderId,
        o.real_sub_order_done_status,
        o.store_id,
        o.order_code,
        o.store_type_id AS orderStoreTypeId,
        sto.store_type_id,
        COUNT(o.id)AS subNum,
        COUNT(IF(s.writeback_real_qty_flag=1,1,NULL)) AS realFinishNum
        FROM
        t_order o
        INNER JOIN t_sub_order s ON o.id=s.order_id
        LEFT JOIN t_store sto ON sto.id=o.store_id
        WHERE
        o.id IN
        <foreach collection="orderIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND o.order_status=0
        AND s.status IN(0,1)
        AND s.logistics_model IN (1,2)
        GROUP BY o.id;
    </select>

    <select id="findOrderIdListBySubOrderIds" resultType="java.lang.Long">
        SELECT
        o.id
        FROM
        t_sub_order s
        INNER JOIN t_order o ON o.id=s.order_id
        WHERE
        s.id IN
        <foreach collection="subOrderIds" item="subOrderId" separator="," open="(" close=")">
            #{subOrderId}
        </foreach>
        AND o.order_status=0
        AND s.status IN(0,1)
        AND s.logistics_model IN (1,2)
        GROUP BY o.id;
    </select>

    <select id="queryUnDeliverySubOrderJobList"
            parameterType="com.pinshang.qingyun.order.vo.subOrder.UnDeliverySubOrderJobReqVo"
            resultType="com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderListEntry">
        SELECT
        o.store_id,
        so.id AS subOrderId,
        so.enterprise_id,
        so.warehouse_id,
        so.logistics_model,
        so.variety_total,
        so.supplier_id,
        o.id AS referOrderId,
        o.delivery_batch,
        o.order_time,
        o.order_code AS referOrderCode,
        ms.shop_type,
        ms.id AS shopId,
        o.order_type,
        s.store_type_id,
        so.presale_status,
        so.stock_type,
        o.business_type,
        o.stall_id
        FROM
        t_sub_order so
        INNER JOIN t_order o ON o.id = so.order_id
        INNER JOIN t_store s ON o.store_id = s.id
        LEFT JOIN t_md_shop ms ON s.id = ms.store_id
        WHERE
        o.order_status = 0
        AND so.`status` = 0
        AND (o.order_duration_time is not null AND o.order_duration_time <![CDATA[ <= ]]> #{currentTime})
        <if test=" businessType == null or businessType==0">
            AND (o.business_type is null OR o.business_type = 0)
        </if>
        <if test=" businessType != null and businessType != 0">
            AND o.business_type = #{businessType}
        </if>
        <choose>
            <when test="null != orderTime and orderTime != ''">
                AND so.order_time = #{orderTime}
            </when>
            <otherwise>
                AND o.order_time <![CDATA[ >= ]]> DATE(#{currentTime})
            </otherwise>
        </choose>
        <if test=" warehouseId != null ">
            AND so.warehouse_id = #{warehouseId}
        </if>
    </select>

    <select id="queryUnDeliverySubOrderIdsJob"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderTODeliveryOrderEntry">
        SELECT
        o.id AS orderId,
        so.id AS subOrderId,
        so.order_time AS orderTime,
        so.warehouse_id AS warehouseId,
        IFNULL(o.business_type,0) AS businessType
        FROM
        t_sub_order so
        INNER JOIN t_order o ON o.id = so.order_id
        INNER JOIN t_store s ON o.store_id = s.id
        WHERE
        o.order_status = 0
        <if test=" warehouseId != null ">
            AND so.warehouse_id = #{warehouseId}
        </if>
        AND so.`status` = 0
        AND (o.order_duration_time is not null AND o.order_duration_time <![CDATA[ <= ]]> #{currentTime})
        <if test=" businessType == null or businessType == 0">
            AND (o.business_type is null OR o.business_type=0)
        </if>
        <if test=" businessType != null and businessType != 0 ">
            AND o.business_type = #{businessType}
        </if>
        <choose>
            <when test="null != orderTime and orderTime != ''">
                AND so.order_time = #{orderTime}
            </when>
            <otherwise>
                AND o.order_time <![CDATA[ >= ]]> DATE(#{currentTime})
            </otherwise>
        </choose>
    </select>
    <select id="queryUnDeliverySubOrderXDAJobList"
            parameterType="com.pinshang.qingyun.order.vo.subOrder.UnDeliverySubOrderJobReqVo"
            resultType="com.pinshang.qingyun.order.mapper.entry.splitOrder.SubOrder2DeliveryOrderListEntry">
        SELECT
        o.store_id,
        so.id AS subOrderId,
        so.enterprise_id,
        so.warehouse_id,
        so.logistics_model,
        so.variety_total,
        so.supplier_id,
        o.id AS referOrderId,
        o.delivery_batch,
        o.order_time,
        o.order_code AS referOrderCode,
        ms.shop_type,
        ms.id AS shopId,
        o.order_type,
        s.store_type_id,
        ssti.id AS shiftStoreTypeId,
        so.presale_status,
        so.stock_type,
        o.business_type
        FROM
        t_sub_order so
        INNER JOIN t_order o ON o.id = so.order_id
        INNER JOIN t_store s ON o.store_id = s.id
        INNER JOIN t_shift_store_type_id ssti ON ssti.store_id = s.id AND ssti.store_id = o.store_id
        LEFT JOIN t_md_shop ms ON s.id = ms.store_id
        WHERE
        s.store_type_id = #{xdaStoreTypeId}
        <if test="null != orderTime and orderTime != '' ">
            AND so.order_time = #{orderTime} -- 时间
        </if>
        <if test=" warehouseId != null ">
            AND so.warehouse_id = #{warehouseId}
        </if>
        AND o.order_status = 0
        AND so.`status` = 0
        AND (o.order_duration_time is not null AND o.order_duration_time <![CDATA[ <= ]]> #{currentTime})
        AND
        <![CDATA[ ( ssti.xda_real_delivery_begin_date IS NOT NULL AND ssti.xda_real_delivery_begin_date != '' AND o.order_time >= ssti.xda_real_delivery_begin_date ) ]]>
        AND (ssti.delivery_execute_time IS NULL OR o.create_time <![CDATA[ >= ]]> ssti.delivery_execute_time )
    </select>

    <select id="queryUnDeliverySubOrderIdsXDAJob"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderTODeliveryOrderEntry">
        SELECT
        o.id AS orderId,
        so.id AS subOrderId,
        ssti.id AS shiftStoreTypeId,
        so.order_time AS orderTime,
        so.warehouse_id AS warehouseId,
        o.business_type
        FROM
        t_sub_order so
        INNER JOIN t_order o ON o.id = so.order_id
        INNER JOIN t_store s ON o.store_id = s.id
        INNER JOIN t_shift_store_type_id ssti ON ssti.store_id = s.id
        WHERE
        s.store_type_id = #{xdaStoreTypeId}
        <if test=" warehouseId != null ">
            AND so.warehouse_id = #{warehouseId}
        </if>
        <if test="null != orderTime and orderTime != '' ">
            AND so.order_time = #{orderTime} -- 时间
        </if>
        AND o.order_status = 0
        AND so.`status` = 0
        AND (o.order_duration_time is not null AND o.order_duration_time <![CDATA[ <= ]]> #{currentTime})
        AND
        <![CDATA[ ( ssti.xda_real_delivery_begin_date IS NOT NULL AND ssti.xda_real_delivery_begin_date != '' AND o.order_time >= ssti.xda_real_delivery_begin_date ) ]]>
        AND if(ssti.delivery_execute_time IS NOT NULL, o.create_time <![CDATA[ >= ]]> ssti.delivery_execute_time,1 )
    </select>

    <select id="findNoDeliveryByOrderIds"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderTODeliveryOrderEntry">
        SELECT
        o.id AS orderId,
        so.id AS subOrderId,
        IFNULL(o.business_type,0) AS businessType
        FROM
        t_sub_order so
        INNER JOIN t_order o ON so.order_id = o.id
        INNER JOIN t_store s ON o.store_id = s.id
        WHERE
        o.id IN
        <foreach collection="orderIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND so.`status` = 0
        <if test=" businessType == null or businessType == 0 or businessType == 14">
            AND IF(o.order_duration_time IS NULL, 1, o.order_duration_time <![CDATA[ <= ]]> NOW())
        </if>
        <if test=" businessType != null and businessType != 0">
            AND o.business_type = #{businessType}
        </if>
        <if test=" businessType == null or businessType==0">
            AND (o.business_type is null OR o.business_type = 0)
        </if>
        <if test=" warehouseId != null ">
            AND so.warehouse_id = #{warehouseId}
        </if>
    </select>
    <select id="selectSubOrderItemCommodityPrice"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.SubOrderItemCommodityPriceEntry">
        SELECT soi.id           AS id,
               so.order_id      AS orderId,
               soi.commodity_id AS commodityId,
               soi.sub_order_id AS subOrderId,
               soi.quantity     AS quantity,
               soi.total_price  AS totalPrice,
               so.total_price   AS sumTotalPrice,
               soi.price        AS price
        FROM t_sub_order_item soi
                 LEFT JOIN t_sub_order so ON so.id = soi.sub_order_id
        WHERE so.order_time = #{orderTime}
          AND EXISTS (SELECT 1
                      FROM t_order_list ol
                      WHERE ol.order_id = so.order_id
                        AND ol.commodity_id = soi.commodity_id
                        AND soi.quantity = ol.commodity_num
                        AND (ol.commodity_price != soi.price or ol.total_price != soi.total_price))
    </select>
    <update id="batchUpdateShiftStoreType" parameterType="java.util.List">
        UPDATE t_shift_store_type_id SET delivery_execute_time = #{currentTime}
        where id in
        <foreach collection="shiftStoreTypeIds" index="index" item="shiftStoreTypeId" open="(" separator="," close=")">
            #{shiftStoreTypeId}
        </foreach>
    </update>

    <update id="updateSubOrderWritebackRealQtyFlagViaOrderIdAndSubOrderItemCommodityList">
        UPDATE t_sub_order tso
        INNER JOIN t_order o on o.id = tso.order_id
        INNER JOIN t_sub_order_item tsoi on tso.id = tsoi.sub_order_id
        SET tso.writeback_real_qty_flag = #{writebackRealQtyFlag}
        WHERE o.id= #{orderId}
        and tsoi.commodity_id IN
        <foreach collection="subOrderItemCommodityList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <select id="findOrderRealTotalPriceListByOrderIds"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.OrderRealTotalPriceListEntry">
        SELECT
        sum(total_price - real_total_price) AS money,
        order_id
        FROM t_order_list_gift
        WHERE comb_type in (1,2)
        <if test="handleOverFlag !=null and handleOverFlag == 0">
            and ifnull(total_price,0.00) &gt; ifnull(real_total_price,0.00)
        </if>
        and order_id IN
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        <![CDATA[ AND real_quantity != commodity_num ]]>
        GROUP BY order_id
    </select>

    <select id="selectSubOrderInfoListByOrderIds"
            resultType="com.pinshang.qingyun.order.mapper.entry.order.SubOrderInfoListEntry">
        SELECT
        s.order_id,
        s.id AS subOrderId
        FROM t_sub_order s
        WHERE
        s.order_id IN
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND s.status != 2
        <if test="logisticsModel!=null">
            AND s.logistics_model != #{logisticsModel}
        </if>
    </select>

</mapper>