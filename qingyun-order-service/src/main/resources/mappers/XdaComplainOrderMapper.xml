<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.xda.XdaComplaintOrderMapper">

    <resultMap id="commodityItem" type="com.pinshang.qingyun.order.dto.xda.XdaComplaintOrderODTO" >
        <id column="uid" property="complainId"/>
        <result column="complaint_code" property="complaintCode"/>
        <result column="deliveryDate" property="deliveryDate"/>
        <result column="created_at" property="createTime"/>
        <result column="complainTime" property="complainTime"/>
        <result column="complaint_handle_status" property="complaintHandleStatus"/>
        <result column="complaint_remark" property="complaintReason"/>
        <result column="orderComplaintType" property="orderComplaintType"/>
        <collection property="complaintCommodityList" ofType="com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityItemDTO">
            <result column="commodity_id" property="commodityId"/>
            <result column="commodityName" property="commodityName"/>
            <result column="complaint_type" property="complaintType"/>
            <result column="default_pic_url" property="commodityPicUrl"/>
            <result column="question_type" property="questionType"/>
            <result column="commodity_order_number" property="realDeliveryQuantity"/>
            <result column="realDeliveryQuantityStr" property="realDeliveryQuantityStr"/>
            <result column="complaint_number" property="realReturnQuantity"/>
            <result column="realReturnQuantityStr" property="realReturnQuantityStr"/>
            <result column="complaint_money" property="complaintMoney"/>
            <result column="commodity_price" property="commodityPrice"/>
            <result column="commodityUnit" property="commodityUnit"/>
            <result column="commodity_code" property="commodityCode"/>
            <result column="commodity_spec" property="commoditySpec"/>
            <result column="commodityPriceName" property="commodityPriceName"/>
            <result column="checkQuantity" property="checkQuantity"/>
            <result column="checkQuantityStr" property="checkQuantityStr"/>
        </collection>
    </resultMap>

    <update id="updateXdaComplaintOrder" >
        update t_complaint
        set complaint_total_money = complaint_total_money + #{complaintTotalMoney},
            update_time = now()
        <if test="complaintRemark != null">
            ,complaint_remark = #{complaintRemark}
        </if>
        where uid = #{id}
    </update>

    <select id="queryComplaintOrderList" resultMap="commodityItem">
        SELECT
            complain.uid,
            complain.complaint_code,
            DATE_FORMAT( complain.delivery_date, '%Y-%m-%d' ) AS deliveryDate,
            complain.created_at,
            (CASE WHEN complain.update_time - complain.created_at > 0 THEN
            CONCAT( complain.update_time,' 更新') ELSE
            CONCAT( complain.update_time,' 创建') END
            ) AS complainTime,
            complain.complaint_handle_status,
            complain.complaint_remark,
            complain.complaint_type AS orderComplaintType,
            item.complaint_type,
            item.question_type,
            item.commodity_order_number,
            item.complaint_number,
            item.complaint_money,
            item.check_number checkQuantity,
            CONCAT(ABS(item.check_number), dic.option_name) AS checkQuantityStr,
            item.question_type,
            item.commodity_id,
            item.commodity_price,
            text.commodity_app_name AS commodityName,
            com.commodity_code,
            com.commodity_spec,
            pic.pic_url AS default_pic_url,
            dic.option_name AS commodityUnit,
            CONCAT(ABS(item.complaint_number), dic.option_name) AS realReturnQuantityStr,
            CONCAT(ABS(item.commodity_order_number), dic.option_name) AS realDeliveryQuantityStr,
            CONCAT('￥', item.commodity_price, '/', dic.option_name) AS commodityPriceName
        FROM t_complaint complain
        LEFT JOIN t_complaint_commodity_list item on complain.uid = item.complaint_id
        LEFT JOIN t_commodity com on item.commodity_id = com.id
        LEFT JOIN t_xda_commodity_text text on com.id = text.commodity_id
        LEFT JOIN t_xda_commodity_text_pic pic on com.id = pic.commodity_id AND pic.is_default = 1 AND pic.pic_type = 1
        LEFT JOIN t_dictionary dic on com.commodity_unit_id = dic.id
        WHERE complain.store_id = #{storeId}
        <if test=" status != null and status > 0 ">
            AND complain.complaint_handle_status = #{status}
        </if>
--             AND item.enabled = 1
            AND complain.created_at >= date_sub(curdate(), interval 3 month)
            AND item.complaint_type IN (1, 2, 3)
            AND CASE WHEN item.complaint_type = 2 THEN item.question_type IN (9131078242860601471, 9131078242860601472, 9131078242860601473, 9131078242860601474)
                ELSE item.question_type IN (9131078242860604176, 9131078242860601561)
                END
        ORDER BY complain.created_at DESC, item.created_at DESC
    </select>

    <select id="queryComplaintCommodity" resultType="com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityItemDTO">
        SELECT
            o.store_id,
            gift.commodity_id,
            (CASE WHEN (gift.real_quantity - gift.commodity_num) > 0 THEN gift.commodity_num ELSE gift.real_quantity END ) AS realDeliveryQuantity,
            CONCAT((CASE WHEN (gift.real_quantity - gift.commodity_num) > 0 THEN gift.commodity_num ELSE gift.real_quantity END ), dic.option_name) AS realDeliveryQuantityStr,
            gift.commodity_price,
            pic.pic_url AS commodityPicUrl,
            com.commodity_code,
            com.commodity_spec,
            com.is_weight,
            text.commodity_app_name AS commodityName,
            dic.option_name AS commodityUnit
        FROM t_order o
        LEFT JOIN t_order_list_gift gift on o.id = gift.order_id
        LEFT JOIN t_commodity com on gift.commodity_id = com.id
        LEFT JOIN t_xda_commodity_text text on com.id = text.commodity_id
        LEFT JOIN t_xda_commodity_text_pic pic on com.id = pic.commodity_id AND pic.is_default = 1 AND pic.pic_type = 1
        LEFT JOIN t_dictionary dic on com.commodity_unit_id = dic.id
        WHERE o.store_id = #{dto.storeId}
            AND o.order_time = #{dto.deliveryDate}
            AND o.order_status = 0
            <if test="dto.businessType != null and (dto.businessType == 10 or dto.businessType == 15 )">
                AND o.process_status = 19
            </if>

            <if test="dto.businessType == null or (dto.businessType != 10 and dto.businessType != 15 )">
                AND o.process_status = 15
            </if>
            AND com.logistics_model in (1,2)
            and gift.comb_type in (1,2)
    </select>

    <select id="queryComplaintCompleteCommodity" resultType="com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityItemDTO">
        SELECT
            ts.store_id,
            item.commodity_id,
            item.complaint_number AS realReturnQuantity,
            item.commodity_price,
            text.commodity_app_name AS commodityName,
            pic.pic_url AS commodityPicUrl,
            com.commodity_code,
            com.commodity_spec,
            com.is_weight
        from t_complaint ts
        LEFT JOIN t_complaint_commodity_list item on ts.uid = item.complaint_id
        LEFT JOIN t_commodity com on item.commodity_id = com.id
        LEFT JOIN t_xda_commodity_text text on com.id = text.commodity_id
        LEFT JOIN t_xda_commodity_text_pic pic on com.id = pic.commodity_id AND pic.is_default = 1 AND pic.pic_type = 1
        WHERE ts.store_id = #{storeId}
            AND ts.delivery_date = #{deliveryDate}
            AND ts.complaint_type = #{complaintType}
            AND ts.complaint_handle_status in (1,2)
        <if test="commodityIdList != null">
            AND item.commodity_id in
            <foreach item="item" index="index" collection="commodityIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

<!--    已投诉/待审核商品数量-->
    <select id="queryComplaintCompleteCommodityQuantity" resultType="com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityQuantityDTO">
        SELECT
            ts.store_id,
            item.commodity_id,
            item.commodity_price,
            item.complaint_type,
            (CASE WHEN item.complaint_number IS NULL THEN 0 ELSE item.complaint_number END) AS realReturnQuantity
        from t_complaint ts
        LEFT JOIN t_complaint_commodity_list item on ts.uid = item.complaint_id
        WHERE ts.store_id = #{storeId}
            AND ts.delivery_date = #{deliveryDate}
            AND ts.complaint_type = #{complaintType}
            AND ts.complaint_handle_status in (1,2)
        <if test="commodityIdList != null">
            AND item.commodity_id in
            <foreach item="item" index="index" collection="commodityIdList" open="(" separator="," close=")">
                #{item}
            </foreach>

        </if>
    </select>

    <select id="queryComplaint" resultType="com.pinshang.qingyun.order.model.xda.XdaComplaint">
        SELECT
            ts.uid AS id,
            ts.created_at,
            ts.enabled,
            ts.enterprise_id,
            ts.complaint_code,
            ts.complaint_handle,
            ts.complaint_total_money,
            ts.complaint_type,
            ts.complaint_user,
            ts.create_id,
            ts.delivery_date,
            ts.handle_remark,
            ts.handle_result,
            ts.store_code,
            ts.store_id,
            ts.update_time,
            ts.check_date,
            ts.check_remark,
            ts.complaint_handle_status,
            ts.complaint_remark,
            ts.linkman_mobile,
            ts.check_total_money,
            ts.batch_no,
            ts.deliveryman_id
        from t_complaint ts
        WHERE ts.store_id = #{storeId}
            AND ts.delivery_date = #{deliveryDate}
            AND ts.complaint_type = #{complaintType}
            AND ts.complaint_handle_status in (1, 2)
    </select>

    <!--根据客户id和送货时间查询订单中的送货员-->
    <select id="findOrderDeliveryMan" resultType="com.pinshang.qingyun.order.mapper.xda.XdaComplaintDeliveryManODTO">
        SELECT
            om.delivery_man_id as deliveryManId,
            om.delivery_man_name as deliveryManName,
            o.store_id as storeId,
            o.order_time as orderTime
        FROM
            t_order o
            LEFT JOIN t_order_mirror om ON om.order_id = o.id
        <where>
            <if test="storeId != null">
                and o.store_id = #{storeId}
            </if>
            <if test="deliveryDate != null">
                and o.order_time = DATE_FORMAT(#{deliveryDate},'%Y-%m-%d')
            </if>
        </where>
        GROUP BY o.store_id
        limit 1
    </select>

    <!--根据客户id查询客户表中的送货员-->
    <select id="findStoreDeliveryMan" resultType="com.pinshang.qingyun.order.mapper.xda.XdaComplaintDeliveryManODTO">
        select
            s.id as storeId, s.deliveryman_id as deliveryManId
        from t_store s
        where s.id = #{storeId};
    </select>
</mapper>