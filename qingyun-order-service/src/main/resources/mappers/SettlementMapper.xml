<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.SettlementMapper">

    <update id="upXsStartBillDateBySettleId">
      update t_settlement set xs_start_bill_date=#{xsStartBillDate} where id=#{id}
    </update>

    <select id="findXsStartBillDateById" resultType="java.lang.String">
        SELECT sa.xs_start_bill_date
        FROM t_settlement sa
        where sa.id = #{id}
    </select>

    <select id="findStoreIdsBySettleId" resultType="Long">
        select store_id
        from t_store_settlement
        where settlement_customer_id = #{settleId}
    </select>



    <!--条件查询结账客户-->
    <select id="findByStr" resultType="com.pinshang.qingyun.order.mapper.entry.settlement.SettlementEntry">
        SELECT
            ts.id,
            ts.customer_code AS customerCode,
            ts.customer_name AS customerName
        FROM
          t_settlement ts
        WHERE
        1=1
        <if test="null != str and str != '' ">
            AND (ts.customer_code LIKE CONCAT("%", #{str} ,"%")
            OR ts.customer_name LIKE CONCAT("%", #{str} ,"%"))
        </if>
    </select>
    <select id="selectSettlementByStoreId" resultType="com.pinshang.qingyun.order.model.settlement.Settlement">
        SELECT
            ts.customer_name
        FROM
            t_settlement ts
        LEFT JOIN t_store_settlement tss on ts.id = tss.settlement_customer_id
        where tss.store_id = #{storeId}
    </select>

</mapper>