package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.order.mapper.SettlementMapper;
import com.pinshang.qingyun.order.mapper.entry.settlement.SettlementEntry;
import com.pinshang.qingyun.order.model.settlement.Settlement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SettlementService {

    @Autowired
    private SettlementMapper storeSettlementMapper;

    public void  upXsStartBillDateBySettleId(Long id,String xsStartBillDate){
        storeSettlementMapper.upXsStartBillDateBySettleId(id,xsStartBillDate);
    }

    public String findXsStartBillDateById(Long id){
        return storeSettlementMapper.findXsStartBillDateById(id);
    }

    public List<Long> findStoreIdsBySettleId(Long settleId){
        return storeSettlementMapper.findStoreIdsBySettleId(settleId);
    }

    public List<SettlementEntry> findByStr(String str){
        return storeSettlementMapper.findByStr(str);
    }

    public Settlement findSettlementById(Long settlementId){
        return storeSettlementMapper.selectByPrimaryKey(settlementId);
    }

    public List<Settlement> findSettlementByStoreId(Long storeId){
        return storeSettlementMapper.selectSettlementByStoreId(storeId);
    }

}
