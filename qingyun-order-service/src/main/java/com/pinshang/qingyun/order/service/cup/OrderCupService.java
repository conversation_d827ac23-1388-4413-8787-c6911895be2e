package com.pinshang.qingyun.order.service.cup;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.enums.bcountry.SfScsProductCodeEnum;
import com.pinshang.qingyun.base.enums.commodity.CommodityPackageTypeEnums;
import com.pinshang.qingyun.base.enums.marketing.MarketSourceTypeEnum;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.order.ProductTypeEnum;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.KafkaConstant;
import com.pinshang.qingyun.kafka.MessageType;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.marketing.dto.FreeBuyCommodityIDTO;
import com.pinshang.qingyun.marketing.dto.FreeBuyIDTO;
import com.pinshang.qingyun.marketing.dto.FreeBuyODTO;
import com.pinshang.qingyun.marketing.dto.app.GiftCommodityODTO;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.marketing.service.mtCoupon.MtCouponTobClient;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.dto.cup.*;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLeftQuantityODTO;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLimitQuantityQueryDetailIDTO;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLimitQuantityQueryIDTO;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLimitQuantitySaveIDTO;
import com.pinshang.qingyun.order.dto.xda.TdaOrderSyncTmsIDTO;
import com.pinshang.qingyun.order.dto.xda.XdaCommodityLimit4AppODTO;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.enums.*;
import com.pinshang.qingyun.order.enums.cup.ApiProductFrozenMultipleEnum;
import com.pinshang.qingyun.order.listener.OrderHistoryHelper;
import com.pinshang.qingyun.order.manage.freebuy.FreeBuyGiftNumCalculator;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.common.EmployeeUserMapper;
import com.pinshang.qingyun.order.mapper.cup.OrderCupReportMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.common.EmployeeUser;
import com.pinshang.qingyun.order.model.cup.ConsumableLimit;
import com.pinshang.qingyun.order.model.cup.OrderWhite;
import com.pinshang.qingyun.order.model.gift.GiftModelCondition;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.model.promotionSale.PromotionSale;
import com.pinshang.qingyun.order.model.promotionSale.PromotionSaleComm;
import com.pinshang.qingyun.order.model.settlement.Settlement;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.model.store.StoreArea;
import com.pinshang.qingyun.order.model.store.StoreDuration;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.service.cup.factory.OrderDetailStrategyFactory;
import com.pinshang.qingyun.order.service.giftLimit.GiftLimitService;
import com.pinshang.qingyun.order.service.xda.CouponDayStatisticsService;
import com.pinshang.qingyun.order.service.xda.OrderLimitQuantityService;
import com.pinshang.qingyun.order.service.xda.v4.OrderMtCouponService;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import com.pinshang.qingyun.order.service.xda.v4.ToBService;
import com.pinshang.qingyun.order.service.xda.v4.XdaOrderV4Service;
import com.pinshang.qingyun.order.util.NumberUtils;
import com.pinshang.qingyun.order.validation.OrderValidationChain;
import com.pinshang.qingyun.order.validation.OrderValidationResult;
import com.pinshang.qingyun.order.vo.BStockLackVO;
import com.pinshang.qingyun.order.vo.order.CancelVo;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.order.vo.order.OrderHistoryAddTypeEnums;
import com.pinshang.qingyun.order.vo.order.OrderItemDto;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceODTO;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceSearchIDTO;
import com.pinshang.qingyun.price.service.StorePromotionClient;
import com.pinshang.qingyun.print.dto.OrderPrintIDTO;
import com.pinshang.qingyun.print.service.PrinterOrderApiClient;
import com.pinshang.qingyun.storage.dto.tob.*;
import com.pinshang.qingyun.storage.service.DeliveryOrderClient;
import com.pinshang.qingyun.storage.service.tob.ToBClient;
import com.pinshang.qingyun.store.dto.storeSettlement.StoreSettlementCollectODTO;
import com.pinshang.qingyun.store.service.StoreCompanyClient;
import com.pinshang.qingyun.store.service.StoreSettlementNewClient;
import com.pinshang.qingyun.tms.dto.transport.OrderTransportInfoODTO;
import com.pinshang.qingyun.tms.dto.transport.SelectOrderTransportInfoInfoIDTO;
import com.pinshang.qingyun.tms.service.TransportClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ArrayStack;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.*;

/**
 * @Author: sk
 * @Date: 2024/7/16
 */
@Service
@Slf4j
public class OrderCupService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private XdaOrderV4Service xdaOrderV4Service;
    @Autowired
    private ToBService toBService;
    @Autowired
    private OrderHistoryService orderHistoryService;
    @Autowired
    private OrderLimitQuantityService orderLimitQuantityService;
    @Autowired
    private TdaOrderService tdaOrderService;
    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private StoreRechargeService storeRechargeService;
    @Autowired
    private ShopService shopService;
    @Autowired
    private ConsumableLimitService consumableLimitService;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private DeliveryOrderClient deliveryOrderClient;
    @Autowired
    private StorePromotionClient storePromotionClient;
    @Autowired
    private OrderCupReportMapper orderCupReportMapper;
    @Autowired
    private PrinterOrderApiClient printInfoClient;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private CommodityFreezeGroupService commodityFreezeGroupService;

    @Autowired
    private ToBClient toBClient;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private MtCouponTobClient mtCouponTobClient;

    @Value("${application.name.switch}")
    private String applicationNameSwitch;

    @Autowired
    StoreSettlementNewClient storeSettlementNewClient;

    @Autowired
    StoreDurationService storeDurationService;

    @Autowired
    StoreService storeService;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderListGiftMapper orderListGiftMapper;

    @Autowired
    SellerOrderKafkaService sellerOrderKafkaService;

    @Autowired
    OrderMirrorMapper orderMirrorMapper;

    @Autowired
    EmployeeUserMapper employeeUserMapper;

    @Autowired
    OrderHistoryMapper orderHistoryMapper;

    @Autowired
    StoreSettlementMapper storeSettlementMapper;

    @Autowired
    StatisticalOrderKafkaService statisticalOrderKafkaService;

    @Autowired
    OrderWhiteService orderWhiteService;
    
    @Autowired
    GiftModelService giftModelService;

    @Autowired
    GiftModelConditionService giftModelConditionService;
    
    @Autowired
    GiftProductService giftProductService;
    
    @Autowired
    PromotionStkService promotionStkService;

    @Autowired
    PromotionStkCodeOrderService promotionStkCodeOrderService;

    @Autowired
    PromotionStkCommService promotionStkCommService;

    @Autowired
    PromotionSaleService promotionSaleService;

    @Autowired
    PromotionSaleCommService promotionSaleCommService;

    @Autowired
    StoreAreaService storeAreaService;

    @Autowired
    SettlementService settlementService;

    @Autowired
    StoreSettlementService storeSettlementService;

    @Autowired
    DictionaryClient dictionaryClient;

    @Autowired
    OrderMirrorService orderMirrorService;

    @Autowired
    OrderListService orderListService;

    @Autowired
    OrderDetailStrategyFactory orderDetailStrategyFactory;
    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private StoreCompanyClient storeCompanyClient;

    @Autowired
    private OrderMtCouponService mtCouponService;

    @Autowired
    private CouponDayStatisticsService couponDayStatisticsService;
    @Autowired
    private SplitOrderSendKfkService splitOrderSendKfkService;

    @Autowired
    private GiftLimitService giftLimitService;

    @Autowired
    private OrderValidationChain orderValidationChain;

    @Autowired
    private TransportClient transportClient;

    /**
     * 二分查找阈值
     */
    static final int BINARY_SEARCH_THRESHOLD = 8;

    @Autowired
    private MtPromotionClient mtPromotionClient;


    /**
     * 订单取消
     * @param orderId
     * @param tokenInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(Long orderId, TokenInfo tokenInfo,Integer planOrderType) {
        Order order = orderMapper.selectByPrimaryKey(orderId);
        Long userId = tokenInfo.getUserId();

        Integer orderStatus = order.getOrderStatus();
        if (orderStatus != 0) {
            QYAssert.isFalse("订单已取消或者删除!");
        }

        // 订单取消校验
        checkEditOrder(orderId, "cancel", true,planOrderType);

        //与华文2025-08-19讨论去除下面状态校验，客服与app流程有差异，不用按照app取消逻辑
//        xdaOrderV4Service.cancelOrderProcessStatusCheck(order);
        // 大店订单
        Boolean isBigShop = BusinessTypeEnums.BIGSHOP_SALE.getCode().equals(order.getBusinessType());
        Boolean isTdaStore = DeliveryOrderTypeEnums.TD_SALE.getCode().equals(order.getBusinessType());
        boolean isBCountry = DeliveryOrderTypeEnums.B_COUNTRY.getCode().equals(order.getBusinessType());

        if(!(isTdaStore || isBCountry || isBigShop)){
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, +1);
            String orderTime = TimeUtil.FORMAT_FULL_DATETIME.format(order.getOrderTime());
            String newTime = TimeUtil.FORMAT_SIMPLE_DATE.format(cal.getTime());
            if (order.getOrderTime().compareTo(cal.getTime()) <= 0 && !orderTime.startsWith(newTime)) {
                QYAssert.isFalse("订单已在配送途中，请不要取消！");
            }
        }

        Map<String,Object> paramMap = new HashMap<String, Object>(2);
        paramMap.put("orderId", order.getId());
        paramMap.put("orderStatus", 2);
        if(isTdaStore || isBCountry || isBigShop){
            paramMap.put("processStatus", XdaOrderProcessStatusEunm.CANCEL.getCode());
        }
        Integer rowNumber = orderMapper.updateOrderStatusByParameter(paramMap);
        QYAssert.isTrue(rowNumber > 0 , "订单取消失败!");

        // 耗材商品取消
        Example orderListExample = new Example(OrderList.class);
        orderListExample.createCriteria().andEqualTo("orderId", order.getId())
                .andEqualTo("type", ProductTypeEnum.NORMAL.getCode());
        List<OrderList> orderLists = orderListMapper.selectByExample(orderListExample);

        orderLists = orderLists.stream().filter(orderList -> CombTypeEnum.COMB_CHILD.getCode().intValue()!=orderList.getCombType()).collect(Collectors.toList());
        Map<Long, BigDecimal> productIdMap = orderLists.stream().collect(groupingBy(e -> e.getCommodityId(),
                Collectors.reducing(BigDecimal.ZERO, e -> e.getCommodityNum(), BigDecimal::subtract)));

        consumableLimitService.checkConsumableCommodityCancel(userId, order.getStoreId(), productIdMap);

        // 记录订单日志
        User cancelUser = new User();
        cancelUser.setId(userId);
        cancelUser.setRealName(tokenInfo.getRealName());
        orderHistoryService.insertOrderHistoryOnCancelOrderV2(order,cancelUser);

        // 维护B端特价限购记录表
        orderLimitQuantityService.saveOrderLimitQuantity(order.getId(), OperateTypeEnums.删除.getCode(),  false);

        //商品库存解冻
        if(!toBService.warehouseUnfreezeInventory(order.getId(), order.getStoreId(),order.getBusinessType())){
            QYAssert.isFalse("订单取消失败!");
        }

        // 返还优惠券
        mtCouponService.refundCoupon(order.getId(), order.getStoreId());

        //回款
        orderCupRefund(userId, order);

        //订单取消回退买赠总数量
        giftLimitService.deleteGiftLimitQuantity(Arrays.asList(order.getId()));

        // 订单取消事务完成后，发送消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                //取消的拆单消息
                SplitOrderKafkaVo splitOrderKafkaVo = new SplitOrderKafkaVo();
                splitOrderKafkaVo.setOrderId(order.getId());
                splitOrderKafkaVo.setType(KafkaMessageOperationTypeEnum.CANCEL);
                splitOrderKafkaVo.setCreateId(order.getCreateId());
                splitOrderKafkaVo.setEnterpriseId(order.getEnterpriseId());
                //splitOrderService.execute(splitOrderKafkaVo, splitOrderService, weChatSendMessageService);
                splitOrderSendKfkService.sendSplitOrderKfkMsg(splitOrderKafkaVo);

                // 给统计查询发送消息
                orderAsyncKafkaService.sendKafkaCancelOrder(order);

                // 发送结算信息kafka
                List<CancelVo> cancelVos = new ArrayList<>();
                CancelVo cancelVo = new CancelVo();
                cancelVo.setSourceCode(order.getOrderCode());
                cancelVo.setSourceType("ORDER");
                cancelVos.add(cancelVo);
                commonService.sendKafkaMessage(cancelVos, MessageType.SETTLE_ORDER_CANCEL_SYNC, KafkaConstant.SETTLE_CANCEL_DATA_ALL_TOPIC, KafkaMessageOperationTypeEnum.CANCEL.getCode());

                if(isNeedLogisticsTransPortByBusinessType(order.getBusinessType())  || isBigShop){
                    // 发送消息，通知物流
                    TdaOrderSyncTmsIDTO tdaOrderSyncTmsIDTO = TdaOrderSyncTmsIDTO.forOrder(order.getId(), XdaOrderProcessStatusEunm.CANCEL.getCode());
                    tdaOrderService.tdaOrderSyncToTms(tdaOrderSyncTmsIDTO);
                }

                // 查询订单优惠券信息，实时维护优惠券统计表
                couponDayStatisticsService.saveOrUpdateCouponDayStatistics(order.getId(), OperateTypeEnums.删除.getCode());
            }
        });

        return Boolean.TRUE;
    }


    @Transactional(rollbackFor = Exception.class)
    public OrderDto createCupOrder(OrderRequestDto orderRequestDto) {

        OrderDto orderDto = new OrderDto();

        Integer storeBussinessType = storeService.getStoreBussinessType(orderRequestDto.getStoreId());
        orderRequestDto.setBusinessType(storeBussinessType);

        OrderValidationResult createOrderValidationResult = orderValidationChain.validate(orderRequestDto);

        boolean ifValid = createOrderValidationResult.isIfValid();
        if(!ifValid){
            QYAssert.isFalse(createOrderValidationResult.getMessage());
        }
        TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO = orderRequestDto.getTdaDeliveryTimeRangeODTO();

        if (tdaDeliveryTimeRangeODTO != null) {
            orderDto.setTdaDeliveryInfo(orderRequestDto.getDeliveryBatch(),
                    orderRequestDto.getLogisticsCenterId(),
                    tdaDeliveryTimeRangeODTO.getLogisticsCenterName(),
                    orderRequestDto.getBusinessType(),
                    orderRequestDto.getDeliveryTimeRange());
        }

        if (SpringUtil.isNotEmpty(orderRequestDto.getItemsList())) {
            checkCommodityInfo(orderRequestDto);
            checkFillCargo(orderRequestDto);
            SpringUtil.copyProperties(orderRequestDto, orderDto);
            orderDto.setItems(new ArrayList<OrderItemDto>());

            // 获取B端库存依据
            Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
            orderRequestDto.getItemsList().forEach(dto -> {
                orderQuantityMap.put(Long.valueOf(dto.getProductId()), dto.getProductNum());
            });
            Map<Long, CommodityInventoryODTO> toBStockMap = getbStockMap(DateUtil.parseDate(orderRequestDto.getOrderTime(), "yyyy-MM-dd")
                    , orderRequestDto.getStoreId()
                    , orderQuantityMap,  null,orderRequestDto.getDeliveryTimeRange()
                    , orderRequestDto.getDeliveryBatch());

            List<String> productIds = orderRequestDto.getItemsList().stream().map(OrderItemRequestDto::getProductId).collect(toList());
            List<Commodity> commodities = commodityService.findCommodityByIdList(productIds.stream().map(Long::valueOf).collect(toList()));
            Map<Long, Commodity> commoditiesMap = commodities.stream().collect(toMap(Commodity::getId, Function.identity()));

            orderRequestDto.getItemsList().forEach(i -> {
                OrderItemDto orderItemDto = new OrderItemDto(i.getProductId(), BigDecimal.ZERO, BigDecimal.ZERO, i.getProductNum(), i.getRemark(), ProductTypeEnums.PRODUCT.getCode());
                CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(Long.valueOf(i.getProductId()));
                orderItemDto.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
                Commodity commodity = commoditiesMap.get(Long.valueOf(i.getProductId()));

                if (tdaDeliveryTimeRangeODTO != null) {
                    QYAssert.isTrue(i.getProductNum()
                            .remainder(new BigDecimal(commodity.getSalesBoxCapacity().toString()))
                            .compareTo(BigDecimal.ZERO) == 0, "商品编码" + i.getProductCode() + " 数量必须是箱规整倍数;");
                }

                //设置是否承重品以及包装规格
                orderItemDto.setIsWeight(commodity.getIsWeight());
                orderItemDto.setCommodityPackageSpec(commodity.getCommodityPackageSpec());
                orderItemDto.setSalesBoxCapacity(BigDecimal.valueOf(commodity.getSalesBoxCapacity()));
                orderItemDto.setCommodityThirdId(commodity.getCommodityThirdId());
                orderDto.addItem(orderItemDto);
            });
            //耗材商品校验
            this.checkConsumableProduct(orderDto);
            //凑整商品校验
            this.checkRoundedGoods(orderDto);
            this.buildProductPrice(orderDto);
            // 移除价格为空的商品
            this.processCommodityPrice(orderDto);
            QYAssert.isTrue(CollectionUtils.isNotEmpty(orderDto.getItems()), "有效商品不能为空!");

            //组装giftItem
            List<OrderItemDto> orderListGiftList = BeanCloneUtils.copyTo(orderDto.getItems(), OrderItemDto.class);
            orderDto.setGiftItems(orderListGiftList);

            // 注意：orderDto.items包含原始商品、配比、配货、赠品商品信息
            //       orderDto.giftItems 只包含原始商品和赠品

            // 买赠
            this.processFreeBuy(orderDto.getStoreId().toString(), orderDto);

            //配货方案（和门店配货共用一套方法）
            orderService.unifyProcessDistribution(orderDto.getStoreId().toString(), orderDto,OrderLaunchTypeEnum.CUSTOMER_SERVICE);

            //处理配比
            this.processRatio(orderDto.getStoreId().toString(), orderDto);

            List<OrderItemDto> rationItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());
            List<OrderItemDto> giftList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.GIFT.getCode().equals(item.getType())).collect(Collectors.toList());
            // 如果配货或者赠品不为空
            if(CollectionUtils.isNotEmpty(rationItemList) || CollectionUtils.isNotEmpty(giftList)) {
                // 1.查询库存依据和库存数量
                Map<Integer, Map<Long, CommodityInventoryODTO>> inventoryMap = queryCommodityInventory(orderDto,null, orderRequestDto.getOrderTime(), toBStockMap);

                // 2.配货商品只能是非限量的
                // 3.赠品数量设置为大仓最大可用数量
                cupGiftAndRelationDeal(inventoryMap, orderDto);
            }

            // 对配比、配货商品统一进行特价拆行
            processPriceSplit(orderDto, orderRequestDto, null);

            QYAssert.isTrue(!orderDto.getGiftItems().isEmpty(), "有效商品不能为空 ...");
            QYAssert.isTrue(orderDto.giftAmount().compareTo(BigDecimal.ZERO) > 0, "订单金额必须大于0...");
            QYAssert.isTrue(storeService.matchBillCondition(orderDto.getStoreId(), orderDto.giftAmount()), "预付款余额不足,无法下单!");
            Order order = new Order();
            this.insertOrder(order, orderDto, OrderModeType.ORDER.getCode());
            Long orderId = order.getId();
            /***
             * 调用大仓冻结接口
             */
            Map<String, String> storageUnderStockMap = this.execWarehouseFreezeInventory(orderDto, orderRequestDto, order, toBStockMap, false, "一期订单创建");
            if (SpringUtil.isNotEmpty(storageUnderStockMap)) {
                // todo报错就将当前订单删除
                orderService.deleteOrder(orderId);
                return new OrderDto(storageUnderStockMap);
            }

            Order kafkaOrder = null;
            try {
                this.saveOrder(order, orderDto, OrderModeType.ORDER.getCode(),orderRequestDto.getUserId(),orderRequestDto.getUserName());
                orderDto.setId(orderId);
                kafkaOrder = this.saveGiftOrder(orderDto, orderId, orderRequestDto.getUserId());
                orderDto.setOrderNo(order.getOrderCode());

                Boolean preStore = storeRechargeService.isPreStore(order.getStoreId());
                if (BooleanUtils.isTrue(preStore)) {
                    //扣款
                    StoreDeductionBO deductionBO = StoreDeductionBO.builder()
                            .orderCode(order.getOrderCode())
                            .orderId(order.getId())
                            .orderAmount(order.getOrderAmount())
                            .storeId(order.getStoreId())
                            .tradeCode(order.getOrderCode())
                            .tradeTime(new Date())
                            .orderTime(order.getOrderTime())
                            .billType(StoreBillTypeEnums.PC_DEDUCTION.getCode())
                            .remark("<--PC扣款：" + order.getOrderCode() + " -->")
                            .userId(orderRequestDto.getUserId())
                            .build();
                    storeRechargeService.storeDeduction(deductionBO);
                }

                updateGiftTotalLimitQuantity(orderDto);
            } catch (Exception e) {
                log.warn("创建订单失败error:{}", e);
                Boolean flag;
                if (tdaDeliveryTimeRangeODTO != null) {
                    flag = this.processWarehouseUnfreezeInventory(orderId, DeliveryOrderTypeEnums.TD_SALE.getCode());
                } else {
                    flag = this.processWarehouseUnfreezeInventory(orderId, DeliveryOrderTypeEnums.SALE.getCode());
                }

                if (flag != null && !flag.booleanValue()) {
                    log.warn("创建订单失败,大仓库存解冻失败 订单id：{}、订单号：{}", orderId, order.getOrderCode());
                }
                QYAssert.isTrue(false, "订单创建失败");
            }

            Order finalKafkaOrder = kafkaOrder;
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {

                    printerOrder(orderDto.getUserId(), orderId,orderRequestDto.getPrintType());
                    orderRequestDto.setOrderId(orderId);

                    //发送统计查询消息
                    orderAsyncKafkaService.sendKafkaSaveOrderMessage(finalKafkaOrder);
                    
                    //向物流系统发送创建订单消息
                    if(isNeedLogisticsTransPortByBusinessType(order.getBusinessType())){
                        TdaOrderSyncTmsIDTO tdaOrderSyncTmsIDTO = TdaOrderSyncTmsIDTO.forOrder(orderId, XdaOrderProcessStatusEunm.WAITING_PICK.getCode());
                        tdaOrderService.tdaOrderSyncToTms(tdaOrderSyncTmsIDTO);
                    }


                    //维护特价限购记录表
                    orderLimitQuantityService.saveOrderLimitQuantity(orderId,1,false);

                }
            });
        }
        return orderDto;
    }

    public boolean isNeedLogisticsTransPortByBusinessType(Integer businessType){
        return Objects.equals(businessType,BusinessTypeEnums.TD_SALE.getCode()) ||
                Objects.equals(businessType,BusinessTypeEnums.PLAN_SALE.getCode()) ||
                Objects.equals(businessType,BusinessTypeEnums.B_COUNTRY.getCode());
    }
    /**
     * 统一处理特价拆行,处理orderItems和orderGiftItems
     * @param orderDto
     * @param orderRequestDto
     */
    private void processPriceSplit(OrderDto orderDto, OrderRequestDto orderRequestDto,List<Commodity> commoditylist){
        List<OrderItemDto> giftItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.GIFT.getCode().equals(item.getType())).collect(Collectors.toList());
        List<OrderItemDto> normalItemList = orderDto.getItems().stream().filter(item -> !ProductTypeEnums.GIFT.getCode().equals(item.getType())).collect(Collectors.toList());

        // 非赠品商品重新查询特价、赋值
        List<Long> commodityIdList = normalItemList.stream().map(item -> Long.valueOf(item.getProductId())).collect(Collectors.toList());
        // 查询商品产品价格方案价格、并且赋值了特价
        List<ProductPriceDto> ppList = this.productPrice(commodityIdList, orderRequestDto.getStoreId() + "", orderRequestDto.getOrderTime());
        Map<String, ProductPriceDto> productPriceMap = ppList.stream().collect(toMap(ProductPriceDto::getProductId, Function.identity()));

        // 商品特价余量map,用于特价拆行。依次减数量
        Map<String, BigDecimal> specialRemainQuantityMap = new HashMap<>();
        ppList.forEach(productPriceDto -> {
            if(productPriceDto.isSpecialFlag()){
                BigDecimal specialQuantity = productPriceDto.getAvailableStoreLimit().min(productPriceDto.getAvailableTotalLimit());
                specialRemainQuantityMap.put(productPriceDto.getProductId(), specialQuantity);
            }
        });

        // 查询商品信息,主要取销售箱规。用于特价拆行
        List<Commodity> commodityList = commodityService.findCommodityByIdList(commodityIdList);
        Map<Long, Commodity> commodityMap = commodityList.stream().collect(toMap(Commodity::getId, Function.identity()));

        // items 最终保存到t_order_list_gift表
        List<OrderItemDto> items = new ArrayList<>();
        // giftItems 最终保存到t_order_list表
        List<OrderItemDto> giftItems = new ArrayList<>();

        // 1.对进行配比、配货之后的进行特价拆行，主要处理orderItems
        orderItemsSplitPrice(normalItemList, productPriceMap, specialRemainQuantityMap, commodityMap, items, orderDto.getStoreId(), commoditylist);

        // 2.对原始订单商品进行判断,是否需要特价拆行.主要处理orderGiftItems
        orderGiftItemsSplitPrice(orderDto, items, productPriceMap, commodityMap, giftItems, commoditylist);

        //3.赠品添加
        if(CollectionUtils.isNotEmpty(giftItemList)){
            items.addAll(giftItemList);
            giftItems.addAll(giftItemList);
        }

        orderDto.setItems(items);
        orderDto.setGiftItems(giftItems);
    }

    /**
     * 对原始订单商品进行判断,是否需要特价拆行.主要处理orderGiftItems
     * @param orderDto
     * @param items  最终拆成的(特价拆行)商品明细,上一步拆的结果. 最终保存到t_order_list_gift表
     * @param productPriceMap  产品价格方案信息，商品的原价，特价
     * @param commodityMap  商品信息，主要获取销售箱规，用于计算特价拆行
     * @param giftItems  最终保存到t_order_list表
     * @param commoditylist  修改前原订单的商品信息，价格、数量
     */
    private void orderGiftItemsSplitPrice(OrderDto orderDto, List<OrderItemDto> items,
                                          Map<String, ProductPriceDto> productPriceMap,
                                          Map<Long, Commodity> commodityMap,
                                          List<OrderItemDto> giftItems, List<Commodity> commoditylist) {

        // 修改前原订单的商品信息，价格、数量
        Map<Long, Commodity> fromOrderMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(commoditylist)) {
            fromOrderMap = commoditylist.stream().collect(toMap(Commodity::getId, Function.identity()));
        }

        List<OrderItemDto> specialItemList = items.stream().filter(item -> ProductTypeEnums.PRODUCT.getCode().equals(item.getType())
                        && item.isSpecialFlag()).collect(Collectors.toList());
        Map<String, OrderItemDto> specialItemMap = specialItemList.stream().collect(toMap(OrderItemDto::getProductId, Function.identity()));

        //获取OrderListGift中配比的正常商品用于设置OrderList中正常商品的promotionId
        Map<String,Long> normalProductItemPromotionIdMap = items.stream()
                .filter(item -> ProductTypeEnums.PRODUCT.getCode().equals(item.getType()))
                .collect(HashMap::new,(k,v)->k.put(v.getProductId(),v.getPromotionId()),HashMap::putAll);

        for(OrderItemDto itemDto : orderDto.getGiftItems()) {
            // 特价方案id 先初始化空items = {ArrayList@28896}  size = 1
            itemDto.setPricePromotionId(null);

            ProductPriceDto productPriceDto = productPriceMap.get(itemDto.getProductId());
            //产品价格方案不存在，就取修改之前的商品价格。如果价格都是空就过滤
            if(productPriceDto == null){
                if(fromOrderMap.containsKey(Long.parseLong(itemDto.getProductId()))) {
                    Commodity commodity = fromOrderMap.get(Long.parseLong(itemDto.getProductId()));

                    itemDto.setOriginalPrice(commodity.getProductPrice());
                    itemDto.updatePrice(commodity.getProductPrice());
                    giftItems.add(itemDto);
                }else {
                    log.warn("客户: {} 商品：{}，价格方案不存在", orderDto.getStoreId(), itemDto.getProductId());
                    continue;
                }
            }else {
                itemDto.setOriginalPrice(productPriceDto.getOriginalPrice());
                itemDto.setPromotionId(normalProductItemPromotionIdMap.get(itemDto.getProductId()));
                OrderItemDto specialItem = specialItemMap.get(itemDto.getProductId());
                if(specialItem != null){
                    BigDecimal salesBoxCapacity = BigDecimal.valueOf(commodityMap.get(Long.parseLong(itemDto.getProductId())).getSalesBoxCapacity());

                    // 原始商品应该享受特价的数量,享受特价的数量是销售箱规的倍数
                    BigDecimal specialProductNum = salesBoxCapacity.multiply(itemDto.getProductNum().divide(salesBoxCapacity,0, RoundingMode.FLOOR));

                    // 算出原商品可享受特价的数量
                    specialProductNum = specialProductNum.min(specialItem.getProductNum());

                    // 没有享受特价的数量
                    BigDecimal normalQuantity = itemDto.getProductNum().subtract(specialProductNum);

                    if(specialProductNum.compareTo(BigDecimal.ZERO) > 0){
                        OrderItemDto specialOrderItemDto = BeanCloneUtils.copyTo(itemDto,OrderItemDto.class);
                        specialOrderItemDto.updatePrice(specialItem.getPrice());
                        specialOrderItemDto.setProductNum(specialProductNum);
                        // 设置享受的特价方案id
                        specialOrderItemDto.setSpecialFlag(true);
                        specialOrderItemDto.setPricePromotionId(productPriceDto.getPricePromotionId());
                        giftItems.add(specialOrderItemDto);
                    }

                    if(normalQuantity.compareTo(BigDecimal.ZERO) > 0 ){
                        OrderItemDto normalOrderItemDto = BeanCloneUtils.copyTo(itemDto,OrderItemDto.class);
                        normalOrderItemDto.updatePrice(productPriceDto.getOriginalPrice());
                        normalOrderItemDto.setProductNum(normalQuantity);
                        giftItems.add(normalOrderItemDto);
                    }

                }else {
                    itemDto.updatePrice(productPriceDto.getOriginalPrice());
                    giftItems.add(itemDto);
                }
            }
        }
    }

    /**
     * 对进行配比、配货之后的进行特价拆行，主要处理orderItems
     * @param normalItemList 非赠品，包含正常商品、配货商品
     * @param productPriceMap 产品价格方案信息，商品的原价，特价
     * @param specialRemainQuantityMap  B端特价：商品特价余量map,用于特价拆行
     * @param commodityMap  商品信息，主要获取销售箱规，用于计算特价拆行
     * @param items  最终拆成的(特价拆行)商品明细,最终保存到t_order_list_gift表
     * @param storeId
     * @param commoditylist  修改前原订单的商品信息，价格、数量
     */
    private void orderItemsSplitPrice(List<OrderItemDto> normalItemList, Map<String, ProductPriceDto> productPriceMap,
                                      Map<String, BigDecimal> specialRemainQuantityMap,
                                      Map<Long, Commodity> commodityMap,
                                      List<OrderItemDto> items,
                                      Long storeId, List<Commodity> commoditylist) {

        // 修改前原订单的商品信息，价格、数量
        Map<Long, Commodity> fromOrderMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(commoditylist)) {
           fromOrderMap = commoditylist.stream().collect(toMap(Commodity::getId, Function.identity()));
        }

        for(OrderItemDto itemDto : normalItemList) {
            // 特价方案id 先初始化空
            itemDto.setPricePromotionId(null);

            ProductPriceDto productPriceDto = productPriceMap.get(itemDto.getProductId());
            // 产品价格方案不存在，就取修改之前的商品价格。如果价格都是空就过滤
            if(productPriceDto == null){
                if(fromOrderMap.containsKey(Long.parseLong(itemDto.getProductId()))) {
                    Commodity commodity = fromOrderMap.get(Long.parseLong(itemDto.getProductId()));

                    itemDto.setOriginalPrice(commodity.getProductPrice());
                    itemDto.updatePrice(commodity.getProductPrice());
                    itemDto.setSpecialFlag(false);
                    items.add(itemDto);
                }else {
                    log.warn("客户: {} 商品：{}，价格方案不存在", storeId, itemDto.getProductId());
                    continue;
                }
            }else {

                itemDto.setOriginalPrice(productPriceDto.getOriginalPrice());
                if(specialRemainQuantityMap.containsKey(itemDto.getProductId())){
                    BigDecimal specialQuantity;
                    BigDecimal normalQuantity;
                    BigDecimal productNum = itemDto.getProductNum();
                    BigDecimal specialRemainQuantity = specialRemainQuantityMap.get(itemDto.getProductId());
                    // 如果可用余量大于0,进行特价拆行
                    if(specialRemainQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal salesBoxCapacity = BigDecimal.valueOf(commodityMap.get(Long.parseLong(itemDto.getProductId())).getSalesBoxCapacity());

                        // 根据销售箱规算出满足箱规的最大可用剩余数量
                        BigDecimal availableQuantity = salesBoxCapacity.multiply(specialRemainQuantity.divide(salesBoxCapacity,0, RoundingMode.FLOOR));
                        // 根据销售箱规算出满足箱规的最大可用的原商品数量
                        BigDecimal productNumSalesBoxCapacity = salesBoxCapacity.multiply(productNum.divide(salesBoxCapacity,0,RoundingMode.FLOOR));

                        specialQuantity = availableQuantity.min(productNumSalesBoxCapacity);
                        normalQuantity = productNum.subtract(specialQuantity);

                        if(specialQuantity.compareTo(BigDecimal.ZERO) > 0){
                            OrderItemDto specialOrderItemDto = BeanCloneUtils.copyTo(itemDto,OrderItemDto.class);
                            specialOrderItemDto.updatePrice(productPriceDto.getPrice());
                            specialOrderItemDto.setProductNum(specialQuantity);

                            // 设置享受的特价方案id
                            specialOrderItemDto.setSpecialFlag(true);
                            specialOrderItemDto.setPricePromotionId(productPriceDto.getPricePromotionId());
                            items.add(specialOrderItemDto);
                        }

                        if(normalQuantity.compareTo(BigDecimal.ZERO) > 0 ){
                            OrderItemDto normalOrderItemDto = BeanCloneUtils.copyTo(itemDto,OrderItemDto.class);
                            normalOrderItemDto.updatePrice(productPriceDto.getOriginalPrice());
                            normalOrderItemDto.setProductNum(normalQuantity);
                            normalOrderItemDto.setSpecialFlag(false);
                            items.add(normalOrderItemDto);
                        }

                        // 特价剩余量依次减下去
                        specialRemainQuantityMap.put(itemDto.getProductId(), specialRemainQuantity.subtract(specialQuantity));
                    }else {
                        itemDto.updatePrice(productPriceDto.getOriginalPrice());
                        itemDto.setSpecialFlag(false);
                        items.add(itemDto);
                    }
                }else {
                    itemDto.updatePrice(productPriceDto.getOriginalPrice());
                    itemDto.setSpecialFlag(false);
                    items.add(itemDto);
                }
            }
        }
    }


    private void updateGiftTotalLimitQuantity(OrderDto orderDto){
        List<OrderItemDto> giftItemList = orderDto.getItems().stream().filter(item -> Objects.equals(ProductTypeEnums.GIFT.getCode(), item.getType()))
                .collect(toList());
        if(SpringUtil.isNotEmpty(giftItemList)){
            List<GiftLimitQuantitySaveIDTO> saveIDTOList = giftItemList.stream().map(giftItem -> {
                Long commodityId = Long.parseLong(giftItem.getProductId());
                BigDecimal totalQuantity = giftItem.getProductNum();
                Long giftModelId = giftItem.getGiftModelId();
                Long orderId = orderDto.getId();
                GiftLimitQuantitySaveIDTO saveIDTO = new GiftLimitQuantitySaveIDTO();
                saveIDTO.setOrderId(orderId);
                saveIDTO.setTotalQuantity(totalQuantity);
                saveIDTO.setGiftPrommotionId(giftModelId);
                saveIDTO.setCommodityId(commodityId);
                return saveIDTO;
            }).collect(toList());
            giftLimitService.saveGiftLimitQuantity(saveIDTOList);
        }
    }

    private void processDistribution(OrderDto orderDto) {
        com.pinshang.qingyun.order.vo.order.OrderDto orderDtoAdapter = BeanCloneUtils.copyTo(orderDto, com.pinshang.qingyun.order.vo.order.OrderDto.class);
        orderService.unifyProcessDistribution(orderDto.getStoreId().toString(), orderDtoAdapter,OrderLaunchTypeEnum.CUSTOMER_SERVICE);
        //将上一步经过配货处理的门店下单的明细OrderItemDto过滤出获得的配货品，同时转换为客服下单的OrderItemDto并添加到客服下单的orderDto中
        List<com.pinshang.qingyun.order.vo.order.OrderItemDto> distributionOrderItemList = orderDtoAdapter.getItems().stream().filter(orderItemDto -> Objects.equals(orderItemDto.getType(), ProductTypeEnums.RATION.getCode())).collect(toList());
        List<OrderItemDto> distributionOrderItemDtos = BeanCloneUtils.copyTo(distributionOrderItemList, OrderItemDto.class);
        distributionOrderItemDtos.forEach(
                distributionOrderItemDto -> orderDto.addItem(BeanCloneUtils.copyTo(distributionOrderItemDto,OrderItemDto.class))
        );
    }


    /**
     *  配货商品只能是非限量的
     *  赠品数量设置为大仓最大可用数量
     * @param commodityInventoryMap
     * @param orderDto
     */
    private void cupGiftAndRelationDeal(Map<Integer, Map<Long, CommodityInventoryODTO>> commodityInventoryMap, OrderDto orderDto) {
        //普通库存信息
        Map<Long, CommodityInventoryODTO> normalCommodityInventoryMap = commodityInventoryMap.get(ProductTypeEnum.NORMAL.getCode());
        //赠品库存信息
        Map<Long, CommodityInventoryODTO> giftCommodityInventoryMap = commodityInventoryMap.get(ProductTypeEnum.GIFT.getCode());
        Map<Long, BigDecimal> giftStockMap = new HashMap<>();
        if(giftCommodityInventoryMap != null && !giftCommodityInventoryMap.isEmpty()) {
            giftCommodityInventoryMap.forEach((k, v) -> {
                giftStockMap.put(Long.valueOf(k), v.getInventoryQuantity());
            });
        }

        orderDto.setItems(
                orderDto.getItems().stream().filter(i -> {
                    boolean product = ProductTypeEnums.PRODUCT.getCode().equals(i.getType());
                    boolean gift = ProductTypeEnums.GIFT.getCode().equals(i.getType());
                    boolean ration = ProductTypeEnums.RATION.getCode().equals(i.getType());
                    Long commodityId = Long.valueOf(i.getProductId());

                    // 这里只给赠品或者配货赋值预售和库存依据
                    CommodityInventoryODTO commodityInventoryODTO;
                    if(gift) {
                        commodityInventoryODTO = giftCommodityInventoryMap.get(commodityId);
                    }else {
                        commodityInventoryODTO = normalCommodityInventoryMap.get(commodityId);
                    }

                    i.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());

                    // 赠品数量不足就赠送当前最大数量
                    BigDecimal giftInventoryQuantity = giftStockMap.get(commodityId);
                    if(gift){
                        if(giftInventoryQuantity != null) {
                            if(giftInventoryQuantity.compareTo(BigDecimal.ZERO) <= 0){
                                i.setProductNum(BigDecimal.ZERO);
                            }else {
                                if(giftInventoryQuantity.compareTo(i.getProductNum()) <= 0){
                                    i.setProductNum(giftInventoryQuantity);
                                }
                                giftInventoryQuantity = giftInventoryQuantity.subtract(i.getProductNum());
                                giftStockMap.put(commodityId, giftInventoryQuantity);
                            }
                        }else {
                            i.setProductNum(BigDecimal.ZERO);
                        }
                    }
                    return product || (gift &&  i.getProductNum().compareTo(BigDecimal.ZERO) > 0 ) || (ration && StockTypeEnum.UN_LIMIT.getCode() == i.getStockType());
                }).collect(Collectors.toList())
        );
    }


    /**
     * 处理配比
     * 如果商品列表中存在耗材商品 配比时 忽略耗材商品
     */
    private void processRatio(String storeId, OrderDto orderDto) {
        if (StringUtils.isNotBlank(storeId)) {

            List<PromotionSale> psList = promotionSaleService.findRatioByStoreId(Long.valueOf(storeId),orderDto.getOrderTime());
            if (SpringUtil.isNotEmpty(psList)) {
                // 获取所有耗材商品
                List<String> commodityIdList = orderDto.getItems().stream().map(OrderItemDto::getProductId).collect(Collectors.toList());
                List<String> consumableCommodityIdList = consumableLimitService
                        .findByStoreIdAndCommodityIds(Long.valueOf(storeId), commodityIdList.stream().map(Long::valueOf).collect(toList()))
                        .stream()
                        .map(ConsumableLimit::getCommodityId)
                        .map(String::valueOf).collect(toList());


                //多配比
                psList.forEach(p -> {
                    if (p.getSaleType().intValue() == 0) {//订单
                        BigDecimal orderTotalPrice = orderDto.getPreComputeUnLimitOrderTotalPrice();
                        if (orderTotalPrice.compareTo(BigDecimal.valueOf(p.getOrderReach())) >= 0) {
                            BigDecimal ratioVal = BigDecimal.valueOf(p.getOrderRate());
                            if (null != ratioVal && ratioVal.intValue() > 0) {
                                BigDecimal oneHundred = BigDecimal.valueOf(100);
                                orderDto.getItems().forEach(item -> {
                                    // 不限量的才会进行配比
                                    if (item.getType().intValue() == ProductTypeEnums.PRODUCT.getCode().intValue() && StockTypeEnum.UN_LIMIT.getCode().equals(item.getStockType())) {
                                        if (!consumableCommodityIdList.contains(item.getProductId())) {
                                            // 需要添加判断 是否为耗材商品
                                            BigDecimal productNum = BigDecimal.valueOf(item.getProductNum().multiply(ratioVal.divide(oneHundred)).intValue());
                                            item.setProductNum(item.getProductNum().add(productNum));
                                            item.setPromotionId(p.getId());
                                            //this.putMap(ratioCommodity, Long.valueOf(item.getProductId()), productNum);


                                        }
                                    }
                                });
                            }
                        }
                    }
                    if (p.getSaleType().intValue() == ProductTypeEnums.PRODUCT.getCode()) {//商品
                        List<PromotionSaleComm> pscList = promotionSaleCommService.findListByPromotionId(p.getId());
                        if (SpringUtil.isNotEmpty(pscList)) {
                            promotionSaleCommService.findSaleCommodityPrice(storeId, pscList);
                            BigDecimal orderTotalPrice = orderDto.getPreComputeUnLimitOrderTotalPrice();
                            if (orderTotalPrice.compareTo(BigDecimal.valueOf(p.getOrderReach())) >= 0) {
                                pscList.forEach(psc -> {
                                    BigDecimal ratioVal = BigDecimal.valueOf(psc.getSale());
                                    if (null != ratioVal && ratioVal.intValue() > 0) {
                                        BigDecimal oneHundred = BigDecimal.valueOf(100);
                                        orderDto.getItems().forEach(
                                                item -> {
                                                    // 不限量的才进行配比
                                                    if (item.getType().intValue() == ProductTypeEnums.PRODUCT.getCode().intValue()
                                                            && item.getProductId().equals(psc.getCommId().toString()) && StockTypeEnum.UN_LIMIT.getCode().equals(item.getStockType())) {
                                                        if (!consumableCommodityIdList.contains(item.getProductId())) {
                                                            BigDecimal number = item.getProductNum().multiply(ratioVal.divide(oneHundred));
                                                            if (null != psc.getLimes()) {//限量
                                                                if (number.compareTo(BigDecimal.valueOf(psc.getLimes())) == 1) {
                                                                    //超出限额  = maxNumber
                                                                    item.setProductNum(item.getProductNum().add((BigDecimal.valueOf(psc.getLimes()))));
                                                                    item.setPromotionId(p.getId());
                                                                    //this.putMap(ratioCommodity, Long.valueOf(item.getProductId()), BigDecimal.valueOf(psc.getLimes()));
                                                                } else {
                                                                    //不超出 =Number
                                                                    item.setProductNum(item.getProductNum().add((BigDecimal.valueOf(number.intValue()))));
                                                                    item.setPromotionId(p.getId());
                                                                    //this.putMap(ratioCommodity, Long.valueOf(item.getProductId()), BigDecimal.valueOf(number.intValue()));
                                                                }
                                                            } else {
                                                                item.setProductNum(item.getProductNum().add((BigDecimal.valueOf(number.intValue()))));
                                                                item.setPromotionId(p.getId());
                                                                //this.putMap(ratioCommodity, Long.valueOf(item.getProductId()), BigDecimal.valueOf(number.intValue()));
                                                            }
                                                        }
                                                    }
                                                });
                                    }
                                });
                            }
                        }
                    }
                });
            }
        }
    }



    private void putMap(Map<Long, BigDecimal> ratioCommodity, Long productId, BigDecimal productNum) {
        if (ratioCommodity.containsKey(productId)) {
            ratioCommodity.put(productId, ratioCommodity.get(productId).add(productNum));
            return;
        }
        ratioCommodity.put(productId, productNum);
    }


    /**
     * 配货方案
     */
    /*private void processDistribution(String storeId, OrderDto orderDto) {

        if (StringUtils.isNotBlank(storeId)) {

            List<PromotionStk> psList = promotionStkService.findDistributionByStoreId(Long.valueOf(storeId), orderDto.getOrderTime());
            if (SpringUtil.isNotEmpty(psList)) {
                //多配货方案
                for (PromotionStk p : psList) {
                    BigDecimal orderTotalPrice = orderDto.getUnLimitOrderTotalPrice();
                    // 3. 获取配货条件(阶梯可能有多个条件, 只取最合适的一个)
                    PromotionStkCodeOrder promotionStkCodeOrder = promotionStkCodeOrderService.getOnePromotionStkCodeOrder(p.getId(), orderTotalPrice);
                    if (promotionStkCodeOrder == null) {
                        continue;
                    }

                    List<PromotionStkComm> pscList = promotionStkCommService.findListByStkCodeId(promotionStkCodeOrder.getId());
                    promotionStkCommService.findStkCommodityPrice(storeId, pscList, orderDto.getOrderTime());//获取商品价格
                    pscList = promotionStkCommService.processPromotionStkComm(pscList); //移除无价格的商品
                    if (SpringUtil.isNotEmpty(pscList)) {
                        //累计赠送
                        if (promotionStkCodeOrder.getStkType().intValue() == 0) {
                            pscList.forEach(c -> {
                                //赠送次数
                                BigDecimal number = orderTotalPrice.divideToIntegralValue(BigDecimal.valueOf(promotionStkCodeOrder.getOrderReach()));
                                if (null != c && null != c.getLimes()) {//限量
                                    //累计赠送总数
                                    BigDecimal nowCountNumber = BigDecimal.valueOf(c.getNumber()).multiply(number);
                                    //超出限量
                                    if (nowCountNumber.compareTo(BigDecimal.valueOf(c.getLimes())) == 1) {
                                        orderDto.addItem(new OrderItemDto(c.getCommId().toString(), c.getOriginalPrice(), c.getCommodityPrice(),
                                                BigDecimal.valueOf(c.getLimes()), "促销配货", ProductTypeEnums.RATION.getCode()));
                                    } else {
                                        orderDto.addItem(new OrderItemDto(c.getCommId().toString(), c.getOriginalPrice(), c.getCommodityPrice(), nowCountNumber,
                                                "促销配货", ProductTypeEnums.RATION.getCode()));
                                    }
                                } else {
                                    if (number.compareTo(BigDecimal.ZERO) == 1) {
                                        orderDto.addItem(new OrderItemDto(c.getCommId().toString(), c.getOriginalPrice(), c.getCommodityPrice(), number
                                                .multiply(BigDecimal.valueOf(c.getNumber())), "促销配货", ProductTypeEnums.RATION.getCode()));
                                    } else {
                                        orderDto.addItem(new OrderItemDto(c.getCommId().toString(), c.getOriginalPrice(), c.getCommodityPrice(),
                                                BigDecimal.valueOf(c.getNumber()), "促销配货", ProductTypeEnums.RATION.getCode()));
                                    }
                                }
                            });
                        }
                        //赠送一次
                        if (promotionStkCodeOrder.getStkType().intValue() == 1) {
                            if (SpringUtil.isNotEmpty(pscList)) {
                                pscList.forEach(c -> {
                                    orderDto.addItem(new OrderItemDto(c.getCommId().toString(), c.getOriginalPrice(), c.getCommodityPrice(),
                                            BigDecimal.valueOf(c.getNumber()), "促销配货", ProductTypeEnums.RATION.getCode()));
                                });
                            }
                        }
                    }
                }
            }
        }


    }*/


    /**
     * 赠品方案
     * <!-- 赠品方案  享受多赠品方案 赠品不收费 商品金额0.00-->
     * <!-- 三类:订单金额,商品组合,商品组合金额-->
     * 经产品确认 如果赠品为耗材商品不做处理 2021/07/07
     */
    /*@Deprecated
    private void processGifts(String storeId, OrderDto orderDto) {

        if (StringUtils.isNotBlank(storeId)) {

            List<GiftModel> gmList = giftModelService.findGiftModelByStoreId(Long.valueOf(storeId),orderDto.getOrderTime());
            if (SpringUtil.isNotEmpty(gmList)) {
                //支持多赠品方案
                gmList.forEach(g -> {
                    //赠品类型 1-订单金额,2-商品数量,3-商品金额
                    if (GiftModelTypeEnum.ORDERAMOUT.getCode().intValue() == g.getGiftModelType().intValue()) {//订单金额
                        BigDecimal orderTotalPrice = this.getOrderTotalPrice(orderDto);

                        // 获取梯度里面合适的一个
                        GiftModelCondition gmc = getOneGiftModelCondition(g.getId(), orderTotalPrice);
                        if (null != gmc && null != gmc.getConditionValue() && gmc.getConditionValue().compareTo(BigDecimal.ZERO) > 0) {
                            if (orderTotalPrice.compareTo(gmc.getConditionValue()) >= 0) {
                                if (gmc.getConditionType() != null) {
                                    List<GiftProduct> gpList = giftProductService.findGiftProductListByGiftModelConditionId(gmc.getId());
                                    if (gmc.getConditionType().intValue() == GiftModelConditionTypeEnum.GIVE_ONE.getCode().intValue()) {//赠送一次
                                        if (SpringUtil.isNotEmpty(gpList)) {
                                            gpList.forEach(p -> {
                                                orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p.getCommodityNumber(), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                            });
                                        }
                                    }
                                    if (gmc.getConditionType().intValue() == GiftModelConditionTypeEnum.CUMULATIVE_GIVE.getCode().intValue()) {//累计赠送
                                        if (SpringUtil.isNotEmpty(gpList)) {
                                            gpList.forEach(p -> {
                                                BigDecimal number = orderTotalPrice.divideToIntegralValue(gmc.getConditionValue());
                                                if (null != p && null != p.getCommodityMaxNumber()) {//限量
                                                    if (number.compareTo(p.getCommodityMaxNumber()) == 1) {
                                                        //超出限额  = maxNumber
                                                        orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p.getCommodityMaxNumber(), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                    } else {
                                                        //不超出 =Number
                                                        BigDecimal totalNumber = p.getCommodityNumber().multiply(number);

                                                        if (totalNumber.compareTo(p.getCommodityMaxNumber()) == 1) {//超出限额
                                                            orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p.getCommodityMaxNumber(), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                        } else {
                                                            orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, totalNumber, "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                        }
                                                    }
                                                } else {
                                                    if (number.compareTo(BigDecimal.ZERO) == 1) {
                                                        orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, number.multiply(p.getCommodityNumber()), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                    } else {
                                                        orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p.getCommodityNumber(), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                    }
                                                }
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (GiftModelTypeEnum.COMMODITYNUMBER.getCode().intValue() == g.getGiftModelType().intValue()) {//商品数量
                        BigDecimal commodityNumber = this.getCommodityNumber(orderDto, g.getCommmdityCodes());
                        // 获取梯度里面合适的一个
                        GiftModelCondition gmc = getOneGiftModelCondition(g.getId(), commodityNumber);

                        if (null != gmc && null != gmc.getConditionValue() && gmc.getConditionValue().compareTo(BigDecimal.ZERO) > 0) {
                            if (commodityNumber.compareTo(gmc.getConditionValue()) >= 0) {
                                if (gmc.getConditionType() != null) {

                                    List<GiftProduct> gpList = giftProductService.findGiftProductListByGiftModelConditionId(gmc.getId());

                                    if (gmc.getConditionType().intValue() == GiftModelConditionTypeEnum.GIVE_ONE.getCode().intValue()) {//赠送一次
                                        if (SpringUtil.isNotEmpty(gpList)) {
                                            gpList.forEach(p -> {
                                                orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p.getCommodityNumber(),
                                                        "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                            });
                                        }
                                    }
                                    if (gmc.getConditionType().intValue() == GiftModelConditionTypeEnum.CUMULATIVE_GIVE.getCode().intValue()) {//累计赠送
                                        if (SpringUtil.isNotEmpty(gpList)) {
                                            gpList.forEach(p -> {
                                                BigDecimal number = commodityNumber.divideToIntegralValue(gmc.getConditionValue());
                                                if (null != p && null != p.getCommodityMaxNumber()) {//限量
                                                    if (number.compareTo(p.getCommodityMaxNumber()) == 1) {
                                                        //超出限额  = maxNumber
                                                        orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p
                                                                .getCommodityMaxNumber(), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                    } else {
                                                        //不超出 =Number
                                                        BigDecimal totalNumber = p.getCommodityNumber().multiply(number);

                                                        if (totalNumber.compareTo(p.getCommodityMaxNumber()) == 1) {//超出限额
                                                            orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p
                                                                    .getCommodityMaxNumber(), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                        } else {
                                                            orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, totalNumber,
                                                                    "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                        }
                                                    }
                                                } else {
                                                    if (number.compareTo(BigDecimal.ZERO) == 1) {
                                                        orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, number.multiply(p
                                                                .getCommodityNumber()), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                    } else {
                                                        orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p.getCommodityNumber(),
                                                                "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                    }
                                                }
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (GiftModelTypeEnum.COMMODITYAMOUT.getCode().intValue() == g.getGiftModelType().intValue()) {//商品金额
                        BigDecimal commodityTotalPrice = this.getCommodityTotalPrice(orderDto, g.getCommmdityCodes());
                        // 获取梯度里面合适的一个
                        GiftModelCondition gmc = getOneGiftModelCondition(g.getId(), commodityTotalPrice);
                        if (null != gmc && null != gmc.getConditionValue() && gmc.getConditionValue().compareTo(BigDecimal.ZERO) > 0) {
                            if (commodityTotalPrice.compareTo(gmc.getConditionValue()) >= 0) {
                                if (gmc.getConditionType() != null) {
                                    List<GiftProduct> gpList = giftProductService.findGiftProductListByGiftModelConditionId(gmc.getId());
                                    if (gmc.getConditionType().intValue() == GiftModelConditionTypeEnum.GIVE_ONE.getCode().intValue()) {//赠送一次
                                        if (SpringUtil.isNotEmpty(gpList)) {
                                            gpList.forEach(p -> {
                                                orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p.getCommodityNumber(),
                                                        "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                            });
                                        }
                                    }
                                    if (gmc.getConditionType().intValue() == GiftModelConditionTypeEnum.CUMULATIVE_GIVE.getCode().intValue()) {//累计赠送
                                        if (SpringUtil.isNotEmpty(gpList)) {
                                            gpList.forEach(p -> {
                                                BigDecimal number = commodityTotalPrice.divideToIntegralValue(gmc.getConditionValue());
                                                if (null != p && null != p.getCommodityMaxNumber()) {//限量
                                                    if (number.compareTo(p.getCommodityMaxNumber()) == 1) {
                                                        //超出限额  = maxNumber
                                                        orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p
                                                                .getCommodityMaxNumber(), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                    } else {
                                                        //不超出 =Number
                                                        BigDecimal totalNumber = p.getCommodityNumber().multiply(number);

                                                        if (totalNumber.compareTo(p.getCommodityMaxNumber()) == 1) {//超出限额
                                                            orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p
                                                                    .getCommodityMaxNumber(), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                        } else {
                                                            orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, totalNumber,
                                                                    "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                        }
                                                    }
                                                } else {
                                                    if (number.compareTo(BigDecimal.ZERO) == 1) {
                                                        orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, number.multiply(p
                                                                .getCommodityNumber()), "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                    } else {
                                                        orderDto.addItem(new OrderItemDto(p.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, p.getCommodityNumber(),
                                                                "促销赠品", ProductTypeEnums.GIFT.getCode()));
                                                    }
                                                }
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                });
            }
        }
    }*/


    /**
     * 处理买赠、统一调用B端促销方案的买赠促销
     */
    private void processFreeBuy(String storeId, OrderDto orderDto) {

        if (StringUtils.isNotBlank(storeId)) {

            FreeBuyIDTO freeBuyIDTO = new FreeBuyIDTO();
            freeBuyIDTO.setInvokeSource(0);
            freeBuyIDTO.setOrderTime(DateUtil.parseDate(orderDto.getOrderTime(),"yyyy-MM-dd"));
            freeBuyIDTO.setChannelType(MarketSourceTypeEnum.CUSTOMER.getCode());
            freeBuyIDTO.setShopId(Long.parseLong(storeId));
            List<FreeBuyCommodityIDTO> freeBuyCommodityIDTOList  = orderDto.getItems().stream().map(this::orderItemDto2FreeBuyCommodityIDTO).collect(toList());
            freeBuyIDTO.setFreeBuyCommodityIDTOList(freeBuyCommodityIDTOList);

            FreeBuyODTO freeBuyODTO = mtPromotionClient.freeBuy(freeBuyIDTO);
            //获取当日当前客户已定赠品商品数量
            List<Long> giftCommodityIdList = freeBuyODTO.getPromotionGroups()
                    .stream()
                    .filter(item -> SpringUtil.isNotEmpty(item.getGiftList()))
                    .flatMap(item -> item.getGiftList().stream())
                    .map(GiftCommodityODTO::getCommodityId).collect(toList());
            List<XdaCommodityLimit4AppODTO> xdaCommodityLimit4AppODTOList = this.getUsedQuantityCommodityByStoreIdMap(orderDto.getOrderTime(), giftCommodityIdList, Long.parseLong(storeId), ProductTypeEnums.GIFT.getCode(), null,orderDto.getId());
            Map<Long, Map<Long, BigDecimal>> promotionGiftCommodityQuantityMap = xdaCommodityLimit4AppODTOList.stream().collect(groupingBy(XdaCommodityLimit4AppODTO::getGiftModelId, toMap(XdaCommodityLimit4AppODTO::getCommodityId, XdaCommodityLimit4AppODTO::getTotalQuantity)));

            //获取促销活动下赠送的商品已送数量
            Map<Long, Map<Long, BigDecimal>> giftLimitTotalQuantityMap = this.getTotalUsedQuantityCommodityByPromotionIdMap(freeBuyODTO,orderDto.getId());

            //判断促销赠品
            freeBuyODTO.getPromotionGroups().forEach(
                    item->{
                        if(SpringUtil.isNotEmpty(item.getGiftList())){

                            Map<Long,BigDecimal> giftCommodityQuantityMap = Maps.newHashMap();
                            if(promotionGiftCommodityQuantityMap.containsKey(item.getPromotionId())){
                                giftCommodityQuantityMap = promotionGiftCommodityQuantityMap.get(item.getPromotionId());
                            }

                            Map<Long,BigDecimal> giftCommodityTotalLimitQuantityMap = Maps.newHashMap();
                            if(giftLimitTotalQuantityMap.containsKey(item.getPromotionId())){
                                giftCommodityTotalLimitQuantityMap = giftLimitTotalQuantityMap.get(item.getPromotionId());
                            }

                            for (GiftCommodityODTO giftCommodityODTO : item.getGiftList()) {
                                FreeBuyGiftNumCalculator storeDailyMaxGiftNumCalculator = new FreeBuyGiftNumCalculator(Objects.isNull(giftCommodityODTO.getLimitNum())?null:new BigDecimal(giftCommodityODTO.getLimitNum()),giftCommodityQuantityMap,giftCommodityODTO.getCommodityId(),Objects.isNull(giftCommodityODTO.getNum())?null:new BigDecimal(giftCommodityODTO.getNum()));
                                BigDecimal storeDayMaxGiftNum = storeDailyMaxGiftNumCalculator.getMaxAvailableGiftNum();
                                FreeBuyGiftNumCalculator totalMaxGiftNumCalculator = new FreeBuyGiftNumCalculator(Objects.isNull(giftCommodityODTO.getTotalLimitNumber())?null:new BigDecimal(giftCommodityODTO.getTotalLimitNumber()),giftCommodityTotalLimitQuantityMap,giftCommodityODTO.getCommodityId(),Objects.isNull(giftCommodityODTO.getNum())?null:new BigDecimal(giftCommodityODTO.getNum()));
                                BigDecimal totalMaxGiftNum = totalMaxGiftNumCalculator.getMaxAvailableGiftNum();
                                BigDecimal giftNum = storeDayMaxGiftNum.min(totalMaxGiftNum);
                                orderDto.addItem(new OrderItemDto(giftCommodityODTO.getCommodityId().toString(), BigDecimal.ZERO, BigDecimal.ZERO, giftNum,
                                                "促销赠品", ProductTypeEnums.GIFT.getCode(),item.getPromotionId()));
                            }
                        }
                    }
            );

        }
    }

    private Map<Long, Map<Long, BigDecimal>> getTotalUsedQuantityCommodityByPromotionIdMap(FreeBuyODTO freeBuyODTO,Long orderId){
        //获取促销活动下赠送的商品已送数量
        List<GiftLimitQuantityQueryDetailIDTO> detailList = freeBuyODTO.getPromotionGroups().stream()
                .filter(promotionGroup->SpringUtil.isNotEmpty(promotionGroup.getGiftList()))
                .map(
                        promotionGroup->{
                            Long promotionId = promotionGroup.getPromotionId();
                            List<Long> commodityIdList = promotionGroup.getGiftList().stream().map(GiftCommodityODTO::getCommodityId).collect(toList());
                            return new GiftLimitQuantityQueryDetailIDTO(promotionId,commodityIdList);
                        }
                ).collect(toList());
        Map<Long, Map<Long, BigDecimal>> giftLimitTotalQuantityMap = Maps.newHashMap();

        if(SpringUtil.isNotEmpty(detailList)){
            GiftLimitQuantityQueryIDTO giftLimitQuantityQueryIDTO = new GiftLimitQuantityQueryIDTO();
            giftLimitQuantityQueryIDTO.setDetailList(detailList);
            giftLimitQuantityQueryIDTO.setOrderId(orderId);
            List<GiftLeftQuantityODTO> giftLeftQuantityODTOList = giftLimitService.queryGiftLeftQuantityList(giftLimitQuantityQueryIDTO);
            giftLimitTotalQuantityMap = giftLeftQuantityODTOList.stream().collect(groupingBy(GiftLeftQuantityODTO::getGiftPrommotionId, toMap(GiftLeftQuantityODTO::getCommodityId, GiftLeftQuantityODTO::getTotalQuantity)));
        }

        return giftLimitTotalQuantityMap;
    }


    private List<XdaCommodityLimit4AppODTO> getUsedQuantityCommodityByStoreIdMap(String orderTime, List<Long> commodityIdList, Long storeId, Integer orderListType, Long promotionId,Long orderId){
        List<XdaCommodityLimit4AppODTO> orderCommodityLimitODTOS = Lists.newArrayList();
        if (SpringUtil.isNotEmpty(commodityIdList)) {
            if(Objects.nonNull(storeId)){
                orderCommodityLimitODTOS = orderListMapper.queryCommodityLimitByStoreId(orderTime,storeId,orderListType,commodityIdList,promotionId,orderId);
            }else{
                orderCommodityLimitODTOS = orderListMapper.queryCommodityLimit(orderTime,commodityIdList,promotionId);
            }
        }
        return orderCommodityLimitODTOS;
    }

    private FreeBuyCommodityIDTO orderItemDto2FreeBuyCommodityIDTO(OrderItemDto orderItemDto) {
        FreeBuyCommodityIDTO freeBuyCommodityIDTO  = new FreeBuyCommodityIDTO();
        freeBuyCommodityIDTO.setCommodityId(Long.parseLong(orderItemDto.getProductId()));
        freeBuyCommodityIDTO.setIsSpecial(orderItemDto.isSpecialFlag());
        freeBuyCommodityIDTO.setProductNum(orderItemDto.getProductNum());
        freeBuyCommodityIDTO.setPrice(orderItemDto.getPrice());
        freeBuyCommodityIDTO.setCategoryId(orderItemDto.getCommodityThirdId());
        return freeBuyCommodityIDTO;
    }

    //统计商品总金额
    private BigDecimal getCommodityTotalPrice(OrderDto orderDto, String commmdityCodes) {
        BigDecimal result = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(commmdityCodes)) {
            List<String> codes = Arrays.asList(commmdityCodes.split(","));
            List<Commodity> commodityList = new ArrayList<Commodity>();
            if (SpringUtil.isNotEmpty(codes)) {
                codes.forEach(c -> {
                    Commodity commodity = commodityService.findCommodityByCommodityCode(c);
                    if (null != commodity) {
                        commodityList.add(commodity);
                    }
                });
            }
            if (SpringUtil.isNotEmpty(commodityList)) {
                for (Commodity c : commodityList) {
                    for (OrderItemDto i : orderDto.getItems()) {
                        if (c.getId().toString().equals(i.getProductId())) {
                            result = result.add(i.getPrice().multiply(i.getProductNum()));
                        }
                    }
                }
            }
        }
        return result;
    }

    //统计商品数量
    private BigDecimal getCommodityNumber(OrderDto orderDto, String commmdityCodes) {
        BigDecimal result = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(commmdityCodes)) {
            List<String> codes = Arrays.asList(commmdityCodes.split(","));
            List<Commodity> commodityList = new ArrayList<Commodity>();
            if (SpringUtil.isNotEmpty(codes)) {
                codes.forEach(c -> {
                    Commodity commodity = commodityService.findCommodityByCommodityCode(c);
                    if (null != commodity) {
                        commodityList.add(commodity);
                    }
                });
            }
            if (SpringUtil.isNotEmpty(commodityList)) {
                for (Commodity c : commodityList) {
                    for (OrderItemDto i : orderDto.getItems()) {
                        if (c.getId().toString().equals(i.getProductId())) {
                            result = result.add(i.getProductNum());
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 获取一个有效的赠品条件
     *
     * @param gmcList
     * @param conditionValueForOrder
     * @return
     */
    private GiftModelCondition getOneGiftModelCondition(List<GiftModelCondition> gmcList,BigDecimal conditionValueForOrder) {

        //二分查找阈值8
        if(gmcList.size() > BINARY_SEARCH_THRESHOLD){
            //此处不需要按照conditionValue升序排列，默认用户输入就是按升序排列，减少排序的性能消耗
//            gmcList.sort(Comparator.comparing(GiftModelCondition::getConditionValue));
            //使用二分查找，无需一个一个遍历
            int left = 0;
            int right = gmcList.size()-1;
            while(left <= right){
                int mid = left + (right-left)/2;
                GiftModelCondition midItem = gmcList.get(mid);
                if (conditionValueForOrder.compareTo(midItem.getConditionValue()) >= 0) {
                    left = mid + 1;
                }else{
                    right = mid -1;
                }
            }
            return left > 0 ? gmcList.get(left - 1): null;

        }else if(gmcList.size() >1){
            GiftModelCondition result = null;
            for (GiftModelCondition item : gmcList) {
                if (conditionValueForOrder.compareTo(item.getConditionValue()) >= 0) {
                    result = item;
                }
            }
            return result;
        }else{
            GiftModelCondition giftModelCondition = gmcList.get(0);
            if (conditionValueForOrder.compareTo(giftModelCondition.getConditionValue()) >= 0) {
                return giftModelCondition;
            }
        }

        return null;
    }

    //统计订单金额
    private BigDecimal getOrderTotalPrice(OrderDto orderDto) {
        return orderDto.amount();
    }

    private void checkFillCargo(OrderRequestDto orderRequestDto) {
        List<OrderWhite> whiteStores = orderWhiteService.findListByStoreId(orderRequestDto.getStoreId());
        if(SpringUtil.isEmpty(whiteStores)){
            List<Order> listOrder = this.findReplenishmentOrderListByParams(orderRequestDto.getStoreId(),
                    OrderModeType.REPLENISHMENT.getCode(),orderRequestDto.getOrderTime(),OrderStatusEnums.NORMAL.getCode());
            QYAssert.isTrue(SpringUtil.isEmpty(listOrder), "抱歉,该客户已经补货,无法继续下单!");
        }
    }

    private void printerOrder(Long userId, Long orderId) {
        OrderPrintIDTO orderPrintIDTO = new OrderPrintIDTO();
        orderPrintIDTO.setOrderIdList(Arrays.asList(orderId));
        orderPrintIDTO.setUserId(userId);
        try {
            printInfoClient.print(orderPrintIDTO);
        }catch (Exception e){
            log.warn("打印订单异常", e);
        }
    }

    private void printerOrder(Long userId, Long orderId,Integer printType) {
        if(null == printType || !printType.equals(1)){
            return;
        }
        OrderPrintIDTO orderPrintIDTO = new OrderPrintIDTO();
        orderPrintIDTO.setOrderIdList(Arrays.asList(orderId));
        orderPrintIDTO.setUserId(userId);
        try {
            printInfoClient.print(orderPrintIDTO);
        }catch (Exception e){
            log.warn("打印订单异常", e);
        }
    }

    private Order saveGiftOrder(OrderDto orderDto, Long orderId, Long userId) {
        Order kafkaOrder = orderService.findOrderById(orderId);
        List<OrderList> orderLists = new ArrayList<OrderList>();
        Boolean isTdaStore = tdaOrderService.isTdaStore(orderDto.getStoreId());
        orderDto.getItems().forEach(i -> {
            OrderList ol = new OrderList();
            OrderListGift gift = new OrderListGift();

            ol.setCommodityId(Long.valueOf(i.getProductId()));
            ol.setCommodityNum(i.getProductNum());
            ol.setType(i.getType());
            ol.setRemark(i.getRemark());
            ol.setCommodityPrice(i.getPrice());
            ol.setOrderId(kafkaOrder.getId());
            ol.setTotalPrice(i.amount());
            ol.setPricePromotionId(i.isSpecialFlag()?i.getPricePromotionId():null);

            SpringUtil.copyProperties(ol, gift);
            //order commodity sys --> start
            List<Commodity> commodityList = commodityService.findCommodityByIdList(Arrays.asList(ol.getCommodityId()));
            if(SpringUtil.isNotEmpty(commodityList)) {
                Commodity commodity = commodityList.get(0);
                ol.setCommodityCode(commodity.getCommodityCode());
                ol.setCommodityName(commodity.getCommodityName());
                ol.setCommoditySpec(commodity.getCommoditySpec());

            }
            ol.setOriginalPrice(i.getOriginalPrice());
            ol.setOriginalTotalPrice(i.originalAmount());
            ol.setIsWeight(i.getIsWeight());
            ol.setCommodityPackageSpec(i.getCommodityPackageSpec());
            ol.setPromotionId(i.getPromotionId());
            ol.setGiftModelId(i.getGiftModelId());
            orderLists.add(ol);
        });

        // 如果是通达，则称重品拆行
        if (isTdaStore) {
            List<OrderList> orderListAll = new ArrayList<>();
            for (OrderList orderList : orderLists) {
                if (orderList.getIsWeight() != null && orderList.getIsWeight().equals(1)) {
                    List<OrderList> splitOrderList = tdaWeightSplit(orderList, orderList.getCommodityPackageSpec());
                    orderListAll.addAll(splitOrderList);
                } else {
                    orderListAll.add(orderList);
                }
            }

            List<OrderListGift> giftList  = BeanCloneUtils.copyTo(orderListAll,OrderListGift.class);
            for (OrderListGift orderListGift : giftList) {
                orderListGiftMapper.insert(orderListGift);
            }
            List<OrderList> fillIdOrderLists = BeanCloneUtils.copyTo(giftList, OrderList.class);
            kafkaOrder.setOrderList(fillIdOrderLists);
        } else {
            List<OrderListGift> giftList  = BeanCloneUtils.copyTo(orderLists,OrderListGift.class);
            for (OrderListGift orderListGift : giftList) {
                orderListGiftMapper.insert(orderListGift);
            }
            List<OrderList> fillIdOrderLists = BeanCloneUtils.copyTo(giftList, OrderList.class);
            kafkaOrder.setOrderList(fillIdOrderLists);
        }

        kafkaOrder.setOrderAmount(orderDto.amount());

        return kafkaOrder;
    }

    /**
     * 通达称重品拆行
     *
     * @param orderList
     */
    private List<OrderList> tdaWeightSplit(OrderList orderList, BigDecimal commodityPackageSpec) {
        List<OrderList> splitOrderList = new ArrayList<>();
        Integer number = orderList.getCommodityNum().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP).intValue();

        // 称重品价格分摊问题,向下保留2位小数。最后一个称重品金额可能会多点
        BigDecimal oneTotalPrice = orderList.getTotalPrice().divide(new BigDecimal(number), 2, BigDecimal.ROUND_DOWN);
        BigDecimal originalTotalPrice = orderList.getOriginalPrice().multiply(orderList.getCommodityNum()).setScale( 2, BigDecimal.ROUND_DOWN);
        BigDecimal oneOriginalTotalPrice = originalTotalPrice.divide(new BigDecimal(number), 2, BigDecimal.ROUND_DOWN);

        for (int i = 0; i < number; i++) {
            OrderList saveOrderList = BeanCloneUtils.copyTo(orderList, OrderList.class);
            if (i < number - 1) {
                saveOrderList.setTotalPrice(oneTotalPrice);
                saveOrderList.setOriginalTotalPrice(oneOriginalTotalPrice);
                saveOrderList.setCommodityNum(commodityPackageSpec);
            } else {
                saveOrderList.setTotalPrice(orderList.getTotalPrice().subtract(oneTotalPrice.multiply(new BigDecimal(number - 1))));
                saveOrderList.setOriginalTotalPrice(originalTotalPrice.subtract(oneOriginalTotalPrice.multiply(new BigDecimal(number - 1))));
                saveOrderList.setCommodityNum(orderList.getCommodityNum().subtract(commodityPackageSpec.multiply(new BigDecimal(number - 1))));

            }

            splitOrderList.add(saveOrderList);
        }
        return splitOrderList;
    }

    private Long saveOrder(Order order, OrderDto orderDto, Integer modeType, Long userId,String userName) {
        orderDto.setOrderNo(order.getOrderCode());
        Long orderId = order.getId();
        List<OrderList> orderLists = new ArrayList<OrderList>();
        if (modeType.intValue() == OrderModeType.REPLENISHMENT.getCode().intValue()) {
            orderDto.getItems().forEach(i -> {
                OrderList list = new OrderList();
                list.setCommodityId(Long.valueOf(i.getProductId()));
                list.setCommodityNum(i.getProductNum());
                list.setType(i.getType());
                list.setRemark(i.getRemark());
                list.setCommodityPrice(i.getPrice());
                list.setOrderId(orderId);
                list.setTotalPrice(i.amount());
                List<Commodity> commodityList = commodityService.findCommodityByIdList(Arrays.asList(list.getCommodityId()));
                if(SpringUtil.isNotEmpty(commodityList)) {
                    Commodity commodity = commodityList.get(0);

                    list.setCommodityCode(commodity.getCommodityCode());
                    list.setCommodityName(commodity.getCommodityName());
                    list.setCommoditySpec(commodity.getCommoditySpec());
                    list.setCommodityRemark(commodity.getCommodityRemark());
                    list.setCommodityPackageKind(commodity.getCommodityPackageKind());

                }


                list.setOriginalPrice(i.getOriginalPrice());
                list.setOriginalTotalPrice(i.originalAmount());
                list.setPricePromotionId(i.isSpecialFlag()?i.getPricePromotionId():null);
                list.setPresaleStatus(0);
                orderLists.add(list);
                OrderListGift gift = new OrderListGift();
                SpringUtil.copyProperties(list, gift);
                orderListMapper.insert(list);
                long itemId = orderListGiftMapper.insert(gift);
                list.setId(itemId);
                i.setItemId(itemId);
            });
            order.setOrderList(orderLists);


//            }).start();
        } else {
            orderDto.getGiftItems().forEach(i -> {
                OrderList list = new OrderList();
                list.setCommodityId(Long.valueOf(i.getProductId()));
                list.setType(i.getType());
                list.setRemark(i.getRemark());
                list.setCommodityPrice(i.getPrice());
                list.setOrderId(orderId);
                list.setTotalPrice(i.amount());
                list.setOriginalPrice(i.getOriginalPrice());
                list.setOriginalTotalPrice(i.originalAmount());
                list.setIsWeight(i.getIsWeight());
                list.setCommodityPackageSpec(i.getCommodityPackageSpec());
                list.setCommodityNum(i.getProductNum());
                list.setPricePromotionId(i.isSpecialFlag()?i.getPricePromotionId():null);
                list.setPresaleStatus(0);
                list.setPromotionId(i.getPromotionId());
                list.setGiftModelId(i.getGiftModelId());

//                list.setCommodityNum(i.getProductNum());
                orderLists.add(list);
//                order.setOrderList(orderList);
            });

            // 如果是通达，则称重品拆行
            Boolean isTdaStore = tdaOrderService.isTdaStore(order.getStoreId());
            if (isTdaStore) {
                List<OrderList> orderListAll = new ArrayList<>();
                for (OrderList orderList : orderLists) {
                    if (orderList.getIsWeight() != null && orderList.getIsWeight().equals(1)) {
                        List<OrderList> splitOrderList = tdaWeightSplit(orderList, orderList.getCommodityPackageSpec());
                        orderListAll.addAll(splitOrderList);
                    } else {
                        orderListAll.add(orderList);
                    }
                }
                for (OrderList orderList : orderListAll) {
                    orderListMapper.insert(orderList);
                }
                order.setOrderList(orderListAll);
            } else {
                for (OrderList orderList : orderLists) {
                    orderListMapper.insert(orderList);
                }
                order.setOrderList(orderLists);
            }
        }
        this.createOrderMirror(order);
        orderHistoryMapper.insertSelective(OrderHistory.order_create(orderId, order.getOrderCode(), orderDto.getOrderTime(), userId, userName, order.getCreateTime()));

        if (modeType.intValue() == OrderModeType.REPLENISHMENT.getCode().intValue()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    //发送统计消息
                    orderAsyncKafkaService.sendKafkaSaveOrderMessage(order);
                }
            });
        }
        return orderId;
    }

    private void createOrderMirror(Order order) {

        Boolean isTdaStore = tdaOrderService.isTdaStore(order.getStoreId());

        Store store = storeService.findStoreByStoreId(order.getStoreId());
        OrderMirror orderMirror = new OrderMirror();
        orderMirror.setOrderId(order.getId());
        orderMirror.setOrderCode(order.getOrderCode());

        if (null != store) {
            String employeeName;
            if (null != store.getDeliverymanId() && !isTdaStore) {
                employeeName = employeeUserMapper.queryEmployeeUserByEmployeeId(store.getDeliverymanId());
                orderMirror.setDeliveryManId(store.getDeliverymanId());
                orderMirror.setDeliveryManName(employeeName);
            }
            if (null != store.getSalesmanId()) {
                employeeName = employeeUserMapper.queryEmployeeUserByEmployeeId(store.getSalesmanId());
                orderMirror.setSalesmanId(store.getSalesmanId());
                orderMirror.setSalesmanName(employeeName);
            }
            if (null != store.getSupervisorId()) {
                employeeName = employeeUserMapper.queryEmployeeUserByEmployeeId(store.getSupervisorId());
                orderMirror.setSupervisionId(store.getSupervisorId());
                orderMirror.setSupervisionName(employeeName);
            }
            if (null != store.getRegionManagerId()) {
                employeeName = employeeUserMapper.queryEmployeeUserByEmployeeId(store.getRegionManagerId());
                orderMirror.setRegionalManagerId(store.getRegionManagerId());
                orderMirror.setRegionalManagerName(employeeName);
            }
            if (null != store.getOfficeDirectorId()) {
                employeeName = employeeUserMapper.queryEmployeeUserByEmployeeId(store.getOfficeDirectorId());
                orderMirror.setDirectorId(store.getOfficeDirectorId());
                orderMirror.setDirectorName(employeeName);
            }
        }
        orderMirrorMapper.insertSelective(orderMirror);
    }

    /***
     * 大仓库存库冻结
     * @param orderDto
     * @param requestDto
     */
    private Map<String, String> execWarehouseFreezeInventory(OrderDto orderDto, OrderRequestDto requestDto, Order order, Map<Long, CommodityInventoryODTO> commodityInventoryMap, boolean needUnFreeze, String remark) {
        Map<Long, Commodity> commodityMap = getCommMap(orderDto);

        Store store = storeService.findStoreByStoreId(requestDto.getStoreId());
        WarehouseFreezeInventoryIDTO idto = new WarehouseFreezeInventoryIDTO();
        idto.setStoreId(requestDto.getStoreId());
        if (store != null) {
            idto.setStoreCode(store.getStoreCode());
            idto.setStoreName(store.getStoreName());
        }
        idto.setOrderTime(DateUtil.parseDate(requestDto.getOrderTime(), "yyyy-MM-dd"));
        idto.setDeliveryBatch(CloudDeliveryBatchTypeEnum.ZERO_BATCH.getCode());
        Integer storeBusinessType = storeService.getStoreBussinessType(requestDto.getStoreId());
        idto.setType(storeBusinessType);
        if (!Objects.equals(storeBusinessType,BusinessTypeEnums.SALE.getCode())) {
            idto.setDeliveryBatch(orderDto.getDeliveryBatch());
        }
        idto.setSourceType(OrderTypeEnum.PC_ORDER.getCode());
        idto.setNeedError(0);
        idto.setOrderId(order.getId());
        idto.setOrderCode(order.getOrderCode());
        idto.setNeedUnFreeze(needUnFreeze);
        idto.setLogisticsCenterId(orderDto.getLogisticsCenterId());
        List<ToBOrderDetailIDTO> commodityList = new ArrayList<>();
        idto.setOrderCommodityDetailList(commodityList);
        Map<Long, List<OrderItemDto>> goodCommodityMap = orderDto.getItems().stream().collect(groupingBy(c -> Long.valueOf(c.getProductId())));
        Map<String, String> normalCommodityMap = orderDto.getItems().stream().filter(p -> ProductTypeEnums.PRODUCT.getCode().equals(p.getType())).collect(toMap(OrderItemDto::getProductId, OrderItemDto::getProductId, (key1, key2) -> key2));
        //Map<Long,BigDecimal> giftCommodityMap = new HashMap<>();
        goodCommodityMap.forEach((k, v) -> {

            BigDecimal giftSum = v.stream().filter(c -> c.getType().compareTo(ProductTypeEnums.GIFT.getCode()) == 0).map(OrderItemDto::getProductNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            /*if(giftSum.compareTo(BigDecimal.ZERO) > 0){
                giftCommodityMap.put(k,giftSum);
            }*/
            BigDecimal commSum = v.stream().map(OrderItemDto::getProductNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            ToBOrderDetailIDTO detailIDTO = new ToBOrderDetailIDTO();
            detailIDTO.setCommodityId(k);
            CommodityInventoryODTO commodityInventoryODTO = commodityInventoryMap.get(k);
            detailIDTO.setStockType(commodityInventoryODTO.getStockType());
            detailIDTO.setOrderQuantity(commSum);
            detailIDTO.setGuaranteedQuantity(commSum.subtract(giftSum));
            Commodity c = commodityMap.get(k);
            detailIDTO.setOrderNumber(commSum.divide(c.getCommodityPackageSpec(), 0, BigDecimal.ROUND_UP).intValue());
            commodityList.add(detailIDTO);
        });

        Map<String, String> commodityUnderStockMap = new HashMap<>();
        try {
            List<CommodityInventoryODTO> commodityInventoryODTOS = toBClient.warehouseFreezeInventory(idto);
            Map<Long, Long> freezeCommWarehouseMap = commodityInventoryODTOS.stream().collect(toMap(CommodityInventoryODTO::getCommodityId, CommodityInventoryODTO::getWarehouseId, (v1, v2) -> v2));
            orderDto.setCommWarehouseMap(freezeCommWarehouseMap);
        } catch (Exception e) {
            log.warn("客服下单库存冻结报错",e);

            // 冻结报错，重新查询下库存。库存不足就返回前端。并且解冻
            Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
            idto.getOrderCommodityDetailList().forEach(item -> {
                orderQuantityMap.put(item.getCommodityId(), item.getOrderQuantity());
            });
            Map<Long, CommodityInventoryODTO> toBStockMap = getbStockMap(DateUtil.parseDate(orderDto.getOrderTime()
                            , "yyyy-MM-dd")
                    , requestDto.getStoreId()
                    , orderQuantityMap
                    ,null
                    , requestDto.getDeliveryTimeRange()
                    , requestDto.getDeliveryBatch());
            List<BStockLackVO> bStockLackList = getFreezeLackList(idto, toBStockMap, requestDto.getUserId(), remark);

            if (CollectionUtils.isNotEmpty(bStockLackList)) {
                commodityUnderStockSendKafkaMsg(bStockLackList);
                bStockLackList.forEach(c -> {
                    // 只返回正常品库存不足，用于前端页面显示
                    if (normalCommodityMap.containsKey(c.getCommodityId() + "")) {
                        commodityUnderStockMap.put(c.getCommodityId().toString(), "库存不足，余量为" + c.getStockQuantity().toString());
                    }
                });
            }

            if(commodityUnderStockMap.size() <= 0) {
                // 如果大仓冻结报错，就随便给一个商品。报错信息是大仓错误信息
                commodityUnderStockMap.put(commodityList.get(0).getCommodityId() + "", "正在加载中，请稍后重试!");
            }
        }
        return commodityUnderStockMap;

    }

    private List<BStockLackVO> getFreezeLackList(WarehouseFreezeInventoryIDTO idto, Map<Long, CommodityInventoryODTO> toBStockMap, Long createId, String remark) {
        List<BStockLackVO> bStockLackList = new ArrayStack();
        for (ToBOrderDetailIDTO toBOrderDetailIDTO : idto.getOrderCommodityDetailList()) {
            CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(toBOrderDetailIDTO.getCommodityId());
            if (commodityInventoryODTO != null && !commodityInventoryODTO.getHaveInventory()) {
                BStockLackVO lackVO = BeanCloneUtils.copyTo(toBOrderDetailIDTO, BStockLackVO.class);
                lackVO.setStoreId(idto.getStoreId());
                lackVO.setCommodityId(toBOrderDetailIDTO.getCommodityId());
                lackVO.setStockQuantity(commodityInventoryODTO.getInventoryQuantity());
                lackVO.setNeedQuantity(toBOrderDetailIDTO.getOrderQuantity());
                lackVO.setOrderType(idto.getSourceType());
                lackVO.setRemark(remark + "冻结失败");
                lackVO.setCreateId(createId);
                bStockLackList.add(lackVO);
            }
        }
        return bStockLackList;
    }

    /**
     * 订单里面有赠品。配货。重新获取commMap
     *
     * @param orderDto
     * @return
     */
    private Map<Long, Commodity> getCommMap(OrderDto orderDto) {
        List<Commodity> commodityList = commodityService.findCommodityByIdList(orderDto.getItems().stream().map(item -> Long.valueOf(item.getProductId().trim())).collect(Collectors.toList()));
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "所有商品系统不存在！");
        Map<Long, Commodity> commMap = commodityList.stream().collect(toMap(Commodity::getId, Function.identity(), (key1, key2) -> key2));
        return commMap;

    }

    /***
     * 大仓库存解冻
     * @param orderId
     * @return
     */
    private Boolean processWarehouseUnfreezeInventory(Long orderId, Integer type) {
        return toBClient.warehouseUnfreezeInventory(new WarehouseUnfreezeInventoryIDTO(orderId, type));
    }

    /***
     * 此insertOrder方法是把下面 saveOrder(Order order, OrderDto orderDto, Integer modeType, User user) 方法，保存订单t_order 逻辑单独拿出来拆开、复制订单保存的逻辑 组成的此接口
     * 主要用于创建订单时需冻结商品  需用到订单号、订单id 所以只保存订单t_order  当冻结失败时需要事务回滚  减少回滚范围
     * @param order
     * @param orderDto
     * @param modeType
     * @return
     */
    private int insertOrder(Order order, OrderDto orderDto, Integer modeType) {
        order.setCreateId(orderDto.getUserId());
        order.setUpdateId(orderDto.getUserId());
        order.setCreateTime(new Date());
        order.setUpdateTime(order.getCreateTime());
        if (modeType.intValue() == OrderModeType.REPLENISHMENT.getCode().intValue()) {
            order.setOrderAmount(orderDto.amount());
            order.setFinalAmount(orderDto.amount());
        } else {
            order.setOrderAmount(orderDto.giftAmount());
            order.setFinalAmount(orderDto.amount());
        }
        QYAssert.isTrue(null != order.getOrderAmount() && order.getOrderAmount().compareTo(BigDecimal.ZERO) > 0, "订单金额必须>0");
        String orderCode = IDGenerator.newOrderCode();
        order.setStoreId(Long.valueOf(orderDto.getStoreId()));
        //根据客户id 查询公司id (隐藏条件 外部客户)
        Long companyId = storeService.selectStoreCompanyIdByStoreId(orderDto.getStoreId());
        QYAssert.isTrue(null != companyId, "客户必须是外部客户 ...");
        order.setCompanyId(companyId);
        try {
            DateFormat time = ConcurrentDateUtil.SDF_FULL_DATE.get();
            order.setOrderTime(time.parse(orderDto.getOrderTime()));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
//      order.setOrderTime(DateTimeUtil.parse(orderDto.getOrderTime(), "yyyy-MM-dd"));
        order.setOrderCode(orderCode);
        order.setEnterpriseId(orderDto.getEnterpriseId());
        order.setPrintNum(orderDto.getPrintNum());
        if(Objects.equals(orderDto.getPlanOrderType(),1)){
            order.setOrderType(OrderTypeEnum.PLAN_SALE_ORDER.getCode());
        }else{
            order.setOrderType(OrderTypeEnum.PC_ORDER.getCode());
        }
        order.setModeType(modeType);
        order.setPrintType(OrderPrintTypeEnum.getEnumByCode(orderDto.getPrintType()));
        order.setOrderRemark(orderDto.getOrderRemark());
        order.setOrderStatus(0);
        order.setFreightAmount(BigDecimal.ZERO);
        //根据订单金额和最终金额，修改覆盖状态
        order.setSyncStatus(order.getOrderAmount().compareTo(order.getFinalAmount()) == 0 ? 1 : 0);
        order.setOrderDurationTime(getOrderDurationTime(orderDto.getStoreId(), orderDto.getOrderTime()));
        order.setTotalAmount(orderDto.originalAmount());
        order.setPromotionAmount(orderDto.promotionAmount());
        order.setBusinessType(orderDto.getBusinessType());

        /**
         * 通达销售
         */
        Integer storeBussinessType = storeService.getStoreBussinessType(order.getStoreId());
        if(isLogisticsTransPort(storeBussinessType)){
            order.setDeliveryBatch(orderDto.getDeliveryBatch());
            order.setLogisticsCenterId(orderDto.getLogisticsCenterId());
            order.setLogisticsCenter(orderDto.getLogisticsCenter());
            order.setDeliveryTimeRange(orderDto.getDeliveryTimeRange());
            order.setProcessStatus(Objects.equals(storeBussinessType,BusinessTypeEnums.PLAN_SALE.getCode())
                    ?XdaOrderProcessStatusEunm.WAITING_SHIP.getCode():XdaOrderProcessStatusEunm.WAITING_PICK.getCode());
        }

        order.setPresaleStatus(0);
        order.setLogisticsCarrierCode(orderDto.getLogisticsCarrierCode());
        return orderMapper.insertSelective(order);
    }


    private boolean isLogisticsTransPort(Integer storeBussinessType){
        return Objects.equals(storeBussinessType,BusinessTypeEnums.TD_SALE.getCode())
                ||Objects.equals(storeBussinessType,BusinessTypeEnums.B_COUNTRY.getCode())
                ||Objects.equals(storeBussinessType,BusinessTypeEnums.PLAN_SALE.getCode());
    }

    private Date getOrderDurationTime(Long storeId, String orderTime) {
        StoreDuration storeDuration = storeDurationService.findStoreDurationByStoreId(storeId);

        if (null == orderTime) {
            return null;
        }
        /**
         * 一期订单截止时间计算逻辑
         * 1、当送货日期为今日,订单截止时间为下单时间
         * 2、如果送货日期大于今日,今日+客户截单时间
         */
        Date parse = DateTimeUtil.parse(orderTime, "yyyy-MM-dd");
        if (DateUtil.isToday(parse)) {
            return new Date();
        }

        if (null != storeDuration && null != storeDuration.getEndTime()) {
            String yyyyMMddHHmmss = DateUtil.get4yMd(new Date()) + " " + storeDuration.getEndTime() + ":00";
            try {
                DateFormat dateFormat = ConcurrentDateUtil.SDF_FULL_DATE_TIME.get();
                return dateFormat.parse(yyyyMMddHHmmss);
            } catch (Exception exception) {
                log.error("订单截止时间转换失败", exception);
            }
        }
        return null;
    }




    /**
     * 判断预付款余额
     *
     * @param storeId
     * @param paidAmount
     * @return
     */
    private boolean matchBillCondition(String storeId, BigDecimal paidAmount) {
        boolean result = true;
        List<StoreSettlementCollectODTO> storeSettlementCollectODTOS = storeSettlementNewClient.listStoreSettlementByStoreIds(Arrays.asList(Long.valueOf(storeId)));
        if (SpringUtil.isNotEmpty(storeSettlementCollectODTOS)) {
            StoreSettlementCollectODTO ss = storeSettlementCollectODTOS.get(0);
            if (ss.getCollectStatus()) {
                if (null == ss.getCollectPrice()) {
                    ss.setCollectPrice(0.0);
                }
                result = BigDecimal.valueOf(ss.getCollectPrice()).compareTo(paidAmount) >= 0;
            }
        }
        return result;
    }

    /**
     *查询商品库存依据
     * @return
     */
    private Map<Integer, Map<Long, CommodityInventoryODTO>> queryCommodityInventory(OrderDto orderDto,  Long orderId, String orderTime, Map<Long, CommodityInventoryODTO> commodityInventoryMap) {

        //  B端多箱规
        List<CommodityInventoryDetailIDTO> orderCommodityList = new ArrayList<>();
        orderDto.getItems().forEach(p -> {
            CommodityInventoryDetailIDTO detailIDTO = new CommodityInventoryDetailIDTO();
            detailIDTO.setCommodityId(Long.valueOf(p.getProductId()));
            detailIDTO.setQuantity(p.getProductNum());
            detailIDTO.setLevel(ProductTypeEnum.NORMAL.getCode());
            if (ProductTypeEnums.GIFT.getCode().equals(p.getType())) {
                detailIDTO.setLevel(ProductTypeEnum.GIFT.getCode());
            }
            orderCommodityList.add(detailIDTO);
        });

        // orderCommodityList去除重复
        List<CommodityInventoryDetailIDTO> mergedList = mergeOrderItemsByLevel(orderCommodityList);

        // 查询大仓依据库存
        CommodityInventoryIDTO commodityInventoryIDTO = new CommodityInventoryIDTO();
        commodityInventoryIDTO.setOrderTime(DateUtil.parseDate(orderTime, "yyyy-MM-dd"));
        commodityInventoryIDTO.setOrderCommodityList(mergedList);
        commodityInventoryIDTO.setOrderId(orderId);
        if (tdaOrderService.isTdaStore(orderDto.getStoreId())) {
            commodityInventoryIDTO.setType(DeliveryOrderTypeEnums.TD_SALE.getCode());
        } else {
            commodityInventoryIDTO.setType(DeliveryOrderTypeEnums.SALE.getCode());
        }
        Map<Integer, Map<Long, CommodityInventoryODTO>> resultMap = this.queryCommodityInventoryWithLevel(commodityInventoryIDTO);

        //普通库存信息
        Map<Long, CommodityInventoryODTO> normalMap = resultMap.get(ProductTypeEnum.NORMAL.getCode());
        //赠品库存信息
        Map<Long, CommodityInventoryODTO> giftMap = resultMap.get(ProductTypeEnum.GIFT.getCode());

        commodityInventoryMap.putAll(normalMap);
        if (giftMap != null) {
            commodityInventoryMap.putAll(giftMap);
        }

        return resultMap;
    }

    private Map<String, String> queryAndProcessCommodityInventory(OrderDto orderDto, OrderRequestDto requestDto, Long orderId, String orderTime, Map<Long, CommodityInventoryODTO> map, String remark) {

        Map<String, String> commodityUnderStockMap = new HashMap<>();
        Map<Long, List<OrderItemDto>> orderItemMap = orderDto.getItems().stream().collect(groupingBy(p -> Long.valueOf(p.getProductId())));

        // 赠品map
        //Map<String, BigDecimal> giftMap = orderDto.getItems().stream().filter(p -> OrderListTypeEnums.GIFT.getCode().equals(p.getType())).collect(Collectors.toMap(OrderItemDto::getProductId,OrderItemDto::getProductNum,(key1 , key2)-> key2));

        //  B端多箱规
        List<CommodityInventoryDetailIDTO> orderCommodityList = new ArrayList<>();
        orderDto.getItems().forEach(p -> {
            CommodityInventoryDetailIDTO detailIDTO = new CommodityInventoryDetailIDTO();
            detailIDTO.setCommodityId(Long.valueOf(p.getProductId()));
            detailIDTO.setQuantity(p.getProductNum());
            detailIDTO.setLevel(ProductTypeEnum.NORMAL.getCode());
            if (ProductTypeEnums.GIFT.getCode().equals(p.getType())) {
                detailIDTO.setLevel(ProductTypeEnum.GIFT.getCode());
            }
            orderCommodityList.add(detailIDTO);
        });

        // orderCommodityList去除重复
        List<CommodityInventoryDetailIDTO> mergedList = mergeOrderItemsByLevel(orderCommodityList);
        /***
         * 查询大仓依据库存
         */
        CommodityInventoryIDTO commodityInventoryIDTO = new CommodityInventoryIDTO();
        commodityInventoryIDTO.setOrderTime(DateUtil.parseDate(orderTime, "yyyy-MM-dd"));
        commodityInventoryIDTO.setOrderCommodityList(mergedList);
        commodityInventoryIDTO.setOrderId(orderId);
        commodityInventoryIDTO.setLogisticsCenterId(orderDto.getLogisticsCenterId());
        Integer storeBussinessType = storeService.getStoreBussinessType(orderDto.getStoreId());
        commodityInventoryIDTO.setType(storeBussinessType);
        Map<Integer, Map<Long, CommodityInventoryODTO>> resultMap = this.queryCommodityInventoryWithLevel(commodityInventoryIDTO);
        //普通库存信息
        Map<Long, CommodityInventoryODTO> commodityInventoryMap = resultMap.get(ProductTypeEnum.NORMAL.getCode());
        //赠品库存信息
        Map<Long, CommodityInventoryODTO> giftMap = resultMap.get(ProductTypeEnum.GIFT.getCode());

        if (map != null) {
            map.putAll(commodityInventoryMap);
            if (giftMap != null) {
                map.putAll(giftMap);
            }
        }
        List<OrderItemDto> items = orderDto.getItems();
        Map<Long, Long> goodProductMap = new HashMap<>();
        List<BStockLackVO> list = new ArrayList<>();
        for (Iterator<OrderItemDto> iterator = items.iterator(); iterator.hasNext(); ) {
            OrderItemDto comm = iterator.next();
            Long productId = Long.valueOf(comm.getProductId());

            CommodityInventoryODTO c;
            if (ProductTypeEnums.GIFT.getCode().equals(comm.getType())) {
                c = giftMap.get(productId);
            } else {
                c = commodityInventoryMap.get(productId);
            }

            if (c == null || c.getWarehouseId() == null || c.getStockType() == null) {
                if (ProductTypeEnums.PRODUCT.getCode().compareTo(comm.getType()) == 0) {
                    commodityUnderStockMap.put(productId.toString(), "未设置库存依据");
                }
                continue;
            }

            /*** 赠品商品 不走以下逻辑 不然库存不足 页面也没法显示出来 （因为是订单下单商品达到促销目标后送的商品）*/
            if (comm.getType().compareTo(ProductTypeEnums.GIFT.getCode()) == 0) {
                // 如果赠品数量不足，按照当前最大库存赋值
                if (!c.getHaveInventory()) {
                    if (c.getInventoryQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                        comm.setProductNum(BigDecimal.ZERO);
                    } else {
                        if (c.getInventoryQuantity().compareTo(comm.getProductNum()) <= 0) {
                            comm.setProductNum(c.getInventoryQuantity());
                        }
                    }
                    setBStockLackVOToList(list, c, comm.getProductNum(), requestDto, remark);
                }
                c.setInventoryQuantity(c.getInventoryQuantity().subtract(comm.getProductNum()));
                // 如果赠品数量为0，删除赠品
                if (comm.getProductNum().compareTo(BigDecimal.ZERO) <= 0) {
                    iterator.remove();
                }
                continue;
            }
            if (ProductTypeEnums.PRODUCT.getCode().compareTo(comm.getType()) == 0) {
                goodProductMap.put(productId, productId);
            }

            /*** 商品库存依据不限量 不删除 配比 配货商品 */
            if (StockTypeEnum.UN_LIMIT.getCode().equals(c.getStockType())) {
                continue;
            }
            /***
             * 删除处理配货 配比商品
             * 如果配货出来的商品是依据大仓或者限量的直接过滤掉
             */
            if (comm.getType().compareTo(ProductTypeEnums.RATION.getCode()) == 0) {
                iterator.remove();
                continue;
            }

        }

        // 未设置库存依据的先返回，避免后面报空错
        if (!commodityUnderStockMap.isEmpty()) {
            return commodityUnderStockMap;
        }

        /***
         * 计算库存是否满足
         */
        orderItemMap = orderDto.getItems().stream().collect(groupingBy(p -> Long.valueOf(p.getProductId())));
        for (Map.Entry<Long, List<OrderItemDto>> m : orderItemMap.entrySet()) {
            Long k = m.getKey();
            /*** 不是订单商品类型 不走以下逻辑 */
            if (!goodProductMap.containsKey(k)) {
                continue;
            }
            CommodityInventoryODTO inventoryODTO = commodityInventoryMap.get(m.getKey());
            if (inventoryODTO.getStockType().intValue() == 2) {
                continue;
            }

            /***
             * 这里不能以 if(!inventoryODTO.getHaveInventory()) 去判断库存是否满足
             *  1：查询库存时包含了赠送商品数量 例如：
             *     买了A商品满足赠送条件 赠送A商品  这时大仓库存此A商品只有一个 那这时以两个A商品查询 这时库存不满足 那么返回页面显示库存不满足这是不对的 因为赠品大仓没有库存则不送
             *  2：查询库存时包含了配比、配货商品数量 例如：
             *     买了A商品（属于大仓库存依据或限量类型）满足条件 配货或配比了一个A商品 这时大仓库存A商品只有一个 这时库存查询是以两个A商品查询 这时库存不满足 那么返回页面显示库存不满足这是不对的 因为 属于大仓库存依据或限量类型需删除配比配货商品
             */
            if (inventoryODTO.getInventoryQuantity() == null || inventoryODTO.getInventoryQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                commodityUnderStockMap.put(k.toString(), "库存不足，余量为0");
                setBStockLackVOToList(list, inventoryODTO, m.getValue().stream().map(OrderItemDto::getProductNum).reduce(BigDecimal.ZERO, BigDecimal::add), requestDto, remark);
                continue;
            }

            /*** 删除配比、配货商品、 且不包含赠品 计算OrderListTypeEnums.GOODS商品库存数量 */
            BigDecimal goodCommodityNum = m.getValue().stream().filter(i -> i.getType().compareTo(ProductTypeEnums.GIFT.getCode()) != 0).map(OrderItemDto::getProductNum).reduce(BigDecimal.ZERO, BigDecimal::add);

            /***
             * 普通商品大于库存依据且提示库存不足
             */
            if (goodCommodityNum.compareTo(inventoryODTO.getInventoryQuantity()) > 0) {
                commodityUnderStockMap.put(k.toString(), "库存不足，余量为" + inventoryODTO.getInventoryQuantity().toString());
                setBStockLackVOToList(list, inventoryODTO, m.getValue().stream().map(OrderItemDto::getProductNum).reduce(BigDecimal.ZERO, BigDecimal::add), requestDto, remark);
            }
        }

        // 库存数量不足，发送消息
        if (CollectionUtils.isNotEmpty(list)) {
            commodityUnderStockSendKafkaMsg(list);
        }
        if (SpringUtil.isNotEmpty(commodityUnderStockMap)) {
            return commodityUnderStockMap;
        }
        return commodityUnderStockMap;
    }

    /***
     * 大仓库存不足，记录库存消息的
     * @param list
     */
    private void commodityUnderStockSendKafkaMsg(List<BStockLackVO> list) {
        mqSenderComponent.send(applicationNameSwitch + KafkaTopicConstant.B_STOCK_LACK_TOPIC,
                list,
                MqMessage.MQ_KAFKA,
                KafkaMessageTypeEnum.B_STOCK_LACK_TOPIC.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
    }

    private void setBStockLackVOToList(List<BStockLackVO> list, CommodityInventoryODTO c, BigDecimal needQuantity, OrderRequestDto requestDto, String remark) {
        BStockLackVO vo = new BStockLackVO();
        vo.setStoreId(requestDto.getStoreId());
        vo.setCommodityId(c.getCommodityId());
        vo.setStockType(c.getStockType());
        vo.setNeedQuantity(needQuantity);
        vo.setStockQuantity(c.getInventoryQuantity());
        vo.setOrderType(1);
        vo.setRemark(remark);
        vo.setCreateId(requestDto.getUserId());
        list.add(vo);
    }

    private Map<Integer, Map<Long, CommodityInventoryODTO>> queryCommodityInventoryWithLevel(CommodityInventoryIDTO idto) {
        Map<Integer, Map<Long, CommodityInventoryODTO>> resultMap = new HashMap<>();
        List<CommodityInventoryODTO> list = toBClient.queryCommodityWithBomInventory(idto);
        if (SpringUtil.isEmpty(list)) {
            return new HashMap<>();
        }

        resultMap = list.stream()
                .collect(groupingBy(
                        CommodityInventoryODTO::getLevel,
                        toMap(CommodityInventoryODTO::getCommodityId, Function.identity())));
        return resultMap;
    }

    private List<CommodityInventoryDetailIDTO> mergeOrderItemsByLevel(List<CommodityInventoryDetailIDTO> orderCommodityList) {
        if (CollectionUtils.isEmpty(orderCommodityList)) {
            return Collections.emptyList();
        }
        Map<String, BigDecimal> productMap = orderCommodityList.stream()
                .collect(groupingBy(
                        idto -> idto.getCommodityId() + "_" + idto.getLevel(),
                        Collectors.reducing(BigDecimal.ZERO, CommodityInventoryDetailIDTO::getQuantity, BigDecimal::add)
                ));
        List<CommodityInventoryDetailIDTO> mergedList = new ArrayList<>();
        // 构建聚合后的商品列表
        productMap.forEach((key, value) -> {
            String[] parts = key.split("_");
            CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
            idto.setCommodityId(Long.parseLong(parts[0]));
            idto.setLevel(Integer.parseInt(parts[1]));
            idto.setQuantity(value);
            mergedList.add(idto);
        });
        return mergedList;
    }

    private List<OrderItemDto> processPriceLimit(OrderDto orderDto){
        return processPriceLimit(orderDto,new HashMap<>());
    }

    private List<OrderItemDto> processPriceLimit(OrderDto orderDto,Map<String, BigDecimal> specialItemMap) {
        List<OrderItemDto> specialOrderItems = new ArrayList<>();
        List<OrderItemDto> items = orderDto.getItems();
        for (OrderItemDto item : items) {
            boolean specialFlag = item.isSpecialFlag();
            if(specialFlag){
                //处理特价
                BigDecimal productNum = item.getProductNum();
                BigDecimal availableStoreLimit = item.getAvailableStoreLimit();
                BigDecimal availableTotalLimit = item.getAvailableTotalLimit();
                BigDecimal availableLimit = availableTotalLimit.min(availableStoreLimit);
                BigDecimal specialQuantity;
                BigDecimal normalQuantity;
                BigDecimal noneLimit = new BigDecimal(999999999);
                if( 0 == item.getLimitNumber() && noneLimit.compareTo(availableTotalLimit) == 0){
                    specialQuantity = productNum;
                    normalQuantity = BigDecimal.ZERO;
                }else{
                    BigDecimal salesBoxCapacity = item.getSalesBoxCapacity();
                    availableLimit = salesBoxCapacity.multiply(availableLimit.divide(salesBoxCapacity,0, RoundingMode.FLOOR));

                    BigDecimal productNumSalesBoxCapacity = salesBoxCapacity.multiply(productNum.divide(salesBoxCapacity,0,RoundingMode.FLOOR));
                    specialQuantity = availableLimit.min(productNumSalesBoxCapacity);
                    normalQuantity = productNum.subtract(specialQuantity);

                }

                if(specialQuantity.compareTo(BigDecimal.ZERO) > 0){
                    OrderItemDto specialOrderItemDto = BeanCloneUtils.copyTo(item,OrderItemDto.class);
                    specialOrderItemDto.updatePrice(item.getPrice());
                    specialOrderItemDto.setProductNum(specialQuantity);
                    specialOrderItems.add(specialOrderItemDto);
                    //已使用的特价数量---商品id+正常商品作为key
                    BigDecimal specialQuantityUsed = specialItemMap.get(item.getProductId() + "-" + ProductTypeEnums.PRODUCT.getCode());
                    if(Objects.nonNull(specialQuantityUsed)){
                        specialQuantityUsed = specialQuantityUsed.add(specialQuantity);
                    }else{
                        specialQuantityUsed = specialQuantity;
                    }
                    //记录商品已使用特价的数量
                    specialItemMap.put(generateSpecialItemMapProductTypeKey(item.getProductId(),ProductTypeEnums.PRODUCT.getCode()),specialQuantityUsed);

                }

                if(normalQuantity.compareTo(BigDecimal.ZERO) >0){
                    OrderItemDto normalOrderItemDto = BeanCloneUtils.copyTo(item,OrderItemDto.class);
                    normalOrderItemDto.updatePrice(normalOrderItemDto.getOriginalPrice());
                    normalOrderItemDto.setProductNum(normalQuantity);
                    normalOrderItemDto.setSpecialFlag(false);
                    specialOrderItems.add(normalOrderItemDto);
                }

            }else{
                specialOrderItems.add(item);
            }
        }
        return specialOrderItems;
    }

    /**
     * 正常订单商品已占用特价map的key
     * @return
     */
    private String generateSpecialItemMapProductTypeKey(String productId,Integer productTypeCode){
        return productId + "-" + productTypeCode;
    }

    private List<OrderItemDto> processDistributionPriceLimit(OrderDto orderDto,Map<String, BigDecimal> specialItemMap) {

        List<OrderItemDto> items = orderDto.getItems();
        //是否有配货商品
        List<OrderItemDto> rationItemList = items.stream().filter(orderItemDto -> Objects.equals(orderItemDto.getType(), ProductTypeEnums.RATION.getCode())).collect(toList());
        if(SpringUtil.isEmpty(rationItemList)){
            return items;
        }
        List<OrderItemDto> mergedOrderItemList = Lists.newArrayList();
        List<OrderItemDto> notRationItemList = items.stream().filter(orderItemDto -> !Objects.equals(orderItemDto.getType(), ProductTypeEnums.RATION.getCode())).collect(toList());

        //配货商品特价拆行
        List<OrderItemDto> specialDistributionOrderItems = new ArrayList<>();

        //重新构建配货商品特价限购信息
        buildProductItemPrice(rationItemList,orderDto.getStoreId().toString(),orderDto.getOrderTime());

        List<Commodity> distributionCommodityList = commodityService.findCommodityByIdList(rationItemList.stream().map(OrderItemDto::getProductId).map(Long::valueOf).collect(toList()));
        Map<Long, Commodity> distributionCommodityMap = distributionCommodityList.stream().collect(toMap(Commodity::getId, Function.identity()));


        for (OrderItemDto item : rationItemList) {
            boolean specialFlag = item.isSpecialFlag();
            if(specialFlag){
                //处理特价
                //之前的正常商品已经用掉的特价限购数
                BigDecimal usedLimitNum = Optional.ofNullable(specialItemMap.get(generateSpecialItemMapProductTypeKey(item.getProductId(),ProductTypeEnums.PRODUCT.getCode()))).orElse(BigDecimal.ZERO);
                BigDecimal productNum = item.getProductNum();
                BigDecimal availableStoreLimit = item.getAvailableStoreLimit();
                BigDecimal availableTotalLimit = item.getAvailableTotalLimit();
                //把之前正常商品用掉的特价限购数去除得到还可以继续使用的特价限购数量
                BigDecimal availableLimit = availableTotalLimit.min(availableStoreLimit).subtract(usedLimitNum);
                BigDecimal specialQuantity;
                BigDecimal normalQuantity;
                BigDecimal noneLimit = new BigDecimal(999999999);
                if( 0 == item.getLimitNumber() && noneLimit.compareTo(availableTotalLimit) == 0){
                    specialQuantity = productNum;
                    normalQuantity = BigDecimal.ZERO;
                }else{

                    BigDecimal salesBoxCapacity = BigDecimal.valueOf(distributionCommodityMap.get(Long.parseLong(item.getProductId())).getSalesBoxCapacity());
                    availableLimit = salesBoxCapacity.multiply(availableLimit.divide(salesBoxCapacity,0, RoundingMode.FLOOR));

                    BigDecimal productNumSalesBoxCapacity = salesBoxCapacity.multiply(productNum.divide(salesBoxCapacity,0,RoundingMode.FLOOR));
                    specialQuantity = availableLimit.min(productNumSalesBoxCapacity);
                    normalQuantity = productNum.subtract(specialQuantity);

                }

                if(specialQuantity.compareTo(BigDecimal.ZERO) > 0){
                    OrderItemDto specialOrderItemDto = BeanCloneUtils.copyTo(item,OrderItemDto.class);
                    specialOrderItemDto.updatePrice(item.getPrice());
                    specialOrderItemDto.setProductNum(specialQuantity);
                    specialDistributionOrderItems.add(specialOrderItemDto);

                    //已使用的特价数量
                    BigDecimal specialQuantityUsed = specialItemMap.get(item.getProductId());
                    if(Objects.nonNull(specialQuantityUsed)){
                        specialQuantityUsed = specialQuantityUsed.add(specialQuantity);
                    }else{
                        specialQuantityUsed = specialQuantity;
                    }
                    //记录商品已使用特价的数量
                    specialItemMap.put(item.getProductId(),specialQuantityUsed);

                }

                if(normalQuantity.compareTo(BigDecimal.ZERO) >0){
                    OrderItemDto normalOrderItemDto = BeanCloneUtils.copyTo(item,OrderItemDto.class);
                    normalOrderItemDto.updatePrice(normalOrderItemDto.getOriginalPrice());
                    normalOrderItemDto.setProductNum(normalQuantity);
                    normalOrderItemDto.setSpecialFlag(false);
                    specialDistributionOrderItems.add(normalOrderItemDto);
                }

            }else{
                specialDistributionOrderItems.add(item);
            }
        }

        mergedOrderItemList.addAll(notRationItemList);
        mergedOrderItemList.addAll(specialDistributionOrderItems);
        return mergedOrderItemList;
    }

    /**
     * 移除价格为空的商品
     * @param orderDto
     */
    private void processCommodityPrice(OrderDto orderDto) {
        orderDto.setItems(
                orderDto.getItems().stream().filter(i -> {
                    return ((i.getPrice() != null) && (i.getPrice().compareTo(BigDecimal.ZERO) > 0));
                }).collect(Collectors.toList())
        );

    }

    private void buildProductPrice(OrderDto orderDto) {
        if (null != orderDto && SpringUtil.isNotEmpty(orderDto.getItems())) {
            buildProductItemPrice(orderDto.getItems(),orderDto.getStoreId().toString(),orderDto.getOrderTime());
        }
    }

    private void buildProductItemPrice(List<OrderItemDto> itemList,String storeId,String orderTime) {
        List<Long> productIds = itemList.stream().map(OrderItemDto::getProductId).map(Long::parseLong).collect(toList());
        List<ProductPriceDto> ppList = this.productPrice(productIds, storeId, orderTime);
        if (SpringUtil.isNotEmpty(ppList)) {
            itemList.forEach(i -> {
                ppList.forEach(p -> {
                    if (i.getProductId().equals(p.getProductId())) {
                        i.updatePrice(p.getPrice());
                        i.setLimitNumber(p.getLimitNumber());
                        i.setAvailableStoreLimit(p.getAvailableStoreLimit());
                        i.setAvailableTotalLimit(p.getAvailableTotalLimit());
                        i.setPricePromotionId(p.getPricePromotionId());
                        i.setProductName(p.getProductName());
                        i.setOriginalPrice(p.getOriginalPrice());
                        i.setSpecialFlag(p.isSpecialFlag());
                    }
                });
            });
        }
    }

    public List<ProductPriceDto> productPrice(List<Long> productIds, String storeId, String orderTime) {
        if (StringUtils.isBlank(storeId)) {
            return null;
        }
        List<ProductPriceDto> ppdList = commodityService.findStoreCommodityByStoreId(storeId,productIds);
        if (SpringUtil.isNotEmpty(ppdList)) {
            ppdList.forEach(p -> {
                p.setOriginalPrice(p.getPrice());
            });
        }
        calculateSpecialPriceProduct(ppdList, Long.parseLong(storeId), orderTime);
        return ppdList;
    }

    private void calculateSpecialPriceProduct(List<ProductPriceDto> ppdList, Long storeId, String orderTime) {
        if (SpringUtil.isNotEmpty(ppdList)) {

            List<Long> commodityIdList = ppdList.stream()
                    .map(ProductPriceDto::getProductId)
                    .map(Long::parseLong)
                    .distinct()
                    .collect(toList());
            Map<Long,StorePromotionCommodityPriceODTO> map = commodityService.selectCommodityPromotionToMap(commodityIdList,storeId,orderTime);

            ppdList.forEach(
                    ppd->{
                        StorePromotionCommodityPriceODTO storePromotionCommodityPriceODTO = map.get(Long.parseLong(ppd.getProductId()));
                        if(null != storePromotionCommodityPriceODTO && storePromotionCommodityPriceODTO.getPrice().compareTo(ppd.getOriginalPrice()) < 0){
                            ppd.setPrice(storePromotionCommodityPriceODTO.getPrice());
                            ppd.setLimitNumber(storePromotionCommodityPriceODTO.getLimitNumber());
                            ppd.setAvailableTotalLimit(storePromotionCommodityPriceODTO.getAvailableTotalLimit());
                            ppd.setAvailableStoreLimit(storePromotionCommodityPriceODTO.getAvailableStoreLimit());
                            ppd.setPricePromotionId(storePromotionCommodityPriceODTO.getPricePromotionId());
                            ppd.setSpecialFlag(true);
                        }
                    }
            );
        }
    }

    /***
     * 验证是否凑整商品
     */
    private void checkRoundedGoods(OrderDto orderDto) {
        if (orderDto == null || SpringUtil.isEmpty(orderDto.getItems())) {
            return;
        }
        List<Long> commodityIds = orderDto.getItems().stream().map(c -> Long.valueOf(c.getProductId())).distinct().collect(toList());
        List<Long> commodityFreezeGroupList = commodityFreezeGroupService.findCommodityFreezeGroupList(commodityIds);
        if (SpringUtil.isEmpty(commodityFreezeGroupList)) {
            return;
        }
        boolean flag = false;
        BigDecimal roundedGoodsNumber = BigDecimal.ZERO;
        for (OrderItemDto dto : orderDto.getItems()) {
            if (commodityFreezeGroupList.contains(Long.valueOf(dto.getProductId()))) {
                flag = true;
                roundedGoodsNumber = roundedGoodsNumber.add(dto.getProductNum());
            }
        }
        if (flag && roundedGoodsNumber.compareTo(BigDecimal.ZERO) == 0) {
            QYAssert.isTrue(roundedGoodsNumber.remainder(BigDecimal.valueOf(ApiProductFrozenMultipleEnum.PRODUCT_FROZEN_MULTIPLE.getFrozenMultiple())).compareTo(BigDecimal.ZERO) == 0, "凑整商品必须是" + ApiProductFrozenMultipleEnum.PRODUCT_FROZEN_MULTIPLE.getFrozenMultiple() + "的倍数,当前数量是" + roundedGoodsNumber);
        }
        QYAssert.isTrue(roundedGoodsNumber.remainder(BigDecimal.valueOf(ApiProductFrozenMultipleEnum.PRODUCT_FROZEN_MULTIPLE.getFrozenMultiple())).compareTo(BigDecimal.ZERO) == 0, "凑整商品必须是" + ApiProductFrozenMultipleEnum.PRODUCT_FROZEN_MULTIPLE.getFrozenMultiple() + "的倍数,当前数量是" + roundedGoodsNumber);

    }

    private void checkConsumableProduct(OrderDto orderDto) {
        //获取所有的商品id
        Long storeId = orderDto.getStoreId();
        Map<String, BigDecimal> productIdMap = orderDto.getItems().stream().collect(toMap(OrderItemDto::getProductId, OrderItemDto::getProductNum));

        //调用方法 根据商品idList 客户id 校验数据
        consumableLimitService.checkConsumableCommodityTwo(storeId, productIdMap);

    }

    private Map<Long, CommodityInventoryODTO> getbStockMap(Date orderTime,
                                                           Long storeId,
                                                           Map<Long, BigDecimal> orderQuantityMap,
                                                           Long orderId,
                                                           String deliveryTimeRange,
                                                           Integer deliveryBatch) {
        //  B端多箱规
        List<CommodityInventoryDetailIDTO> orderCommodityList = new ArrayList<>();
        orderQuantityMap.forEach((k, v) -> {
            CommodityInventoryDetailIDTO detailIDTO = new CommodityInventoryDetailIDTO();
            detailIDTO.setCommodityId(k);
            detailIDTO.setQuantity(v);
            detailIDTO.setLevel(ProductTypeEnum.NORMAL.getCode());
            orderCommodityList.add(detailIDTO);
        });

        List<CommodityInventoryODTO> list = toBService.queryCupCommodityInventory(orderTime,orderCommodityList,storeId,orderId,deliveryTimeRange,deliveryBatch);
        if (SpringUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(toMap(CommodityInventoryODTO::getCommodityId, Function.identity()));

    }

    private Map<Long, Commodity> checkCommodityInfo(OrderRequestDto orderRequestDto) {
        Set<String> repeatCommodityCodeSet = new HashSet<>();
        orderRequestDto.getItemsList().stream().forEach(c -> {
            QYAssert.isTrue(StringUtils.isNotBlank(c.getProductId()), "商品不能为空");
            QYAssert.isTrue(c.getProductNum() != null, "商品数量不能为空");
            QYAssert.isTrue(c.getProductNum().compareTo(BigDecimal.ZERO) > 0, "商品数量必须大于0");
            QYAssert.isTrue(repeatCommodityCodeSet.add(c.getProductId().trim()), "此商品重复 商品编码 " + c.getProductCode());
        });

        List<Commodity> commodityList = commodityService.findCommodityByIdList(orderRequestDto.getItemsList().stream().map(item -> Long.valueOf(item.getProductId().trim())).collect(Collectors.toList()));
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "所有商品系统不存在！");
        checkCommodityIfComb(commodityList);

        Map<Long, Commodity> commMap = commodityList.stream().collect(toMap(Commodity::getId, Function.identity(), (key1, key2) -> key2));

        for (OrderItemRequestDto itemRequestDto : orderRequestDto.getItemsList()) {
            QYAssert.isTrue(commMap.containsKey(Long.valueOf(itemRequestDto.getProductId())), "商品编码" + itemRequestDto.getProductCode() + " 系统不存在！");
            Commodity commodity = commMap.get(Long.valueOf(itemRequestDto.getProductId()));
            QYAssert.isTrue("1".equals(commodity.getCommodityState().trim()), "此商品编码" + itemRequestDto.getProductCode() + "不可售，请确认！");
            itemRequestDto.setSalesBoxCapacity(BigDecimal.valueOf(commodity.getSalesBoxCapacity()));

            if(!NumberUtils.isNumber(itemRequestDto.getProductNum() + "")) {
                QYAssert.isFalse("商品编码" + itemRequestDto.getProductCode() + " 订货数量格式不正确,必须大于0并且长度最多15位，允许2位小数");
            }

            // 整包非称重只能输入整数
            if(CommodityPackageTypeEnums.整包.getCode().equals(Long.valueOf(commodity.getCommodityPackageId() + ""))
               && YesOrNoEnums.NO.getCode().equals(commodity.getIsWeight())) {
                if(!NumberUtils.isInteger(itemRequestDto.getProductNum() + "")) {
                    QYAssert.isFalse("商品编码" + itemRequestDto.getProductCode() + " 订货数量格式不正确,必须大于0并且长度最多15位的整数");
                }
            }
        }
        return commMap;
    }

    private void checkCommodityIfComb(List<Commodity> commodityList) {
        StringBuilder sb = new StringBuilder();
        List<Commodity> combCommodities = commodityList.stream()
                .filter(commodity -> commodity.getProductType().intValue() == 2)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(combCommodities)) {
            StringBuilder codes = new StringBuilder();
            StringBuilder names = new StringBuilder();
            combCommodities.forEach(combCommodity -> {
                codes.append(combCommodity.getCommodityCode()).append("、");
                names.append(combCommodity.getCommodityName()).append("、");
            });
            codes.deleteCharAt(codes.length() - 1); // 删除最后一个多余的逗号
            names.deleteCharAt(names.length() - 1); // 删除最后一个多余的逗号
            sb.append("商品编码：").append(codes).append(", 商品名称：").append(names).append("不允许为组合商品");

        }

        QYAssert.isTrue(sb.length() == 0, sb.toString());
    }


    public JsonMsgBean checkEditOrder(Long orderId, String editTypeStr, Boolean assertMsg ,Integer planOrderType) {
        JsonMsgBean json = new JsonMsgBean(true);

        // 转换 editType 为枚举
        EditType editType;
        try {
            editType = EditType.valueOf(editTypeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            json.setSuccess(false);
            json.setMessage("无效的编辑类型: " + editTypeStr);
            return json;
        }

        // 校验订单存在性和状态
        Order order = validateOrder(orderId);
        Long storeId = order.getStoreId();

        // 校验门店状态
        validateStoreStatus(storeId);

        // 根据编辑类型处理具体逻辑
        switch (editType) {
            case MODIFY:
                handleModifyOrder(order, storeId, json);
                break;
            case CANCEL:
                handleCancelOrder(order, storeId, assertMsg, json,planOrderType);
                break;
            case COPY:
                // 复制操作无需额外校验
                break;
        }

        // 对修改和取消操作进行共享的截止时间校验
        if (editType != EditType.COPY) {
            validateOrderDeadline(order, assertMsg, json);
        }

        return json;

    }

    private void validateOrderDeadline(Order order, Boolean assertMsg, JsonMsgBean json) {
        if (null != order.getOrderDurationTime()) {
            //判断是否超过订单截止时间
            if (DateUtil.isBefore(order.getOrderDurationTime(), new Date())) {
                //判断当前订单是否开始拣货
                if (checkOrderGeneratePickOrder(order.getId())) {
                    if(assertMsg) {
                        QYAssert.isFalse("大仓已执行发货，不能取消订单或修改订单。");
                    }
                    json.setMessage("大仓已执行发货，不能取消订单或修改订单。");
                    json.setSuccess(Boolean.FALSE);
                } else {
                    json.setMessage("该订单已过截单时间，可能已安排生产、采购、发货，如果执意取消或修改，将可能被相关部门追责。");
                    json.setSuccess(Boolean.TRUE);
                }
            }
        }
    }

    private void handleCancelOrder(Order order, Long storeId, Boolean assertMsg, JsonMsgBean json,Integer planOrderType) {

        if (tdaOrderService.isTdaStore(storeId)) {
            boolean isValidStatus = Arrays.asList(
                    XdaOrderProcessStatusEunm.WAITING_PICK.getCode(),
                    XdaOrderProcessStatusEunm.OUT_STOCKING.getCode(),
                    XdaOrderProcessStatusEunm.WAITING_COLLECT.getCode()
            ).contains(Integer.valueOf(order.getProcessStatus()));
            QYAssert.isTrue(isValidStatus, "订单状态异常，不能取消!");
        }

        if(!Objects.equals(planOrderType,1)){
            Integer businessType = order.getBusinessType();
            if(Objects.equals(businessType,BusinessTypeEnums.PLAN_SALE.getCode())){
                QYAssert.isFalse("计划销售类型的订单，请在计划订单中取消");
            }
        }

    }

    private void handleModifyOrder(Order order, Long storeId, JsonMsgBean json) {
        if (tdaOrderService.isTdaStore(storeId)) {
            QYAssert.isFalse("通达销售订单不允许修改");
        }

        if(Objects.equals(order.getBusinessType(),BusinessTypeEnums.PLAN_SALE.getCode())){
            QYAssert.isFalse("计划销售类型的订单，不允许修改订单");
        }

        if(Objects.equals(order.getBusinessType(),BusinessTypeEnums.B_COUNTRY.getCode())){
            QYAssert.isFalse("B端全国类型的订单，不允许修改订单");
        }
        // 校验订单明细
        List<OrderList> orderLists = getOrderLists(order.getId());
        QYAssert.isTrue(SpringUtil.isNotEmpty(orderLists), "订单明细不存在，无法修改");

        boolean isExistComb = orderLists.stream().anyMatch(item -> !item.getCombType().equals(CombTypeEnum.NOT_COMB.getCode()));
        if (isExistComb) {
            QYAssert.isFalse("包含组合商品，客服不能修改订单");
        }

        // 订单修改如果当天订单商品享受了特价，但是特价停用或者不是非限量的特价不允许修改
        boolean isAllNonLimit = checkCanCopyOrModityfy(order, orderLists);
        if (!isAllNonLimit) {
            QYAssert.isFalse("订单中存在限量或限购特价商品，不可修改订单");
        }

        boolean isXdaAppUseCoupon = OrderTypeEnum.XDA_APP_ORDER.getCode().equals(order.getOrderType()) && checkIfUseCoupon(orderLists);
        if (isXdaAppUseCoupon) {
            QYAssert.isFalse("订单在鲜达App使用了优惠券，不允许修改订单");
        }

        // 校验订单类型白名单
        validateOrderTypeWhitelist(order.getOrderType());
    }

    private void validateOrderTypeWhitelist(Integer orderType) {
        // 背景：由于门店订单被客服误修改，导致了线上数据问题，为屏蔽这个风险，须要对客服修改订单功能做控制
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("customerUpdateOrderwhite");
        if(dictionaryODTO != null && StringUtils.isNotBlank(dictionaryODTO.getOptionValue())) {
            String [] str = dictionaryODTO.getOptionValue().split(",");
            boolean flag = Arrays.stream(str).anyMatch(item -> item.equals(orderType + ""));
            if(!flag){
                QYAssert.isFalse("该订单来源不允许客服修改!");
            }
        }else {
            QYAssert.isFalse("请配置修改订单类型白名单!");
        }
    }


    public List<OrderList> getOrderLists(Long orderId){
        Example orderListExample = new Example(OrderList.class);
        orderListExample.createCriteria().andEqualTo("orderId", orderId);
        List<OrderList> orderLists = orderListMapper.selectByExample(orderListExample);
        return orderLists;
    }
    private void validateStoreStatus(Long storeId) {
        Integer storeStatus = storeCompanyClient.selectStoreCompanyByStoreId(storeId);
        if(null == storeStatus || storeStatus != 1){
            QYAssert.isTrue(false, "客户已停用!");
        }
    }

    private Order validateOrder(Long orderId) {

        Example orderExample = new Example(Order.class);
        orderExample.createCriteria().andEqualTo("id", orderId).andEqualTo("orderStatus", YesOrNoEnums.NO.getCode());
        List<Order> orderList = orderMapper.selectByExample(orderExample);

        QYAssert.isTrue(CollectionUtils.isNotEmpty(orderList), "无法编辑该订单!");
        return orderList.get(0);
    }

    /**
     * 取消、修改订单校验
     *
     * @param orderId
     * @param editType
     * @param  assertMsg == true强制抛出异常
     * @return
     */
    public JsonMsgBean checkEditOrderOld(Long orderId, String editType, Boolean assertMsg) {
        JsonMsgBean json = new JsonMsgBean(true);

        Example orderExample = new Example(Order.class);
        orderExample.createCriteria().andEqualTo("id", orderId).andEqualTo("orderStatus", YesOrNoEnums.NO.getCode());
        List<Order> orderList = orderMapper.selectByExample(orderExample);

        QYAssert.isTrue(CollectionUtils.isNotEmpty(orderList), "无法编辑该订单!");

        Order order = orderList.get(0);
        Long storeId = order.getStoreId();

        Integer storeStatus = storeCompanyClient.selectStoreCompanyByStoreId(storeId);
        if(null == storeStatus || storeStatus != 1){
            QYAssert.isTrue(false, "客户已停用!");
        }

        if ("modify".equals(editType)) {
            if (tdaOrderService.isTdaStore(storeId)) {
                QYAssert.isFalse("通达销售订单不允许修改");
            }

            if(Objects.equals(order.getBusinessType(),BusinessTypeEnums.PLAN_SALE.getCode())){
                QYAssert.isFalse("计划销售类型的订单，不允许修改订单");
            }

            if(Objects.equals(order.getBusinessType(),BusinessTypeEnums.B_COUNTRY.getCode())){
                QYAssert.isFalse("B端全国类型的订单，不允许修改订单");
            }
            Example orderListExample = new Example(OrderList.class);
            orderListExample.createCriteria().andEqualTo("orderId", order.getId());
            List<OrderList> orderLists = orderListMapper.selectByExample(orderListExample);
//            List<OrderList> orderLists = orderLists.stream().filter(orderList-> Objects.equals(orderList.getType(),ProductTypeEnums.PRODUCT.getCode())).collect(toList());
            QYAssert.isTrue(SpringUtil.isNotEmpty(orderLists),"订单明细不存在，无法修改");

            boolean isExistComb = orderLists.stream().anyMatch(item -> !item.getCombType().equals(CombTypeEnum.NOT_COMB.getCode()));
            if (isExistComb) {
                QYAssert.isFalse("包含组合商品，客服不能修改订单");
            }

            // 订单修改如果当天订单商品享受了特价，但是特价停用或者不是非限量的特价不允许修改
            boolean isAllNonLimit = checkCanCopyOrModityfy(order, orderLists);
            if (!isAllNonLimit) {
                QYAssert.isFalse("订单中存在限量或限购特价商品，不可修改订单");
            }

            boolean isXdaAppUseCoupon = OrderTypeEnum.XDA_APP_ORDER.getCode().equals(order.getOrderType()) && checkIfUseCoupon(orderLists);
            if (isXdaAppUseCoupon) {
                QYAssert.isFalse("订单在鲜达App使用了优惠券，不允许修改订单");
            }

            // 背景：由于门店订单被客服误修改，导致了线上数据问题，为屏蔽这个风险，须要对客服修改订单功能做控制
            DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("customerUpdateOrderwhite");
            if(dictionaryODTO != null && StringUtils.isNotBlank(dictionaryODTO.getOptionValue())) {
                String [] str = dictionaryODTO.getOptionValue().split(",");
                boolean flag = Arrays.stream(str).anyMatch(item -> item.equals(order.getOrderType() + ""));
                if(!flag){
                    QYAssert.isFalse("该订单来源不允许客服修改!");
                }
            }else {
                QYAssert.isFalse("请配置修改订单类型白名单!");
            }
        }

        //通达订单取消逻辑
        if ("cancel".equals(editType) && tdaOrderService.isTdaStore(storeId)) {

            Boolean checkStatus = Integer.valueOf(XdaOrderProcessStatusEunm.WAITING_PICK.getCode()).equals(order.getProcessStatus())
                    || Integer.valueOf(XdaOrderProcessStatusEunm.OUT_STOCKING.getCode()).equals(order.getProcessStatus())
                    || Integer.valueOf(XdaOrderProcessStatusEunm.WAITING_COLLECT.getCode()).equals(order.getProcessStatus());
            // 待拣货的订单取消成功后，需要通知给大仓 ，大仓需要释放冻结的库存，同时也需要通知物流
            // 出库中、待揽收的订单，取消成功后，消息通知给物流进行拦截
            if (!checkStatus) {
                QYAssert.isFalse("订单状态异常，不能取消!");
            }

        } else if(!"copy".equals(editType)){
            if (null != orderList.get(0).getOrderDurationTime()) {
                //判断是否超过订单截止时间
                if (DateUtil.isBefore(orderList.get(0).getOrderDurationTime(), new Date())) {
                    //判断当前订单是否开始拣货
                    if (checkOrderGeneratePickOrder(orderId)) {
                        if(assertMsg) {
                            QYAssert.isFalse("大仓已执行发货，不能取消订单或修改订单。");
                        }
                        json.setMessage("大仓已执行发货，不能取消订单或修改订单。");
                        json.setSuccess(Boolean.FALSE);
                    } else {
                        json.setMessage("该订单已过截单时间，可能已安排生产、采购、发货，如果执意取消或修改，将可能被相关部门追责。");
                        json.setSuccess(Boolean.TRUE);
                    }
                }
            }
        }
        return json;
    }

    private boolean checkIfUseCoupon(List<OrderList> orderLists) {
        return orderLists.stream().filter(orderList -> null != orderList.getCouponId()).findAny().isPresent();
    }

    /**
     * 订单修改如果当天订单商品享受了特价，但是特价停用或者不是非限量的特价不允许修改
     * @param order
     * @param orderLists
     * @return
     */
    private boolean checkCanCopyOrModityfy(Order order, List<OrderList> orderLists) {
        List<OrderList> filterPricePromotionList = orderLists.stream().filter(p -> p.getPricePromotionId() != null).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(filterPricePromotionList)){
            Set<Long> commodityIdList = filterPricePromotionList.stream().map(item -> item.getCommodityId()).collect(Collectors.toSet());
            StorePromotionCommodityPriceSearchIDTO idto = new StorePromotionCommodityPriceSearchIDTO();
            idto.setStoreId(order.getStoreId());
            idto.setOrderTime(order.getOrderTime());
            idto.setCommodityIdList(new ArrayList<>(commodityIdList));
            idto.setNeedAvailableLimit(1);
            Map<Long, StorePromotionCommodityPriceODTO> map = storePromotionClient.getStorePromotionCommodityMapByParams(idto);

            BigDecimal nonLimitNumber = new BigDecimal("999999999");
            boolean isAllNonLimit = commodityIdList.stream().allMatch(
                    key -> {
                        StorePromotionCommodityPriceODTO storePromotionCommodityPriceODTO = map.get(key);
                        if (storePromotionCommodityPriceODTO == null) {
                            return false;
                        }
                        return nonLimitNumber.compareTo(storePromotionCommodityPriceODTO.getAvailableTotalLimit()) == 0
                                && nonLimitNumber.compareTo(storePromotionCommodityPriceODTO.getAvailableStoreLimit()) == 0;
                    }
            );
            return isAllNonLimit;
        }
        return true;
    }

    /**
     * 判断当前订单是否开始拣货
     * @param orderId
     * @return
     */
    public Boolean checkOrderGeneratePickOrder(Long orderId){
        Map<Long, Boolean> longBooleanMap = deliveryOrderClient.checkOrderGeneratePickOrder(Collections.singletonList(orderId));
        return longBooleanMap.values().stream().anyMatch(item -> item.equals(Boolean.TRUE));
    }

    /**
     * 订单列表
     * @param vo
     * @return
     */
    public PageInfo<OrderCupPageResultODTO> findOrderListByPage(OrderCupPageQueryIDTO vo) {
        QYAssert.isTrue(StringUtils.isNotBlank(vo.getOrderTimeStart()), "送货日期不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(vo.getOrderTimeEnd()), "送货日期不能为空");

        Date endDate = DateUtil.parseDate(vo.getOrderTimeEnd(), "yyyy-MM-dd");
        Date startDate = DateUtil.parseDate(vo.getOrderTimeStart(), "yyyy-MM-dd");
        int diff = DateUtil.getDayDif(endDate, startDate);
        QYAssert.isTrue(diff <= 92, "送货日期的跨度不能超过3个月");

        vo.setStoreCode(StringUtils.isNotBlank(vo.getStoreCode()) ? StringUtil.trim(vo.getStoreCode()) : null);
        vo.setStoreName(StringUtils.isNotBlank(vo.getStoreName()) ? StringUtil.trim(vo.getStoreName()) : null);

        if(StringUtils.isNotBlank(vo.getStoreCode()) || StringUtils.isNotBlank(vo.getStoreName())){
            List<Long> storeIdList = storeMapper.queryStoreIdsByStoreNameOrCode(vo.getStoreCode(), vo.getStoreName());
            if(CollectionUtils.isNotEmpty(storeIdList)){
                if(storeIdList.size() <= 500){
                    vo.setStoreIdList(storeIdList);
                }
            }else {
                return new PageInfo<>();
            }
        }
        if(!Objects.isNull(vo.getStoreCode())){
            vo.setStoreCode(StringUtil.trim(vo.getStoreCode()));
        }

        if(!Objects.isNull(vo.getStoreName())){
            vo.setStoreName(StringUtil.trim(vo.getStoreName()));
        }

        if(!Objects.isNull(vo.getOrderCode())){
            vo.setOrderCode(StringUtil.trim(vo.getOrderCode()));
        }

        if(Objects.equals(vo.getPlanOrderType(),1)){
            vo.setOrderType(OrderTypeEnum.PLAN_SALE_ORDER.getCode());
        }

        PageInfo<OrderCupPageResultODTO> pageDate = null ;
        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderCupReportMapper.findOrderListByPage(vo);
        });

        return pageDate;
    }

    @Transactional(rollbackFor = Exception.class)
    public OrderDto updateOrder(OrderRequestDto orderRequestDto) {
        OrderDto orderDto = new OrderDto();
        if (null != orderRequestDto.getOrderId()) {
            // 订单修改校验
            checkEditOrder(orderRequestDto.getOrderId(), "modify", true,orderRequestDto.getPlanOrderType());

            orderDto.setOrderDate(new Date());
            orderDto.setPrintType(orderRequestDto.getPrintType());

            Order order = orderService.findOrderById(orderRequestDto.getOrderId());

            QYAssert.isTrue(order != null, "无法编辑该订单!");
            Map<Long, Commodity> commMap = checkCommodityInfo(orderRequestDto);
            orderDto.setId(order.getId());
            orderDto.setUserId(order.getCreateId());
            orderDto.setOrderNo(order.getOrderCode());
            orderDto.setStoreId(order.getStoreId());
            orderDto.setOrderRemark(orderRequestDto.getOrderRemark());
            //OrderDto orderGiftDto = new OrderDto();

            Long storeId = orderDto.getStoreId();

            if (order.getModeType().intValue() == OrderModeType.REPLENISHMENT.getCode().intValue()) {
                List<Commodity> commoditylist = findCommodityByOrderId(orderRequestDto.getOrderId(),DateTimeUtil.formatDate(order.getOrderTime(), "yyyy-MM-dd"),ProductTypeEnums.PRODUCT.getCode());
                SpringUtil.copyProperties(orderRequestDto, orderDto);
                orderDto.setOrderTime(orderRequestDto.getOrderTime());
                orderDto.setItems(new ArrayList<OrderItemDto>());
                orderRequestDto.getItemsList().forEach(
                        i -> {
                            OrderItemDto orderItemDto = new OrderItemDto(i.getProductId(), BigDecimal.ZERO, BigDecimal.ZERO, i.getProductNum(), i.getRemark(),
                                    ProductTypeEnums.PRODUCT.getCode());
                            orderItemDto.setSalesBoxCapacity(i.getSalesBoxCapacity());
                            orderDto.addItem(orderItemDto);
                        });
                Map<String, BigDecimal> productIdMap = orderDto.getItems().stream().collect(toMap(OrderItemDto::getProductId, OrderItemDto::getProductNum));
                consumableLimitService.checkConsumableCommodityTwo(storeId, productIdMap);
                /*** 速冻调整成凑整商品 */
                //this.checkFrozenProduct(orderDto);
                /*** 凑整商品 */
                this.checkRoundedGoods(orderDto);
                this.buildProductPrice(orderDto);
                // 如果修改当时获取商品价格为空，就把订单里面的商品价格重新赋值给当前价格
                // 订单商品无法修改,价格取以前价格,数量也是以前数量
                setPrice(commoditylist, orderDto);
                // 移除价格为空的商品
                this.processCommodityPrice(orderDto);

                QYAssert.isTrue(CollectionUtils.isNotEmpty(orderDto.getItems()), "有效商品不能为空!");

                //处理特价拆行
                List<OrderItemDto> priceLimitOrderItems = processPriceLimit(orderDto);
                orderDto.setItems(priceLimitOrderItems);
                /***
                 * 取消其它限量 走大仓库存依据 调整需求如下：
                 * @see http://192.168.0.213/zentao/story-view-10044.html
                 */
                //this.checkCustomerProductLimit(orderDto);
                //this.checkProductLimit(orderDto);

                /***
                 * 查询大仓库存依据 获得商品库存依据
                 */
                Map<Long, CommodityInventoryODTO> commodityInventoryMap = new HashMap<>();
                Map<Integer, Map<Long, CommodityInventoryODTO>> inventoryMap = queryCommodityInventory(orderDto,order.getId(), orderRequestDto.getOrderTime(), commodityInventoryMap);

                /***
                 * 调用大仓冻结接口
                 */
                Map<String, String> storageUnderStockMap = this.execWarehouseFreezeInventory(orderDto,  orderRequestDto, order, commodityInventoryMap, true, "一期修改订单（补货订单）");
                if (SpringUtil.isNotEmpty(storageUnderStockMap)) {
                    return new OrderDto(storageUnderStockMap);
                }

                try {
                    QYAssert.isTrue(storeService.updateMatchBillCondition(orderDto.getStoreId(), orderDto.amount(), order.getOrderAmount()), "余额不足,修改失败!");
                    order.setUpdateTime(new Date());
                    order.setUpdateId(orderDto.getUserId());
                    order.setUpdateId(orderRequestDto.getUserId());
                    this.updateOrder(order, orderDto, orderRequestDto.getUserId(),orderRequestDto.getUserName());
                    orderLimitQuantityService.saveOrderLimitQuantity(order.getId(),1,false);
                } catch (Exception e) {
                    log.warn("订单修改失败error:{}", e);
                    /*** 调用大仓解冻接口 */
                    //通达销售订单不允许修改
                    Boolean flag = this.processWarehouseUnfreezeInventory(orderRequestDto.getOrderId(), DeliveryOrderTypeEnums.SALE.getCode());
                    if (flag != null && !flag.booleanValue()) {
                        log.warn("订单修改失败,大仓库存解冻失败 订单id：{}、订单号：{}", orderRequestDto.getOrderId(), order.getOrderCode());
                    }
                    QYAssert.isTrue(false, StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : "订单修改失败");
                }
            } else {
                // 获取B端库存依据
                Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
                orderRequestDto.getItemsList().forEach(dto -> {
                    orderQuantityMap.put(Long.valueOf(dto.getProductId()), dto.getProductNum());
                });
                Map<Long, CommodityInventoryODTO> toBStockMap = getbStockMap(order.getOrderTime()
                        , orderRequestDto.getStoreId()
                        , orderQuantityMap
                        ,orderRequestDto.getOrderId()
                        , orderRequestDto.getDeliveryTimeRange()
                        , orderRequestDto.getDeliveryBatch());

                List<Commodity> commoditylist = orderMapper.findCommodityByOrderId(order.getId(),DateTimeUtil.formatDate(order.getOrderTime(),"yyyy-MM-dd"),ProductTypeEnums.PRODUCT.getCode());
                SpringUtil.copyProperties(orderRequestDto, orderDto);
                orderDto.setUserId(order.getCreateId());
                orderDto.setOrderNo(order.getOrderCode());
                orderDto.setStoreId(order.getStoreId());
                orderDto.setOrderTime(orderRequestDto.getOrderTime());
                orderDto.setItems(new ArrayList<OrderItemDto>());


                orderRequestDto.getItemsList().forEach(
                        i -> {
                            OrderItemDto orderItemDto = new OrderItemDto(i.getProductId(), BigDecimal.ZERO, BigDecimal.ZERO, i.getProductNum(), i.getRemark(),
                                    ProductTypeEnums.PRODUCT.getCode());
                            CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(Long.valueOf(i.getProductId()));
                            orderItemDto.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
                            orderItemDto.setSalesBoxCapacity(i.getSalesBoxCapacity());
                            Commodity commodity = commMap.get(Long.parseLong(i.getProductId()));
                            orderItemDto.setCommodityThirdId(Optional.ofNullable(commodity).map(Commodity::getCommodityThirdId).orElse(null));
                            orderDto.addItem(orderItemDto);
                        });
                Map<String, BigDecimal> productIdMap = orderDto.getItems().stream().collect(toMap(OrderItemDto::getProductId, OrderItemDto::getProductNum));
                consumableLimitService.checkConsumableCommodityUpdate(storeId, productIdMap, orderRequestDto.getOrderId());
                /*** 速冻调整成凑整商品 */
                //this.checkFrozenProduct(orderDto);
                /*** 凑整商品 */
                this.checkRoundedGoods(orderDto);
                this.buildProductPrice(orderDto);

                //如果修改当时获取商品价格为空，就把订单里面的商品价格重新赋值给当前价格
                // 订单商品无法修改,价格取以前价格,数量也是以前数量
                setPrice(commoditylist, orderDto);

                // 移除价格为空的商品
                this.processCommodityPrice(orderDto);
                QYAssert.isTrue(CollectionUtils.isNotEmpty(orderDto.getItems()), "有效商品不能为空!");

                //组装giftItem
                List<OrderItemDto> orderListGiftList = BeanCloneUtils.copyTo(orderDto.getItems(), OrderItemDto.class);
                orderDto.setGiftItems(orderListGiftList);

                // 注意：orderDto.items包含原始商品、配比、配货、赠品商品信息
                //       orderDto.giftItems 只包含原始商品和赠品

                // 处理赠品（和门店处理赠品共用方法）
                //orderService.unifyProcessGifts(orderDto.getStoreId().toString(), orderDto, OrderLaunchTypeEnum.CUSTOMER_SERVICE);
                this.processFreeBuy(orderDto.getStoreId().toString(),orderDto);

                //配货方案（和门店配货共用一套方法）
                orderService.unifyProcessDistribution(orderDto.getStoreId().toString(), orderDto,OrderLaunchTypeEnum.CUSTOMER_SERVICE);

                //处理配比
                this.processRatio(orderDto.getStoreId().toString(), orderDto);

                List<OrderItemDto> rationItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.RATION.getCode().equals(item.getType())).collect(Collectors.toList());
                List<OrderItemDto> giftItemList = orderDto.getItems().stream().filter(item -> ProductTypeEnums.GIFT.getCode().equals(item.getType())).collect(Collectors.toList());
                // 如果配货或者赠品不为空
                if(CollectionUtils.isNotEmpty(rationItemList) || CollectionUtils.isNotEmpty(giftItemList)) {
                    // 1.查询库存依据和库存数量
                    Map<Integer, Map<Long, CommodityInventoryODTO>> inventoryMap = queryCommodityInventory(orderDto,order.getId(), orderRequestDto.getOrderTime(), toBStockMap);

                    // 2.配货商品只能是非限量的
                    // 3.赠品数量设置为大仓最大可用数量
                    cupGiftAndRelationDeal(inventoryMap, orderDto);
                }

                // 对配比、配货商品统一进行特价拆行
                processPriceSplit(orderDto, orderRequestDto, commoditylist);

                //如果修改当时获取商品价格为空，就把订单里面的商品价格重新赋值给当前价格
                setPrice(commoditylist, orderDto);

                QYAssert.isTrue(!orderDto.getGiftItems().isEmpty(), "有效商品不能为空 ...");
                QYAssert.isTrue(storeService.updateMatchBillCondition(orderDto.getStoreId(), orderDto.giftAmount(), order.getOrderAmount()), "余额不足,修改失败!");
                order.setUpdateTime(new Date());
                order.setUpdateId(orderDto.getUserId());


                /***
                 * 调用大仓冻结接口
                 */
                Map<String, String> storageUnderStockMap = this.execWarehouseFreezeInventory(orderDto,  orderRequestDto, order, toBStockMap, true, "一期修改订单");
                if (SpringUtil.isNotEmpty(storageUnderStockMap)) {
                    return new OrderDto(storageUnderStockMap);
                }

                try {
                    order.setUpdateTime(new Date());
                    order.setUpdateId(orderRequestDto.getUserId());
                    order.setOrderRemark(orderDto.getOrderRemark());
                    order.setPrintNum(orderDto.getPrintNum());

                    this.updateOrder(order, orderDto, orderRequestDto.getUserId(),orderRequestDto.getUserName());
                    this.updateGiftOrder(order, orderDto, orderRequestDto.getEmployeeNumber());
                    //维护特价限购记录表
                    orderLimitQuantityService.saveOrderLimitQuantity(order.getId(),1,false);
                    updateGiftTotalLimitQuantity(orderDto);
                } catch (Exception e) {
                    log.warn("订单修改失败error:{}", e);
                    /*** 调用大仓解冻接口 */
                    Boolean flag = this.processWarehouseUnfreezeInventory(orderRequestDto.getOrderId(), DeliveryOrderTypeEnums.SALE.getCode());
                    if (flag != null && !flag.booleanValue()) {
                        log.warn("订单修改失败,大仓库存解冻失败 订单id：{}、订单号：{}", orderRequestDto.getOrderId(), order.getOrderCode());
                    }
                    QYAssert.isTrue(false, StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : "订单修改失败");
                }
            }


            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    printerOrder(orderRequestDto.getUserId(), orderRequestDto.getOrderId(),orderRequestDto.getPrintType());

                    // 统计查询发送消息
                    statisticalOrderKafkaService.sendStatisticalOrderKafka(order, orderDto, KafkaMessageOperationTypeEnum.UPDATE, orderRequestDto.getUserId());

                    }
            });

        }
        return orderDto;
    }

    /**
     * 如果修改当时获取商品价格为空，就把订单里面的商品价格重新赋值给当前价格
     * @param commoditylist
     * @param orderDto
     */
    private void setPrice(List<Commodity> commoditylist, OrderDto orderDto) {
        if (SpringUtil.isNotEmpty(commoditylist)) {
            orderDto.getItems().forEach(i -> {
                if (i.getPrice() == null) {
                    commoditylist.forEach(c -> {
                        if (c.getId().toString().equals(i.getProductId())) {
                            i.setProductNum(c.getProductNumber());
                            i.updatePrice(c.getProductPrice());
                        }
                    });
                }
            });
        }
    }

    private void removeFilterRation(OrderDto orderGiftDto, OrderDto orderDto) {
        if(orderGiftDto.getItems().size() != orderDto.getItems().size()) {
            List<OrderItemDto> rationList = orderDto.getItems().stream().filter(p -> ProductTypeEnums.RATION.getCode().equals(p.getType())).collect(Collectors.toList());
            Map<String, List<OrderItemDto>> rationMap = rationList.stream().collect(groupingBy(item -> {
                return item.getProductId() + "_" + item.getType();
            }));
            for (Iterator<OrderItemDto> iterator = orderGiftDto.getItems().iterator(); iterator.hasNext(); ) {
                OrderItemDto item = iterator.next();
                if(ProductTypeEnums.RATION.getCode().equals(item.getType()) && !rationMap.containsKey(item.getProductId() + "_" + item.getType())) {
                    iterator.remove();
                }
            }
        }
    }

    private void updateGiftOrder(Order order, OrderDto orderDto, String employeeNumber) {

        Example delExp = new Example(OrderListGift.class);
        delExp.createCriteria().andEqualTo("orderId", order.getId());
        int rowNumber = orderListGiftMapper.deleteByExample(delExp);

        if (rowNumber > 0) {
            List<OrderList> orderList = new ArrayList<OrderList>();
            orderDto.getItems().forEach(i -> {
                OrderListGift gift = new OrderListGift();
                OrderList ol = new OrderList();
                ol.setCommodityId(Long.valueOf(i.getProductId()));
                ol.setCommodityNum(i.getProductNum());
                ol.setType(i.getType());
                ol.setRemark(i.getRemark());
                ol.setCommodityPrice(i.getPrice());
                ol.setOrderId(order.getId());
                ol.setTotalPrice(i.amount());
                ol.setOriginalPrice(i.getOriginalPrice());
                ol.setOriginalTotalPrice(i.originalAmount());
                ol.setPricePromotionId(i.isSpecialFlag()?i.getPricePromotionId():null);
                ol.setGiftModelId(i.getGiftModelId());
                SpringUtil.copyProperties(ol, gift);
                //order commodity sys --> start
                List<Commodity> commodityList = commodityService.findCommodityByIdList(Arrays.asList(ol.getCommodityId()));
                if(SpringUtil.isNotEmpty(commodityList)){
                    Commodity commodity = commodityList.get(0);
                    ol.setCommodityCode(commodity.getCommodityCode());
                    ol.setCommodityName(commodity.getCommodityName());
                    ol.setCommoditySpec(commodity.getCommoditySpec());
                }

                //order commodity sys --> end
                orderListGiftMapper.insertSelective(gift);
                long itemId = gift.getId();
                i.setItemId(itemId);
                ol.setId(itemId);
                orderList.add(ol);
            });
        }
    }

    private void updateOrder(Order order, OrderDto orderDto, Long userId,String userName) {
        //原订单金额
        BigDecimal orderAmount = order.getOrderAmount();
        List<OrderList> oldOrderList = orderListMapper.findByOrderIdAndProductType(order.getId(), ProductTypeEnums.PRODUCT.getCode());
        if (CollectionUtils.isNotEmpty(oldOrderList)) {
            Integer orderStatus = oldOrderList.get(0).getOrderStatus();
            if (orderStatus != 0) {
                QYAssert.isTrue(false, "订单已取消或者删除!");
            }
        }
        List<OrderList> orderList = new ArrayList<OrderList>();
        Example delExp = new Example(OrderList.class);
        delExp.createCriteria().andEqualTo("orderId", order.getId());
        int rowNumber = orderListMapper.deleteByExample(delExp);
        if (order.getModeType().intValue() == OrderModeType.REPLENISHMENT.getCode().intValue()) {
            orderListGiftMapper.deleteByExample(delExp);
            if (rowNumber > 0) {
                orderDto.getItems().forEach(i -> {
                    OrderList list = new OrderList();
                    list.setCommodityId(Long.valueOf(i.getProductId()));
                    list.setCommodityNum(i.getProductNum());
                    list.setType(i.getType());
                    list.setRemark(i.getRemark());
                    list.setCommodityPrice(i.getPrice());
                    list.setOrderId(order.getId());
                    list.setTotalPrice(i.amount());
                    list.setOriginalPrice(i.getOriginalPrice());
                    list.setOriginalTotalPrice(i.originalAmount());
                    list.setPricePromotionId(i.isSpecialFlag()?i.getPricePromotionId():null);
                    orderListMapper.insertSelective(list);

                    OrderListGift gift = new OrderListGift();
                    SpringUtil.copyProperties(list, gift);
                    gift.setId(null);
                    orderListGiftMapper.insertSelective(gift);
                    i.setItemId(gift.getId());

                    List<Commodity> commodityList = commodityService.findCommodityByIdList(Arrays.asList(list.getCommodityId()));
                    if(SpringUtil.isNotEmpty(commodityList)){
                        Commodity commodity = commodityList.get(0);
                        list.setCommodityCode(commodity.getCommodityCode());
                        list.setCommodityName(commodity.getCommodityName());
                        list.setCommoditySpec(commodity.getCommoditySpec());
                    }

                    orderList.add(list);
                });
                order.setOrderTime(DateTimeUtil.parse(orderDto.getOrderTime(), "yyyy-MM-dd"));
                order.setOrderAmount(orderDto.amount());
                order.setFinalAmount(orderDto.amount());
                QYAssert.isTrue(null != order.getOrderAmount() && order.getOrderAmount().compareTo(BigDecimal.ZERO) > 0, "订单金额必须>0");
                //根据订单金额和最终金额，修改覆盖状态
                order.setSyncStatus(order.getOrderAmount().compareTo(order.getFinalAmount()) == 0 ? 1 : 0);
                order.setOrderDurationTime(getOrderDurationTime(order.getStoreId(), orderDto.getOrderTime()));
                orderMapper.updateByPrimaryKeySelective(order);


                Date updateTime = order.getUpdateTime();
                Boolean preStore = storeRechargeService.isPreStore(order.getStoreId());
                if (BooleanUtils.isTrue(preStore)) {

                    // 逻辑调整，如果新订单金额和原订单金额一致，则不做操作
                    // 如果新订单金额大于原订单金额，做扣款操作(新订单金额 - 原订单金额)
                    // 如果新订单金额小于原订单金额，做充值操作(原订单金额 - 新订单金额)
                    addChargeOrDeduction(order, orderDto.amount(), userId, orderAmount, updateTime);
                }
            }
        } else {
            if (rowNumber > 0) {
                orderDto.getGiftItems().forEach(i -> {
                    OrderList list = new OrderList();
                    list.setCommodityId(Long.valueOf(i.getProductId()));
                    list.setCommodityNum(i.getProductNum());
                    list.setType(i.getType());
                    list.setRemark(i.getRemark());
                    list.setCommodityPrice(i.getPrice());
                    list.setOrderId(order.getId());
                    list.setTotalPrice(i.amount());
                    list.setOriginalPrice(i.getOriginalPrice());
                    list.setOriginalTotalPrice(i.originalAmount());
                    list.setPricePromotionId(i.isSpecialFlag()?i.getPricePromotionId():null);
                    list.setGiftModelId(i.getGiftModelId());
                    orderListMapper.insertSelective(list);

                    List<Commodity> commodityList = commodityService.findCommodityByIdList(Arrays.asList(list.getCommodityId()));
                    if(SpringUtil.isNotEmpty(commodityList)){
                        Commodity commodity = commodityList.get(0);
                        list.setCommodityCode(commodity.getCommodityCode());
                        list.setCommodityName(commodity.getCommodityName());
                        list.setCommoditySpec(commodity.getCommoditySpec());
                    }

                    orderList.add(list);
                });
                order.setOrderTime(DateTimeUtil.parse(orderDto.getOrderTime(), "yyyy-MM-dd"));
                order.setOrderAmount(orderDto.giftAmount());
                order.setFinalAmount(orderDto.amount());
                QYAssert.isTrue(null != order.getOrderAmount() && order.getOrderAmount().compareTo(BigDecimal.ZERO) > 0, "订单金额必须>0");
                //根据订单金额和最终金额，修改覆盖状态
                order.setSyncStatus(order.getOrderAmount().compareTo(order.getFinalAmount()) == 0 ? 1 : 0);
                order.setOrderDurationTime(getOrderDurationTime(order.getStoreId(), orderDto.getOrderTime()));
                orderMapper.updateByPrimaryKeySelective(order);

                order.setOrderList(orderList);

                Date updateTime = order.getUpdateTime();
                Boolean preStore = storeRechargeService.isPreStore(order.getStoreId());
                if (BooleanUtils.isTrue(preStore)) {
                    // 逻辑调整，如果新订单金额和原订单金额一致，则不做操作
                    // 如果新订单金额大于原订单金额，做扣款操作(新订单金额 - 原订单金额)
                    // 如果新订单金额小于原订单金额，做充值操作(原订单金额 - 新订单金额)
                    addChargeOrDeduction(order, orderDto.giftAmount(), userId, orderAmount, updateTime);
                }
            }
        }
        User user = new User();
        user.setUserId(userId);
        user.setRealName(userName);
        user.setEmployeeName(userName);
        List<OrderList> newOrderHistoryList = orderList.stream().filter(o -> ProductTypeEnums.PRODUCT.getCode().equals(o.getType())).collect(toList());
        List<OrderHistory> orderHistoryList = OrderHistoryHelper.orderHistoryList(oldOrderList, newOrderHistoryList, OrderHistoryAddTypeEnums.COMMON, user);
        if (!order.getOrderTime().equals(oldOrderList.get(0).getOrderTime())) {
            OrderHistory orderHistory = OrderHistory.order_modify(order.getId(), order.getOrderCode(), userId, userName, new Date()
                    , DateTimeUtil.formatDate(order.getOrderTime(), "yyyy-MM-dd"), DateTimeUtil.formatDate(oldOrderList.get(0).getOrderTime(), "yyyy-MM-dd"));

            orderHistoryMapper.insertSelective(orderHistory);
        }
        if (!orderHistoryList.isEmpty()) {
            orderHistoryMapper.insertList(orderHistoryList);
        }
    }

    /**
     *
     * @param order
     * @param newOrderAmount  新订单金额
     * @param userId
     * @param fromOrderAmount  原订单金额
     * @param updateTime
     */
    private void addChargeOrDeduction(Order order, BigDecimal newOrderAmount, Long userId, BigDecimal fromOrderAmount, Date updateTime) {

        // 扣款
        if(newOrderAmount.compareTo(fromOrderAmount) > 0){
            StoreDeductionBO deductionBO = StoreDeductionBO.builder()
                    .orderCode(order.getOrderCode())
                    .orderId(order.getId())
                    .orderAmount(newOrderAmount.subtract(fromOrderAmount))
                    .storeId(order.getStoreId())
                    .tradeCode(order.getOrderCode())
                    .tradeCode(order.getOrderCode() + "_" + TimeUtil.toString(updateTime, TimeUtil.DATE_FORMAT_LONG))
                    .tradeTime(new Date())
                    .orderTime(order.getOrderTime())
                    .billType(StoreBillTypeEnums.PC_DEDUCTION.getCode())
                    .remark("<--修改订单差额扣款：" + order.getOrderCode() + " -->")
                    .userId(userId)
                    .build();
            try {
                storeRechargeService.storeDeduction(deductionBO);
            }catch (Exception e){
                log.warn("余额不足，修改失败，orderCode {}", order.getOrderCode());
                throw new RuntimeException("余额不足,修改失败！");
            }
        }

        //回款
        if(fromOrderAmount.compareTo(newOrderAmount) > 0) {
            StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                    .orderCode(order.getOrderCode())
                    .tradeCode(order.getOrderCode() + "_" + TimeUtil.toString(updateTime, TimeUtil.DATE_FORMAT_LONG))
                    .money(fromOrderAmount.subtract(newOrderAmount).doubleValue())
                    .storeId(order.getStoreId())
                    .tradeTime(new Date())
                    .receiptDate(new Date())
                    .billType((StoreBillTypeEnums.PC_REFUNDMENT.getCode()))
                    .remark("<--修改订单差额回款：" + order.getOrderCode() + " -->")
                    .userId(userId)
                    .build();
            storeRechargeService.storeRecharge(rechargeBO);
        }
    }


    public List<Commodity> findCommodityByOrderId(Long orderId,String orderTime,Integer productType){
        return orderMapper.findCommodityByOrderId(orderId,orderTime,productType);
    }


    public void processCommodityPromotion(List<Commodity> commodityList, Map<Long, StorePromotionCommodityPriceODTO> storePromotionMap) {
        commodityList.forEach(
                commodity -> {
                    Long commodityId = commodity.getId();
                    StorePromotionCommodityPriceODTO storePromotionCommodityPriceODTO = storePromotionMap.get(commodityId);
                    if (storePromotionCommodityPriceODTO != null
                            && storePromotionCommodityPriceODTO.getPrice().compareTo(commodity.getCommodityPrice()) < 0) {
                        commodity.setOriginalCommodityPrice(commodity.getCommodityPrice());
                        commodity.setCommodityPrice(storePromotionCommodityPriceODTO.getPrice());
                        Integer limitNumber = storePromotionCommodityPriceODTO.getLimitNumber();
                        String tagName;
                        if (0 == limitNumber) {
                            tagName = "特价";
                        } else {
                            tagName = "特价|每天限[" + limitNumber + "]份";
                        }
                        commodity.setPromotionFlag(1);
                        commodity.setTagName(tagName);
                        commodity.setLimitNumber(storePromotionCommodityPriceODTO.getLimitNumber());
                        commodity.setAvailableStoreLimit(storePromotionCommodityPriceODTO.getAvailableStoreLimit());
                        commodity.setAvailableTotalLimit(storePromotionCommodityPriceODTO.getAvailableTotalLimit());


                        //处理特价
                        BigDecimal productNum = commodity.getProductNumber();
                        if(productNum !=null){
                            BigDecimal availableStoreLimit = commodity.getAvailableStoreLimit();
                            BigDecimal availableTotalLimit = commodity.getAvailableTotalLimit();
                            BigDecimal availableLimit = availableTotalLimit.min(availableStoreLimit);
                            BigDecimal specialQuantity;
                            BigDecimal normalQuantity;
                            BigDecimal noneLimit = new BigDecimal(999999999);

                            if( 0 == limitNumber && noneLimit.compareTo(availableTotalLimit) == 0){
                                specialQuantity = productNum;
                                normalQuantity = BigDecimal.ZERO;
                            }else{
                                BigDecimal salesBoxCapacity = BigDecimal.valueOf(commodity.getSalesBoxCapacity());
                                availableLimit = salesBoxCapacity.multiply(availableLimit.divide(salesBoxCapacity,0,RoundingMode.FLOOR));

                                BigDecimal productNumSalesBoxCapacity = salesBoxCapacity.multiply(productNum.divide(salesBoxCapacity,0,RoundingMode.FLOOR));
                                specialQuantity = availableLimit.min(productNumSalesBoxCapacity);
                                normalQuantity = productNum.subtract(specialQuantity);
                            }
                            commodity.setPromotionCount(specialQuantity);
                            commodity.setNormalCount(normalQuantity);
                        }

                    }
                }
        );
    }

    public List<Order> findReplenishmentOrderListByParams(Long storeId, Integer orderModeType, String orderTime, int orderStatus) {
        Example example = new Example(Order.class);
        example.createCriteria().andEqualTo("storeId",storeId)
                .andEqualTo("modeType",orderModeType)
                .andEqualTo("orderTime",orderTime)
                .andEqualTo("orderStatus",orderStatus);
        return orderMapper.selectByExample(example);
    }

    /**
     * 订单补货
     *
     * @param orderRequestDto
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderDto saveManualOrder(OrderRequestDto orderRequestDto) {
        QYAssert.isTrue(StringUtils.isNotBlank(orderRequestDto.getOrderTime()), "送货日期不能为空!");
        Date orderTime = DateTimeUtil.parse(orderRequestDto.getOrderTime(), DateTimeUtil.YYYY_MM_DD);
        QYAssert.isTrue(orderTime != null, "送货日期格式异常!");
        QYAssert.isTrue(orderTime.after(new Date()), "送货日期必须大于今天!");

        OrderDto orderDto = new OrderDto();
        if (SpringUtil.isNotEmpty(orderRequestDto.getItemsList())) {
            Map<Long, Commodity> commMap = checkCommodityInfo(orderRequestDto);

            SpringUtil.copyProperties(orderRequestDto, orderDto);
            orderDto.setItems(new ArrayList<OrderItemDto>());

            List<String> productIds = orderRequestDto.getItemsList().stream().map(OrderItemRequestDto::getProductId).collect(toList());
            List<Commodity> commodities = commodityService.findCommodityByIdList(productIds.stream().map(Long::valueOf).collect(toList()));
            Map<Long, Commodity> commoditiesMap = commodities.stream().collect(toMap(Commodity::getId, Function.identity()));

            orderRequestDto.getItemsList().forEach(i -> {
                OrderItemDto orderItemDto = new OrderItemDto(i.getProductId(), BigDecimal.ZERO, BigDecimal.ZERO, i.getProductNum(), i.getRemark(), ProductTypeEnums.PRODUCT.getCode());
                Commodity commodity = commoditiesMap.get(Long.valueOf(i.getProductId()));
                orderItemDto.setIsWeight(commodity.getIsWeight());
                orderItemDto.setCommodityPackageSpec(commodity.getCommodityPackageSpec());
                orderItemDto.setSalesBoxCapacity(BigDecimal.valueOf(commodity.getSalesBoxCapacity()));
                orderDto.addItem(orderItemDto);
            });

            //耗材商品校验
            this.checkConsumableProduct(orderDto);
            /***
             * 取消了速冻商品的校验 调整为凑整商品 需求如下：
             * @see http://192.168.0.213/zentao/story-view-10044.html
             *
             */
            //凑整商品校验
            this.checkRoundedGoods(orderDto);
            this.buildProductPrice(orderDto);
            // 移除价格为空的商品
            this.processCommodityPrice(orderDto);
            QYAssert.isTrue(CollectionUtils.isNotEmpty(orderDto.getItems()), "有效商品不能为空!");

            //处理特价拆行
            List<OrderItemDto> priceLimitOrderItems = processPriceLimit(orderDto);
            //没有价格-> 移除
            this.processItemComm(priceLimitOrderItems);

            orderDto.setItems(priceLimitOrderItems);
            QYAssert.isTrue(storeService.matchBillCondition(orderDto.getStoreId(), orderDto.amount()), "预付款余额不足,无法下单!");
            /***
             * 查询大仓库存依据 获得商品库存依据
             */
            Map<Long, CommodityInventoryODTO> commodityInventoryMap = new HashMap<>();
            Map<Integer, Map<Long, CommodityInventoryODTO>> inventoryMap = queryCommodityInventory(orderDto,null, orderRequestDto.getOrderTime(), commodityInventoryMap);

            Order order = new Order();
            this.insertOrder(order, orderDto, OrderModeType.REPLENISHMENT.getCode());
            Long orderId = order.getId();
            orderDto.setId(orderId);
            /***
             * 调用大仓冻结接口
             */
            Map<String, String> storageUnderStockMap = this.execWarehouseFreezeInventory(orderDto,  orderRequestDto, order, commodityInventoryMap, false, "一期订单补货");
            if (SpringUtil.isNotEmpty(storageUnderStockMap)) {
                // 报错就将当前订单删除
                orderService.deleteOrder(order.getId());
                return new OrderDto(storageUnderStockMap);
            }

            try {
                saveOrder(order, orderDto, OrderModeType.REPLENISHMENT.getCode(), orderRequestDto.getUserId(),orderRequestDto.getUserName());
                orderRequestDto.setOrderId(orderId);
                orderLimitQuantityService.saveOrderLimitQuantity(orderId,1,false);
                Boolean preStore = storeRechargeService.isPreStore(order.getStoreId());
                if (BooleanUtils.isTrue(preStore)) {
                    //扣款
                    StoreDeductionBO deductionBO = StoreDeductionBO.builder()
                            .orderCode(order.getOrderCode())
                            .orderId(order.getId())
                            .orderAmount(order.getOrderAmount())
                            .storeId(order.getStoreId())
                            .tradeCode(order.getOrderCode())
                            .tradeTime(new Date())
                            .orderTime(order.getOrderTime())
                            .billType(StoreBillTypeEnums.PC_DEDUCTION.getCode())
                            .remark("<--PC扣款：" + order.getOrderCode() + " -->")
                            .userId(orderRequestDto.getUserId())
                            .build();
                    storeRechargeService.storeDeduction(deductionBO);
                }
            } catch (Exception e) {
                log.warn("订单补货失败error:{}", e);
                //不允许通达补货
                Boolean flag = this.processWarehouseUnfreezeInventory(orderId, DeliveryOrderTypeEnums.SALE.getCode());
                if (flag != null && !flag.booleanValue()) {
                    log.warn("订单补货失败,大仓库存解冻失败 订单id：{}、订单号：{}", orderId, order.getOrderCode());
                }
                QYAssert.isTrue(false, "订单补货失败");
            }

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    printerOrder(orderRequestDto.getUserId(), orderId,orderRequestDto.getPrintType());
                }
            });
        }
        return orderDto;
    }

    // 没有价格-> 移除
    private List<OrderItemDto> processItemComm(List<OrderItemDto> priceLimitOrderItems) {
        if (priceLimitOrderItems.isEmpty()) {
            return null;
        }
        return priceLimitOrderItems.stream().filter(p -> {
            return (null != p.getPrice() && p.getPrice().compareTo(BigDecimal.ZERO) > 0);
        }).collect(Collectors.toList());
    }


    /**
     * 订单打印
     * @param orderIdList
     * @return
     */
    public Boolean printerOrder(List<Long> orderIdList, Long userId) {
        QYAssert.isTrue(CollectionUtils.isNotEmpty(orderIdList), "请选择打印数据！");

        Example orderExample = new Example(Order.class);
        orderExample.createCriteria().andIn("id", orderIdList);
        orderExample.orderBy("createTime").desc();
        List<Order> orderList = orderMapper.selectByExample(orderExample);
        if(CollectionUtils.isNotEmpty(orderList)){
            List<Long> idList = orderList.stream().map(item -> item.getId()).collect(Collectors.toList());
            OrderPrintIDTO orderPrintIDTO = new OrderPrintIDTO();
            orderPrintIDTO.setOrderIdList(idList);
            orderPrintIDTO.setUserId(userId);
            try {
                printInfoClient.print(orderPrintIDTO);
            }catch (Exception e){
                log.warn("打印订单异常", e);
            }
        }

        return true;
    }

    /**
     * 订单批量打印
     */
    public Integer subPrinter(OrderBatchPrintRequestIDTO printRequestIDTO) {
        QYAssert.isTrue(StringUtils.isNotBlank(printRequestIDTO.getOrderTime()), "送货日期不能为空");
        //QYAssert.isTrue(StringUtils.isNotBlank(printRequestIDTO.getEmpCode()), "操作员编码不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(printRequestIDTO.getStoreCodes()), "客户编码不能为空");
        TokenInfo token = FastThreadLocalUtil.getQY();

        Set<String> storeCodes = getStoreCodes(printRequestIDTO);
        QYAssert.isTrue(storeCodes.size() > 0, "请输入客户编码！");

        List<Store> storeList = storeMapper.getStoreByCodeList(new ArrayList<>(storeCodes));
        QYAssert.isTrue(CollectionUtils.isNotEmpty(storeList), "客户编码不合法！");

        // storeIdList 顺序与 storeCodes 保持一致
        List<Long> storeIdList = storeCodes.stream()
                .flatMap(storeCode -> storeList.stream()
                        .filter(store -> store.getStoreCode().equals(storeCode))
                        .map(Store::getId))
                .collect(Collectors.toList());

        Example orderExample = new Example(Order.class);
        Example.Criteria c = orderExample.createCriteria();
        c.andEqualTo("orderTime", printRequestIDTO.getOrderTime())
                .andEqualTo("orderStatus", YesOrNoEnums.NO.getCode())
                .andIn("storeId", storeIdList);

        // 职员编码不为空，就过滤当前职员的订单
        if(StringUtils.isNotBlank(printRequestIDTO.getEmpCode())){
            Example employeeExample = new Example(EmployeeUser.class);
            employeeExample.createCriteria().andEqualTo("employeeCode", printRequestIDTO.getEmpCode());
            EmployeeUser employeeUser = employeeUserMapper.selectOneByExample(employeeExample);
            QYAssert.notNull(employeeUser, "当前职员编码无效！");

            c.andEqualTo("createId", employeeUser.getUserId());
        }

        orderExample.orderBy("createTime").desc();
        List<Order> orderList = orderMapper.selectByExample(orderExample);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(orderList), "没有有效订单！");

        if(CollectionUtils.isNotEmpty(orderList)) {
            // 按客户编码顺序排序
            Map<Long, Integer> storeIdIndexMap = IntStream.range(0, storeIdList.size())
                    .boxed()
                    .collect(toMap(storeIdList::get, index -> index));
            List<Long> idList = orderList.stream()
                    .sorted(Comparator.comparing(order -> storeIdIndexMap.get(order.getStoreId())))
                    .map(BaseIDPO::getId)
                    .collect(Collectors.toList());
            OrderPrintIDTO orderPrintIDTO = new OrderPrintIDTO();
            orderPrintIDTO.setOrderIdList(idList);
            orderPrintIDTO.setUserId(token.getUserId());
            // 按订单顺序打印
            orderPrintIDTO.setUseOrderedProcess(true);
            printInfoClient.print(orderPrintIDTO);
        }

        return orderList.size();
    }

    private @NotNull Set<String> getStoreCodes(OrderBatchPrintRequestIDTO printRequestIDTO) {
        String code = printRequestIDTO.getStoreCodes();
        String[] storeCodesArray = new String[]{code};
        if (code.contains(",")) {
            storeCodesArray = code.split(",");
        } else if (code.contains("，")) {
            storeCodesArray = code.split("，");
        } else if (code.contains("、")) {
            storeCodesArray = code.split("、");
        } else if (code.contains(";")) {
            storeCodesArray = code.split(";");
        } else if (code.contains("；")) {
            storeCodesArray = code.split("；");
        } else if (code.contains("\r\n")) {
            storeCodesArray = code.split("\r\n");
        } else if (code.contains("\n")) {
            storeCodesArray = code.split("\n");
        }

        StringBuffer sb = new StringBuffer();
        Set<String> storeCodes = new LinkedHashSet<>();
        for (String s : storeCodesArray) {
            if (StringUtils.isNotBlank(s)) {
                storeCodes.add(s.trim());
            }
        }
        return storeCodes;
    }

    public List<Order> searchOrderListByStoreId(Long storeId, String orderTime, Integer orderStatus) {
        if (null == storeId) {
            return null;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("storeId", storeId);
        paramMap.put("orderTimeStart", orderTime + " 00:00:00");
        paramMap.put("orderTimeEnd", orderTime + " 23:59:59");
        paramMap.put("orderStatus", orderStatus);
        List<Order> list = orderService.findStoreSearch(paramMap);
        return list;
    }

    public Map<String, Object> orderDetail(Long orderId) {
        Map<String,Object> result = new HashMap<>();
        Order order = orderService.findOrderById(orderId);
        Store store = storeService.findStoreByStoreId(order.getStoreId());
        StoreArea storeArea = storeAreaService.findStoreAreaById(store.getStoreDistrictId());//区域

        List<Settlement> settlementList = settlementService.findSettlementByStoreId(store.getId());//结账客户
        StoreSettlement storeSettlment = storeSettlementService.findByStoreId(store.getId());
        if (storeSettlment != null && storeSettlment.getCollectPrice() != null) {
            store.setBalance(BigDecimal.valueOf(storeSettlment.getCollectPrice()));
        }
        if (CollectionUtils.isNotEmpty(settlementList)) {
            store.setSettlementCustomerName(settlementList.get(0).getCustomerName());
        }
        if (storeArea != null) {
            store.setStoreDistrictName(storeArea.getAreaName());
        }
        DictionaryODTO d = dictionaryClient.getDictionaryById(store.getStoreTypeId());
        if (d != null) {
            order.setStoreTypeName(d.getOptionName());
        }
        order.setLogisticsCarrierCodeName(SfScsProductCodeEnum.getName(order.getLogisticsCarrierCode()));
        OrderMirror orderMirror = orderMirrorService.findOrderMirrorByOrderId(orderId);
        Boolean isTdaStore = tdaOrderService.isTdaStore(order.getStoreId());
        if (isTdaStore) {
            if (null != order.getDriverId()) {
                String  employeeName = employeeUserMapper.queryEmployeeUserByUserId(order.getDriverId());
                store.setDeliveryManId(order.getDriverId());
                if (StringUtils.isNotBlank(employeeName)) {
                    store.setDeliverymanName(employeeName);
                }
            }
        } else {
            store.setDeliverymanId(orderMirror.getDeliveryManId());
            store.setDeliverymanName(orderMirror.getDeliveryManName());
        }

        store.setSupervisorId(orderMirror.getSupervisionId());
        store.setSupervisorName(orderMirror.getSupervisionName());
        store.setSalesmanId(orderMirror.getSalesmanId());
        store.setSalesmanName(orderMirror.getSalesmanName());

        String createUserName = employeeUserMapper.queryEmployeeUserByUserId(order.getCreateId());
        if (StringUtils.isNotBlank(createUserName))
            order.setCreateUserName(createUserName);
        else
            order.setCreateUserName(store.getStoreName());
        List<OrderList> allOrderLists = orderListService.findByOrderId(orderId);
        List<OrderList> orderLists = allOrderLists.stream().filter(orderList -> ProductTypeEnums.PRODUCT.getCode().equals(orderList.getType())).collect(toList());
//        List<OrderList> orderLists = orderListService.findByOrderIdAndProductType(orderId, ProductTypeEnums.PRODUCT.getCode());
        List<OrderList> giftLists = allOrderLists.stream().filter(orderList -> ProductTypeEnums.GIFT.getCode().equals(orderList.getType())).collect(toList());
//        List<OrderList> giftLists = orderListService.findByOrderIdAndProductType(orderId, ProductTypeEnums.GIFT.getCode());//赠品
        List<OrderList> rationLists = allOrderLists.stream().filter(orderList -> ProductTypeEnums.RATION.getCode().equals(orderList.getType())).collect(toList());
//        List<OrderList> rationLists = orderListService.findByOrderIdAndProductType(orderId, ProductTypeEnums.RATION.getCode());//配货
        List<OrderList> specialLists = allOrderLists.stream().filter(orderList -> ProductTypeEnums.TH.getCode().equals(orderList.getType())).collect(toList());
//        List<OrderList> specialLists = orderListService.findByOrderIdAndProductType(orderId, ProductTypeEnums.TH.getCode());//特惠
        wrapOrderCouponDiscountAmount(order,orderLists);
        wrapOrderListsCouponCode(orderLists);

        Integer businessType = order.getBusinessType();
        if(Objects.equals(businessType,BusinessTypeEnums.PLAN_SALE.getCode())
        ||Objects.equals(businessType,BusinessTypeEnums.B_COUNTRY.getCode())){
            SelectOrderTransportInfoInfoIDTO idto = new SelectOrderTransportInfoInfoIDTO();
            idto.setOrderId(orderId);
            OrderTransportInfoODTO orderTransportInfoODTO = transportClient.selectOrderTransportInfoInfo(idto);
            //物流消息
            result.put("logisticsInfo",orderTransportInfoODTO);
        }

        result.put("order", order);
        result.put("store", store);
        result.put("orderLists", orderLists.stream().filter(orderList -> CombTypeEnum.COMB_CHILD.getCode().intValue() != orderList.getCombType()).collect(Collectors.toList()));
        result.put("giftLists", giftLists.stream().filter(orderList -> CombTypeEnum.COMB_CHILD.getCode().intValue() != orderList.getCombType()).collect(Collectors.toList()));
        result.put("rationLists", rationLists.stream().filter(orderList -> CombTypeEnum.COMB_CHILD.getCode().intValue() != orderList.getCombType()).collect(Collectors.toList()));
        result.put("specialLists", specialLists.stream().filter(orderList -> CombTypeEnum.COMB_CHILD.getCode().intValue() != orderList.getCombType()).collect(Collectors.toList()));
        return result;

    }

    private void wrapOrderListsCouponCode(List<OrderList> orderLists) {

        List<Long> couponIds = orderLists.stream().map(OrderList::getCouponId).filter(Objects::nonNull).collect(toList());
        if(SpringUtil.isNotEmpty(couponIds)){
            Map<Long, String> couponIdCodeMap = mtCouponTobClient.queryCouponCodeMap(couponIds);
            orderLists.forEach(
                    orderList -> {
                        Long couponId = orderList.getCouponId();
                        if(Objects.nonNull(couponId)) {
                            orderList.setCouponCode(couponIdCodeMap.get(couponId));
                        }
                    }
            );
        }
    }

    private void wrapOrderCouponDiscountAmount(Order order,List<OrderList> orderLists) {
        BigDecimal couponDiscountAmountTotal = orderLists.stream()
                .map(OrderList::getCouponDiscountAmount)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        order.setCouponDiscountAmount(couponDiscountAmountTotal);
    }

    public EditOrderDto editOrder(Long orderId,String editType) {
       return orderDetailStrategyFactory.detail(orderId,editType);
    }

    /**
     * 一期订单回款
     *
     * @param userId 用户id
     * @param order  订单信息
     */
    public void orderCupRefund(Long userId, Order order) {
        // 更新预付款余额，订单回款记录对账明细
        Boolean preStore = storeRechargeService.isPreStore(order.getStoreId());
        //非预付费客户 不处理
        if (!Objects.equals(preStore, Boolean.TRUE)) {
            return;
        }
        String remark = "<--PC回款:" + order.getOrderCode() + " -->";
        int billType = StoreBillTypeEnums.PC_REFUNDMENT.getCode();
        Boolean isXsJmShop = shopService.isJmShop(order.getStoreId());
        if (BooleanUtils.isTrue(isXsJmShop)) {
            remark = "<--门店订货回款：" + order.getOrderCode() + " -->";
            billType = StoreBillTypeEnums.SHOP_ORDER_DEDUCTION.getCode();
        }
        //回款
        StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                .orderCode(order.getOrderCode())
                .tradeCode(order.getOrderCode())
                .money(order.getOrderAmount().doubleValue())
                .storeId(order.getStoreId())
                .tradeTime(new Date())
                .receiptDate(new Date())
                .billType(billType)
                .remark(remark)
                .userId(userId)
                .build();
        storeRechargeService.storeRecharge(rechargeBO);

        // 退还运费
        BigDecimal freightAmount = order.getFreightAmount();
        if (freightAmount != null && freightAmount.compareTo(BigDecimal.ZERO) > 0) {
            billType = StoreBillTypeEnums.PC_FREIGHT_DEDUCTION.getCode();
            remark = "<--PC回款（运费）:" + order.getOrderCode() + " -->";
            StoreRechargeBO rechargeFreightBO = StoreRechargeBO.builder()
                    .orderCode(order.getOrderCode())
                    // 运费的tradeCode加前缀，防止同一个tradeCode充值失败
                    .tradeCode("YF" + order.getOrderCode())
                    .money(freightAmount.doubleValue())
                    .storeId(order.getStoreId())
                    .tradeTime(new Date())
                    .receiptDate(new Date())
                    .billType(billType)
                    .remark(remark)
                    .userId(userId)
                    .build();
            storeRechargeService.storeRecharge(rechargeFreightBO);
        }
    }

    public JsonMsgBean checkCopyOrder(Long orderId) {

        JsonMsgBean json = new JsonMsgBean(true);

        Example orderExample = new Example(Order.class);
        orderExample.createCriteria().andEqualTo("id", orderId);
        List<Order> orderList = orderMapper.selectByExample(orderExample);

        QYAssert.isTrue(CollectionUtils.isNotEmpty(orderList), "无法复制该订单!");

        Order order = orderList.get(0);
        Long storeId = order.getStoreId();

        Integer storeStatus = storeCompanyClient.selectStoreCompanyByStoreId(storeId);
        if(null == storeStatus || storeStatus != 1){
            QYAssert.isTrue(false, "客户已停用!");
        }

        return json;

    }
}
