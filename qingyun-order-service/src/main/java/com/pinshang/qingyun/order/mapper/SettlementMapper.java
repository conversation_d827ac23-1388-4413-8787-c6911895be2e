package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.mapper.entry.settlement.SettlementEntry;
import com.pinshang.qingyun.order.model.settlement.Settlement;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SettlementMapper extends MyMapper<Settlement>{

    void  upXsStartBillDateBySettleId(@Param("id") Long id, @Param("xsStartBillDate") String xsStartBillDate);

    String findXsStartBillDateById(Long id);


    List<Long> findStoreIdsBySettleId(@Param("settleId") Long settleId);

    List<SettlementEntry> findByStr(@Param("str") String str);

    List<Settlement> selectSettlementByStoreId(Long storeId);
}
