package com.pinshang.qingyun.order.service.cup;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.OrderListMapper;
import com.pinshang.qingyun.order.mapper.cup.ConsumableLimitMapper;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.cup.ConsumableLimit;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.service.CommodityService;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryDetailIDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Slf4j
@Service
public class ConsumableLimitService {

    @Autowired
    private ConsumableLimitMapper consumableLimitMapper;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private OrderListMapper orderListMapper;

    /**
     * 订单取消，耗材商品数量加回去
     * @param storeId
     * @param productIdMap
     */
    public  void checkConsumableCommodityCancel(Long userId, Long storeId, Map<Long, BigDecimal> productIdMap) {
        List<Long> productIdList = productIdMap.keySet().stream().collect(Collectors.toList());

        List<ConsumableLimit> consumableLimitList = this.findByStoreIdAndCommodityIds(storeId,productIdList);

        if(CollectionUtils.isNotEmpty(consumableLimitList)){
            Date now = new Date();
            consumableLimitList.forEach(item->{
                Long commodityId = item.getCommodityId();
                if(productIdMap.containsKey(commodityId)){
                    //限量
                    Integer monthLimitNum = item.getMonthLimitNum();

                    // 已订数量
                    Integer monthUsedNum = null == item.getMonthUsedNum()?0:item.getMonthUsedNum();
                    //订单数量
                    Integer orderNum = productIdMap.get(commodityId).intValue();
                    if(monthUsedNum > 0 && monthUsedNum > orderNum ){
                        // 修改已使用的 耗材商品数量
                        Integer integer = monthUsedNum + orderNum ;
                        item.setMonthUsedNum(integer);
                    }else if(monthUsedNum > 0 && monthUsedNum < orderNum ){
                        item.setMonthUsedNum(0);
                    }
                    item.setUpdateId(userId);
                    item.setUpdateTime(now);
                    consumableLimitMapper.updateByPrimaryKeySelective(item);
                }
            });
        }
    }

    /**
     * * 落地的
     * 校验订单登记时 耗材商品
     * @param storeId
     * @param productIdMap
     */
    public void checkConsumableCommodityTwo(Long storeId, Map<String, BigDecimal> productIdMap) {
        List<String> productIdList = productIdMap.keySet().stream().collect(Collectors.toList());
        if(null != storeId && null != productIdList && productIdList.size() >0) {

            List<Long> commodityIdList = productIdList.stream().map(Long::valueOf).collect(Collectors.toList());

            List<ConsumableLimit> consumableLimitLis = this.findByStoreIdAndCommodityIds(storeId,commodityIdList);

            List<Commodity> commodityList = commodityService.findCommodityByIdList(commodityIdList);
            //所有的 商品map
            Map<Long, String> commodityIdMap = commodityList.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getCommodityName));
            consumableLimitLis.forEach(item->{
                String commodityId = item.getCommodityId().toString();
                if(productIdMap.containsKey(commodityId)){
                    //限量
                    Integer monthLimitNum = item.getMonthLimitNum();

                    // 已订数量
                    Integer monthUsedNum = null == item.getMonthUsedNum()?0:item.getMonthUsedNum();
                    //订单数量
                    Integer orderNum = productIdMap.get(commodityId).intValue();
                    if(null != monthLimitNum && monthLimitNum != 0){
                        if(orderNum + monthUsedNum > monthLimitNum){
                            int num = monthLimitNum - monthUsedNum;
                            QYAssert.isTrue(false, commodityIdMap.get(item.getCommodityId())+"月度限量"+monthLimitNum+"，本月已订"+monthUsedNum+"，还能订"+num+"。");
                        }
                        // 修改已使用的 耗材商品数量
                        Integer integer = monthUsedNum + productIdMap.get(commodityId).intValue();
                        item.setMonthUsedNum(integer);
                        consumableLimitMapper.updateByPrimaryKeySelective(item);

                    }else {
                    	QYAssert.isTrue(false, commodityIdMap.get(item.getCommodityId())+"月度限量未设置，暂不能订购该耗材，请联系督导。");
                    }
                }
            });
        }
    }

    public List<ConsumableLimit> findByStoreIdAndCommodityIds(Long storeId,List<Long> commodityIdList){

        Example consumableLimitExample = new Example(ConsumableLimit.class);
        consumableLimitExample.createCriteria().andEqualTo("storeId", storeId)
                .andIn("commodityId", commodityIdList);
        List<ConsumableLimit> consumableLimitList = consumableLimitMapper.selectByExample(consumableLimitExample);

        return consumableLimitList;
    }

    /**
     * 落地的
     * 修改订单
     * @param storeId
     * @param productIdMap
     * @param orderId
     */
    public void checkConsumableCommodityUpdate(Long storeId, Map<String, BigDecimal> productIdMap, Long orderId) {
        List<String> productIdList = productIdMap.keySet().stream().collect(Collectors.toList());
        // 原单 商品数量
        List<OrderList> byOrderId = orderListMapper.findByOrderIdAndProductType(orderId, ProductTypeEnums.PRODUCT.getCode());
        //Map<Long, BigDecimal> orderCommodityNum = byOrderId.stream().collect(Collectors.toMap(OrderList::getCommodityId, OrderList::getCommodityNum));
        Map<Long, BigDecimal> orderCommodityNum = byOrderId.stream()
                .collect(groupingBy(
                        idto -> idto.getCommodityId(),
                        Collectors.reducing(BigDecimal.ZERO, OrderList::getCommodityNum, BigDecimal::add)
                ));
        //所有的 商品map
        List<Commodity> commodityList = commodityService.findCommodityByIdList(productIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
        Map<Long, String> commodityIdMap = commodityList.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getCommodityName));

        //根据客户 & 订单商品 获取所有的限量商品
        List<ConsumableLimit> consumableLimitLis = findByStoreIdAndCommodityIds(storeId,productIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
        consumableLimitLis.forEach(item->{
            String commodityId = item.getCommodityId().toString();
            if(productIdMap.containsKey(commodityId)){
                //限量
                Integer monthLimitNum = item.getMonthLimitNum();
                // 已订数量
                Integer monthUsedNum = null == item.getMonthUsedNum()?0:item.getMonthUsedNum();
                //订单数量
                Integer orderNum = productIdMap.get(commodityId).intValue();

                Integer integer = 0;
                //如果原单有就获取
                if(orderCommodityNum.containsKey(item.getCommodityId())){
                    integer = orderCommodityNum.get(item.getCommodityId()).intValue();
                }
                //本月已定 : 本月一定 - 原单商品数量
                //还可以订 : monthLimitNum - monthUsedNum + integer
                if(null == monthLimitNum ){
                	QYAssert.isTrue(false, "耗材-" + commodityIdMap.get(item.getCommodityId())+"-月度限量未设置，请先设置月度限量！");
                }else if(monthLimitNum > monthUsedNum && monthLimitNum - monthUsedNum < orderNum - integer){
                	QYAssert.isTrue(false, commodityIdMap.get(item.getCommodityId())+"月度限量"+monthLimitNum+"，本月已订"+(monthUsedNum - integer)+"，还能订"+ (monthLimitNum - monthUsedNum + integer)+"。");
                }
                item.setMonthUsedNum(monthUsedNum + productIdMap.get(commodityId).intValue() -integer);

                consumableLimitMapper.updateByPrimaryKeySelective(item);
            }else {
            	QYAssert.isTrue(false, commodityIdMap.get(item.getCommodityId())+"月度限量未设置，暂不能订购该耗材，请联系督导。");
            }
        });
    }
}
