package com.pinshang.qingyun.order.dto.sync;

import com.pinshang.qingyun.order.dto.order.AssociationOrderODTO;
import com.pinshang.qingyun.order.enums.OrderPrintTypeEnum;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.model.store.Store;
import lombok.Data;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/7/26 13:57
 */
@Data
public class SyncOrderODTO {
    private Long orderId;

    private Long companyId;

    private Long storeId;

    private Integer deliveryBatch;

    private Integer modeType;

    private String orderCode;

    /**订单时间*/
    private Date orderTime;

    /**订单类型*/
    private Integer orderType;

    /**应付金额（参与促销活动之前的源始金额）*/
    private BigDecimal totalAmount;

    /**最终金额*/
    private BigDecimal finalAmount;

    /**订单金额*/
    private BigDecimal orderAmount;

    /**订单运费金额（免运费为0）*/
    private BigDecimal freightAmount;

    private Integer businessType;

    private Integer orderStatus;

    //是否直送 0=否  1=是
    private Integer directStatus;

    private String logisticsCarrierCode;

    private Long createId;

    private Date createTime;

    private Long updateId;

    private Date updateTime;

    private Long logisticsCenterId;

    private String deliveryTimeRange;

    private Long driverId;

    private Long storeTypeId;

    /**打印份数 **/
    private Integer printNum;
    /**打印类型(1：本地,2：送货员,3：不打印) **/

    private Integer printType;

    /**备注 **/
    private String orderRemark;

    /** 流程状态(鲜达新加)  7=待发货    11=出库中　15=配送中　　19=配送完成 */
    private Integer processStatus;
    /** 订单截止时间(鲜达新加),根据当前客户截止时间与订单中的最晚商品截止时间得来;订单取消与是否进大仓时使用 */
    private Date orderDurationTime;

    /**
     * 实发总金额
     **/
    private BigDecimal realTotalPrice;
    /**
     * 实发日期
     **/
    private Date realOrderTime;

    private Long consignmentId; // 代销商户id

    private Long cacelReasonId;

    /**
     * 是否预售:0=否，1=是
     */
    private Integer presaleStatus;

    private String logisticsCenter;

    /** 档口id */
    private Long stallId;

    private List<SyncOrderListODTO> syncOrderListODTOList;
}
