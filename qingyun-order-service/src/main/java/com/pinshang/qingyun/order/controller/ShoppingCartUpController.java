package com.pinshang.qingyun.order.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.dto.shopCartUp.ShoppingCartQueryIDTO;
import com.pinshang.qingyun.order.service.ShoppingCartUpService;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.ShoppingCartDetailVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: sk
 * @Date: 2025/4/8
 * 购物车列表和详情整合controller,后续再改购物车相关，让 pda 和 web 端指向该接口,
 * pda不强制升级，不升级还调用以前的方法
 *
 * 包含总部代理购物车列表、门店购物车列表、PDA购物车列表
 * 总部代理购物车详情、门店购物车详情、PDA购物车详情
 */
@RestController
@RequestMapping("/shoppingCartUp")
public class ShoppingCartUpController {

    @Autowired
    private ShoppingCartUpService shoppingCartUpService;

    /**
     * 总部登录、代理购物车列表
     * @param shoppingCartAdminPageIDTO
     * @return
     *
     * 原路径 com.pinshang.qingyun.api.order.ShoppingCartController#shoppingCartdetailsAdmin
     */
    @ApiOperation(value = "总部登录、代理购物车列表", notes = "总部登录、代理购物车列表")
    @RequestMapping(value="/shoppingCartAdminList", method = RequestMethod.POST)
    public TablePageInfo<ShoppingCartDetailVo> shoppingCartAdminList(@RequestBody ShoppingCartQueryIDTO shoppingCartQueryIDTO){
        TokenInfo ti = FastThreadLocalUtil.getQY();
        shoppingCartQueryIDTO.setUserId(ti.getUserId());

        ThreadLocalUtils.setAdmin(true);

        TablePageInfo<ShoppingCartDetailVo> tablePageInfo = new TablePageInfo<>();
        try{
            tablePageInfo  = shoppingCartUpService.shoppingCartAdminList(shoppingCartQueryIDTO);
        }finally {
            ThreadLocalUtils.remove();
        }
        return tablePageInfo;
    }


    /**
     * 门店、手持PDA购物车列表
     * @return
     *
     * 原路径 com.pinshang.qingyun.api.order.ShoppingCartController#shoppingCartdetails(web端)
     *       com.pinshang.qingyun.pdaapi.order.ShoppingCartController#shoppingCartdetails(pda端)
     */
    @ApiOperation(value = "门店、手持PDA购物车列表", notes = "门店、手持PDA购物车列表")
    @RequestMapping(value = "/shoppingCartList", method = RequestMethod.GET)
    public Object shoppingCartList(){
          return shoppingCartUpService.shoppingCartList();
    }


    /**
     * 总部代理购物车展示详情(单个)
     * @param shoppingCartQueryIDTO
     * @return
     * 原路径 com.pinshang.qingyun.api.order.ShoppingCartController#handleCartdetailAdmin(java.lang.Long)
     */
    @ApiOperation(value = "总部代理购物车展示详情(单个)", notes = "总部代理购物车展示详情(单个)")
    @RequestMapping(value = "/shoppingCartDetailAdmin", method = RequestMethod.POST)
    public Object shoppingCartDetailAdmin(@RequestBody ShoppingCartQueryIDTO shoppingCartQueryIDTO){
        shoppingCartQueryIDTO.setIsInternal(false);
        shoppingCartQueryIDTO.setHandle(true);
        ThreadLocalUtils.setAdmin(true);

        Object object = null;
        try {
            object =  shoppingCartUpService.shoppingCartDetail(shoppingCartQueryIDTO);
        }finally {
            ThreadLocalUtils.remove();
        }
        return object;
    }

    /**
     * 门店、PDA手持购物车展示详情(单个)
     * @param shoppingCartQueryIDTO
     * @return
     * 原路径 com.pinshang.qingyun.api.order.ShoppingCartController#handleCartdetail(java.lang.Long, java.lang.String)(web端)
     *       com.pinshang.qingyun.pdaapi.order.ShoppingCartController#handleCartdetail(java.lang.Long)(pda端)
     */
    @ApiOperation(value = "门店、PDA手持购物车展示详情(单个)", notes = "门店、PDA手持购物车展示详情(单个)")
    @RequestMapping(value = "/shoppingCartDetail", method = RequestMethod.POST)
    public Object shoppingCartDetail(@RequestBody ShoppingCartQueryIDTO shoppingCartQueryIDTO){
        TokenInfo ti = FastThreadLocalUtil.getQY();
        shoppingCartQueryIDTO.setIsInternal(ti.getIsInternal());
        shoppingCartQueryIDTO.setHandle(true);
        return shoppingCartUpService.shoppingCartDetail(shoppingCartQueryIDTO);
    }
}
