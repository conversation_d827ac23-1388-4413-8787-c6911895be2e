package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.AutoPreOrderAuditIDTO;
import com.pinshang.qingyun.order.mapper.ShoppingCartItemMapper;
import com.pinshang.qingyun.order.model.auto.AutoPreOrder;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.model.order.ShoppingCartItem;
import com.pinshang.qingyun.order.service.auto.AutoPreOrderService;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.order.BStockShortResponseVO;
import com.pinshang.qingyun.order.vo.order.CreateOrderVo;
import com.pinshang.qingyun.order.vo.order.CreateShopOrderFilterVo;
import com.pinshang.qingyun.order.vo.order.OverUnderOrderingCommodityVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 门店下单Service
 *
 * <AUTHOR>
 * @since 2025年02月19日
 */
@Slf4j
@Service
public class ShopOrderService {
    @Autowired
    private OrderService orderService;
    @Autowired
    private BStockService bStockService;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private AutoPreOrderService autoPreOrderService;
    @Autowired
    private ShoppingCartService shoppingCartService;
    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;
    @Autowired
    private OrderReferenceService orderReferenceService;
    @Autowired
    private ShoppingCartItemMapper shoppingCartItemMapper;

    /**
     * 单门店购物车提交订单
     *
     * @param orderVoList 订单信息列表
     * @return 下单结果
     */
    public CreateShopOrderFilterVo singleShopCreateOrder(List<CreateOrderVo> orderVoList) {
        // 1. 校验订单信息是否为空
        if (CollectionUtils.isEmpty(orderVoList)) {
            return CreateShopOrderFilterVo.success();
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        CreateOrderVo orderVo = orderVoList.get(0);
        Long cartId = orderVo.getShoppingCartId();

        // 2. 获取购物车信息
        ShoppingCart shoppingCart = shoppingCartService.queryShoppingCartById(cartId);
        QYAssert.isTrue(shoppingCart != null, "购物车不能为空，请刷新重试!");


        // 3. 校验档口权限
        boolean isBigShop = checkStallPermission(shoppingCart, tokenInfo.getShopId());

        // 4. 处理自动订货
        if (isAutoOrder(shoppingCart)) {
            return handleAutoOrder(isBigShop, orderVoList, cartId, tokenInfo.getUserId(), orderVo.getDeliveryBatch());
        }

        // 5. 设置订单基本信息
        setOrderBaseInfo(orderVoList, tokenInfo);

        // 6. 校验B端库存
        List<BStockShortResponseVO> stockShortList = checkBStockAndCreateOrder(orderVo, tokenInfo.getUserId());
        if (CollectionUtils.isNotEmpty(stockShortList)) {
            stockShortList.forEach(response -> response.setShoppingCartId(cartId));
            // 如果库存不足，返回提示信息
            return CreateShopOrderFilterVo.success(stockShortList);
        }

        // 7. 非强制提交时，校验订货商品和数量，对多订、少订的商品进行提示
        if (!Objects.equals(orderVo.getForceStatus(), YesOrNoEnums.YES.getCode())) {
            OverUnderOrderingCommodityVo checkResult = orderReferenceService.checkOverUnderOrdering(tokenInfo.getShopId(), shoppingCart.getStallId(), orderVo.getItems());
            if (checkResult != null && (CollectionUtils.isNotEmpty(checkResult.getOverOrderingCommodityVos()) || CollectionUtils.isNotEmpty(checkResult.getUnderOrderingCommodityVos()))) {
                // 如果有多订、少订商品，返回提示信息
                return CreateShopOrderFilterVo.fail(checkResult);
            }
        }

        // 8. 创建订单
        return BeanCloneUtils.copyTo(orderService.createOrder(orderVoList), CreateShopOrderFilterVo.class);
    }

    /**
     * 校验档口权限
     */
    private boolean checkStallPermission(ShoppingCart shoppingCart, Long shopId) {
        boolean isBigShop = (shoppingCart.getStallId() != null && shoppingCart.getStallId() > 0);
        if (isBigShop) {
            ddTokenShopIdService.processReadDdTokenShopId(shopId, shoppingCart.getStallId());
        }
        return isBigShop;
    }

    /**
     * 判断是否自动订货
     */
    private boolean isAutoOrder(ShoppingCart shoppingCart) {
        return shoppingCart.getAutoStatus() != null &&
                shoppingCart.getAutoStatus().equals(YesOrNoEnums.YES.getCode());
    }

    /**
     * 处理自动订货
     */
    private CreateShopOrderFilterVo handleAutoOrder(boolean isBigShop, List<CreateOrderVo> orderVoList, Long cartId, Long userId, String deliveryBatch) {
        // 大店不允许自动订货
        if (isBigShop) {
            QYAssert.isFalse("大店不允许加货");
        }

        // 更新购物车商品数量
        updateShoppingCartQuantity(orderVoList);

        // 提交审核
        CreateShopOrderFilterVo filterResult = BeanCloneUtils.copyTo(autoPreOrderService.submitAudit(cartId, userId, deliveryBatch), CreateShopOrderFilterVo.class);

        // 校验是否需要自动审核
        if (!isAutoAuditEnabled() || !(filterResult.getObject() instanceof AutoPreOrder)) {
            return filterResult;
        }

        // 执行自动审核
        AutoPreOrder autoPreOrder = (AutoPreOrder) filterResult.getObject();
        List<String> insufficientStockItems = performAutoAudit(autoPreOrder);

        filterResult.setObject(insufficientStockItems);
        return filterResult;
    }

    /**
     * 更新购物车商品数量
     */
    private void updateShoppingCartQuantity(List<CreateOrderVo> orderVoList) {
        if (CollectionUtils.isEmpty(orderVoList)) {
            return;
        }

        orderVoList.forEach(orderVo -> {
            if (CollectionUtils.isNotEmpty(orderVo.getItems())) {
                orderVo.getItems().forEach(item -> {
                    ShoppingCartItem cartItem = new ShoppingCartItem();
                    cartItem.setCommodityNum(item.getQuantity());
                    cartItem.setId(item.getShoppingCartItemId());
                    shoppingCartItemMapper.updateByPrimaryKeySelective(cartItem);
                });
            }
        });
    }

    /**
     * 设置订单基本信息
     */
    private void setOrderBaseInfo(List<CreateOrderVo> orderVoList, TokenInfo tokenInfo) {
        orderVoList.forEach(orderVo -> {
            orderVo.setEnterpriseId(tokenInfo.getEnterpriseId());
            orderVo.setStoreId(tokenInfo.getStoreId());
            orderVo.setCreateId(tokenInfo.getUserId());
            orderVo.setCreateName(tokenInfo.getRealName());
            orderVo.setInternal(tokenInfo.getIsInternal());
            for (CreateOrderVo.CreateOrderItemIDTO item : orderVo.getItems()) {
                QYAssert.isTrue(null != item && null != item.getQuantity(), "份数不能为空");
            }
        });
        Boolean isXd = shoppingCartService.getIsXd(tokenInfo.getStoreId());
        ThreadLocalUtils.setXd(isXd);
        ThreadLocalUtils.setOrderType(OrderTypeEnum.SHOP_PC_ORDER.getCode());
    }

    /**
     * 校验B端库存并创建订单
     */
    private List<BStockShortResponseVO> checkBStockAndCreateOrder(CreateOrderVo orderVo, Long userId) {
        // 构建订货数量Map
        Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderVo.getItems())) {
            orderVo.getItems().forEach(item ->
                    orderQuantityMap.put(item.getCommodityId(), item.getQuantity())
            );
        }
        // 校验B端库存
        return bStockService.checkBStock(
                orderVo.getStoreId(),
                OrderTypeEnum.SHOP_PC_ORDER.getCode(),
                DateUtil.parseDate(orderVo.getOrderTime(), "yyyy-MM-dd"),
                orderQuantityMap,
                "提交订单",
                null,
                userId
        );
    }

    /**
     * 是否开启自动审核
     */
    private boolean isAutoAuditEnabled() {
        try {
            DictionaryODTO autoAuditConfig = dictionaryClient.getDictionaryByCode("SHOP_AUTO_AUDIT_SWITCH");
            return Optional.ofNullable(autoAuditConfig)
                    .map(DictionaryODTO::getOptionValue)
                    .map(Boolean::parseBoolean)
                    .orElse(false);
        } catch (Exception e) {
            log.error("获取自动审核配置失败", e);
            return false;
        }
    }

    /**
     * 执行自动审核
     */
    private List<String> performAutoAudit(AutoPreOrder autoPreOrder) {
        AutoPreOrderAuditIDTO auditRequest = new AutoPreOrderAuditIDTO();
        auditRequest.setId(autoPreOrder.getId());
        auditRequest.setPassOrFail(Boolean.TRUE);
        return autoPreOrderService.auditPassOrFail(auditRequest);
    }

}
