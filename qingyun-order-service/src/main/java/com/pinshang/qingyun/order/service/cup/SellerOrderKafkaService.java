package com.pinshang.qingyun.order.service.cup;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.KafkaConstant;
import com.pinshang.qingyun.kafka.MessageOperationType;
import com.pinshang.qingyun.kafka.MessageType;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderListGift;
import com.pinshang.qingyun.order.service.CommonService;
import com.pinshang.qingyun.order.vo.order.ItemVo;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.order.vo.order.OrderItemDto;
import com.pinshang.qingyun.order.vo.order.OrderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class SellerOrderKafkaService {

    @Autowired
    CommonService commonService;

    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    /**
     * 结算订单同步信息
     * @param order
     * @param operationType
     */
    @Async
    public void sendSellerOrderKafka(Order order, OrderDto orderDto, MessageOperationType operationType, Long userId) {
        List<OrderVo> orderVos = new ArrayList<>();
        OrderVo orderVo = new OrderVo();
        orderVo.setSourceCode(order.getOrderCode());
        orderVo.setSourceId(order.getId());
        orderVo.setCompanyId(order.getCompanyId());
        orderVo.setOrderTime(order.getOrderTime());
        orderVo.setStoreId(order.getStoreId());
        orderVo.setSourceType("ORDER");
        orderVo.setSendMqTime(DateTimeUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        orderVo.setDeliveryTime(order.getOrderTime());
        orderVo.setCreateTime(order.getCreateTime());

        List<ItemVo> itemVos = new ArrayList<>();
        // 注意：这里的值为giftItems
        List<OrderItemDto> orderItemDtos = orderDto.getItems();
        BigDecimal orderAmount = BigDecimal.ZERO;
        if (SpringUtil.isNotEmpty(orderItemDtos)) {
            ItemVo itemVo = null;
            for (OrderItemDto item : orderItemDtos) {
                itemVo = new ItemVo();
                itemVo.setCommodityId(Long.parseLong(item.getProductId()));
                itemVo.setNumber(item.getProductNum());
                itemVo.setSourceId(String.valueOf(order.getId()));
                itemVo.setUnitPrice(item.getPrice());
                BigDecimal totalPrice = item.getPrice().multiply(item.getProductNum()).setScale(2, BigDecimal.ROUND_HALF_UP);
                itemVo.setTotalPrice(totalPrice);

                // 新增字段
                itemVo.setItemId(item.getItemId());
                itemVo.setDeliveryNumber(item.getProductNum());
                itemVo.setDeliveryUnitPrice(item.getPrice());
                itemVo.setDeliveryTotalPrice(totalPrice);

                itemVos.add(itemVo);
                orderAmount = orderAmount.add(totalPrice);
            }
        }

        // 清美一期配货走这里
        List<OrderListGift> orderListGifts = orderDto.getOrderListGifts();
        if (SpringUtil.isNotEmpty(orderListGifts)) {
            orderAmount = BigDecimal.ZERO;
            ItemVo itemVo = null;
            for (OrderListGift item : orderListGifts) {
                itemVo = new ItemVo();
                itemVo.setCommodityId(item.getCommodityId());
                itemVo.setNumber(item.getCommodityNum());
                itemVo.setSourceId(String.valueOf(order.getId()));
                itemVo.setUnitPrice(item.getCommodityPrice());
                BigDecimal totalPrice = item.getCommodityPrice().multiply(item.getCommodityNum()).setScale(2, BigDecimal.ROUND_HALF_UP);
                itemVo.setTotalPrice(totalPrice);

                itemVo.setItemId(item.getItemId());
                itemVo.setDeliveryNumber(item.getCommodityNum());
                itemVo.setDeliveryUnitPrice(item.getCommodityPrice());
                itemVo.setDeliveryTotalPrice(totalPrice);

                itemVos.add(itemVo);
                orderAmount = orderAmount.add(totalPrice);
            }
        }

        // 一期orderAmount和deliveryTotalAmount一样
        orderVo.setTotalAmount(orderAmount);
        orderVo.setDeliveryTotalAmount(orderAmount);

        orderVo.setItems(itemVos);
        orderVos.add(orderVo);
        String data = JSON.toJSONString(orderVos);
        String topic = applicationNameSwitch + KafkaConstant.SETTLE_ORDER_ALL_TOPIC;
        log.info("seller order : topic = {}, data = {}",topic, data);

        try {
            commonService.sendKafkaMessage(orderVos, MessageType.SETTLE_ORDER_SYNC, KafkaConstant.SETTLE_ORDER_ALL_TOPIC, operationType.getCode());
        } catch (Exception e) {
            log.error("【发送结算信息】异常：\n error=" + e + "\n dto=" + JSON.toJSONString(orderVos) + "\n message=" + data);
        }
    }

}
