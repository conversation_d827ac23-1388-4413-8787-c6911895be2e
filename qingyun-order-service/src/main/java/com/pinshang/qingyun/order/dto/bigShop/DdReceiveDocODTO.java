package com.pinshang.qingyun.order.dto.bigShop;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Data
public class DdReceiveDocODTO {

    @ApiModelProperty("单据id")
    private String docId;

    @ApiModelProperty("收货单号")
    private String docCode;

    @ApiModelProperty("关联订单数")
    private Integer orderCodeCount;

    @ApiModelProperty("关联订单list")
    private Set<String> orderCodeList;

    @ApiModelProperty("送货日期")
    private Date orderTime;

    @ApiModelProperty("档口id")
    private Long stallId;

    /** 门店id **/
    private Long shopId;
    private String shopName;

    @ApiModelProperty("档口名称")
    private String stallName;

    /** 配送批次 1=1配 + 补货  2=2配  8=新开店 */
    private Integer deliveryBatch;

    @ApiModelProperty("配送批次")
    private String  deliveryBatchName;

    /** 单据状态：0 待收货 1 已经收货 **/
    private Integer docStatus;

    /** 收货人 */
    private Long receiveUserId;

    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "receiveUserId")
    private String receiveUserName;

    @ApiModelProperty("收货时间")
    private Date receiveTime;

    @ApiModelProperty("导出时间")
    private String printTime;

    @ApiModelProperty("打印人")
    private String printUserName;

    @ApiModelProperty("商品明细")
    private  List<DdReceiveDocCommodityODTO> commodityList;


    public String getOrderTimeStr(){
        if(orderTime != null){
            return DateUtil.getDateFormate(orderTime, "yyyy-MM-dd");
        }
        return "";
    }

    public String getDeliveryBatchName(){
        if(DeliveryBatchTypeEnum.REPLENISHMENT.getCode().equals(deliveryBatch)
                || DeliveryBatchTypeEnum.ONE_BATCH.getCode().equals(deliveryBatch)){
            return "1配+补货";
        }else if(DeliveryBatchTypeEnum.TWO_BATCH.getCode().equals(deliveryBatch)){
            return DeliveryBatchTypeEnum.TWO_BATCH.getName();
        } else if (DeliveryBatchTypeEnum.NEW_SHOP_BATCH.getCode().equals(deliveryBatch)) {
            return DeliveryBatchTypeEnum.NEW_SHOP_BATCH.getName();
        }else {
            return DeliveryBatchTypeEnum.getNameByCode(deliveryBatch);
        }
    }

    public String getDocStatusName(){
        if(YesOrNoEnums.NO.getCode().equals(docStatus)){
            return "待收货";
        }else if(YesOrNoEnums.YES.getCode().equals(docStatus)){
            return "已收货";
        }else {
            return "";
        }
    }

    public String getReceiveTimeStr(){
        if(receiveTime != null){
            return DateUtil.getDateFormate(receiveTime, "yyyy-MM-dd HH:mm:ss");
        }
        return "";
    }
}
