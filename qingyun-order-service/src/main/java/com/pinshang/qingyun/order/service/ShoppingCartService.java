package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.GlobalSupplierNameConstant;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.ProductLimitEntry;
import com.pinshang.qingyun.order.mapper.entry.order.*;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.model.order.ShoppingCartItem;
import com.pinshang.qingyun.order.model.promotion.PromotionProduct;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.service.auto.AutoShopCommodityService;
import com.pinshang.qingyun.order.util.BeanUtil;
import com.pinshang.qingyun.order.util.OrderUtil;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.commodity.CommodityListRequestVO;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.commodity.HandCartRspVO;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.order.vo.order.ShoppingCartDetailVo.DeliveryBatchDay;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import com.pinshang.qingyun.shop.service.consignment.ConsignmentClient;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.dto.CommodityWarehouseODto;
import com.pinshang.qingyun.storage.dto.WarehouseODto;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import com.pinshang.qingyun.storage.service.WarehouseClient;
import com.pinshang.qingyun.supplier.dto.FullSupplierODTO;
import com.pinshang.qingyun.supplier.dto.QuerySupplierByIdsIDTO;
import com.pinshang.qingyun.supplier.dto.SupplierODTO;
import com.pinshang.qingyun.supplier.service.SupplierClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
@Slf4j
@Service
public class ShoppingCartService {
	public static SimpleDateFormat format =new SimpleDateFormat("yyyy-MM-dd");
	@Autowired
	ShoppingCartMapper shoppingCartMapper;
	@Autowired
	OrderService orderService;
	@Autowired
	ShoppingCartItemMapper itemMapper;
	@Autowired
	CommodityMapper commodityMapper;
	@Autowired
	OrderMapper orderMapper;
	@Autowired
	ShopMapper shopMapper;
	@Autowired
	private ConsigneeCommonService consigneeCommonService;
	@Lazy
	@Autowired
	private MdShopOrderSettingService mdShopOrderSettingService;

	@Autowired
	private ProductPriceModelClient productPriceModelClient;
	@Autowired
	private CommoditySupplierClient commoditySupplierClient;

	@Autowired
	private CommodityWarehouseClient commodityWarehouseClient;
	@Autowired
	private SupplierClient supplierClient;

	@Autowired
	private WarehouseClient warehouseClient;

	@Autowired
	private CommonService commonService;
	@Lazy
	@Autowired
	private ShoppingCartAdminService shoppingCartAdminService;
	@Autowired
	private AutoShopCommodityService autoShopCommodityService;

	@Lazy
	@Autowired
	private ProductService productService;
	@Autowired
	private ShoppingCartDeleteService shoppingCartDeleteService;
	@Autowired
	private ConsignmentClient consignmentClient;
	@Autowired
	private BStockService bStockService;
	@Autowired
	private ConsignmentSupplierService consignmentSupplierService;
	@Autowired
	private ShopService shopService;
	@Autowired
	private OrderReferenceService  orderReferenceService;


	@Transactional(rollbackFor = Exception.class)
	public Boolean removeShoppingCart(ShoppingCartVo vo){
		QYAssert.isTrue(null!=vo && (null !=vo.getShoppingCartId() || null !=vo.getShoppingCartItemId()), "参数错误,不能从购物车删除");
		ShoppingCart shoppingCart = null;
		if(null ==vo.getShoppingCartId() && null !=vo.getShoppingCartItemId()){
			ShoppingCartItem item =itemMapper.selectByPrimaryKey(vo.getShoppingCartItemId());
			QYAssert.isTrue(item != null,"购物车不存在,请刷新重试!");
			shoppingCart =shoppingCartMapper.selectByPrimaryKey(item.getShoppingCartId());
		}else if(null != vo.getShoppingCartId()){
			shoppingCart =shoppingCartMapper.selectByPrimaryKey(vo.getShoppingCartId());
		}
		QYAssert.isTrue(null!=shoppingCart, "未在购物车中查询到该数据,不能从购物车删除");

		//删除不校验时间
		
		if(null ==vo.getShoppingCartItemId()){
			//删除购物车主表
			shoppingCartMapper.deleteByPrimaryKey(vo.getShoppingCartId());
			deleteItemByCartId(vo.getShoppingCartId());
		}else{
			itemMapper.deleteByPrimaryKey(vo.getShoppingCartItemId());
			if(shoppingCart.getVarietyTotal().intValue() ==1){
				//如果只有一个商品,则删除购物车主表
				shoppingCartMapper.deleteByPrimaryKey(shoppingCart.getId());
			}else if(shoppingCart.getVarietyTotal().intValue() > 1){
				//如果不是一个商品,则修改商品数量;
				shoppingCart.setVarietyTotal(shoppingCart.getVarietyTotal().intValue() -1);
				shoppingCartMapper.updateByPrimaryKey(shoppingCart);
			}
		}
		return true;
	}
	
	/*
	 * 根据storeId删除购物车明细及购物车数据
	 * 订单复制、预订单复制
	 * 代理登陆: 清空所有购物车
	 * 非代理登陆:
	 *     1,当前是代销商，则只清空代销商的购物车
	 *     2,当前非代销商，则只清空门店的购物车
	 */
	@Transactional(rollbackFor = Exception.class)
	public void deleteShoppingCartAndItemByStoreId(Long storeId, Long stallId){
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
		Boolean isInternal = tokenInfo.getIsInternal();
		Long consignmentId = tokenInfo.getConsignmentId();

		if(null == storeId){
			return;
		}

		Example ex =new Example(ShoppingCart.class);
		Criteria c = ex.createCriteria();
		if(isInternal){
			c.andEqualTo("storeId", storeId);
		}else {
			c.andEqualTo("storeId", storeId)
					.andEqualTo("consignmentId", (consignmentId != null && consignmentId > 0) ? consignmentId : -1);
		}

		if(stallId != null && stallId > 0) {
			c.andEqualTo("stallId", stallId);
		}

		List<ShoppingCart> list =shoppingCartMapper.selectByExample(ex);
		if(null !=list && !list.isEmpty()){
			list.forEach(s ->{
				itemMapper.deleteItemByCartId(s.getId());
			});
			shoppingCartMapper.deleteByExample(ex);
		}
	}
	
	/*
	 * 通过购物车id删除item明细
	 */
	@Transactional(rollbackFor = Exception.class)
	public void deleteItemByCartId(Long shoppingCartId){
		itemMapper.deleteItemByCartId(shoppingCartId);
	}


	/*
	 * 检测商品状态+门店采购状态
	 */
	private SplitOrderListEntry checkCommodityState(ShoppingCartVo vo){
		QYAssert.isTrue(null != vo && null != vo.getStoreId(), "门店id不能为空");
		QYAssert.isTrue(null != vo && null != vo.getCommodityId(), "商品id不能为空");
		QYAssert.isTrue(null != vo && null != vo.getEnterpriseId(), "企业id不能为空");
		Boolean isXd = ThreadLocalUtils.getXd();
		if(!isXd){
			QYAssert.isTrue(null != vo.getQuantity() && vo.getQuantity().compareTo(BigDecimal.ZERO) > 0 , "商品数量至少要大于0!");
		}

		//检测商品状态+门店采购状态
		SplitOrderListEntry logi = shoppingCartMapper.queryCommoditySplitInfo(vo.getStoreId(), vo.getCommodityId(), vo.getEnterpriseId(),ThreadLocalUtils.getAdmin());
		QYAssert.isTrue(null != logi, "商品停用、不可售或者不可采,不能添加到购物车");
		return logi;
	}


	public void checkStoreOrderTimeByWareHouse(Long  commodityId){
		List<Long> commoditIdList = new ArrayList<>();
		commoditIdList.add(commodityId);
		Map<Long, WarehouseODto> wareMap = warehouseClient.queryWarehouseListByCommodityIds(commoditIdList);
		QYAssert.isTrue(wareMap !=  null && wareMap.size() > 0, "未设置默认仓库");
		WarehouseODto warehouseODto = wareMap.get(commodityId);
		if (StringUtils.isNotBlank(warehouseODto.getWorkBeginTime()) && StringUtils.isNotBlank(warehouseODto.getWorkEndTime())){
			QYAssert.isTrue(DateTimeUtil.compareNewDate(warehouseODto.getWorkBeginTime(), warehouseODto.getWorkEndTime()), "超出订货时间,无法操作!");
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean addShoppingCart(ShoppingCartVo vo){
		// 检查客户下单时间
		if(!vo.getInternal()){
			OrderRequestVo orderRequestVo = new OrderRequestVo();
			orderRequestVo.setStoreId(vo.getStoreId() + "");
			orderService.checkStoreOrderTime(orderRequestVo);
		}

		//检查门店订货通用设置（物流模式、供应商、时间、仓库、时间）
		List<MdShopOrderSettingEntry> settingList = orderService.checkShopOrderSetting(vo.getStoreId(), Arrays.asList(vo.getCommodityId() + ""), vo.getInternal());
		// 因为加购物车其实就1个商品，所以用settingList.get(0),后面就不用再次查询通用设置表了
		MdShopOrderSettingEntry mdShopOrderSetting = settingList.get(0);

		// 鲜食加盟的不允许加购直送商品
		Boolean isJmAndDirect = (shopService.isJmShop(vo.getStoreId()) && IogisticsModelEnums.DIRECT_SENDING.getCode() == mdShopOrderSetting.getLogisticsModel());
		QYAssert.isTrue(!isJmAndDirect, "直送商品不允许订货 " + mdShopOrderSetting.getCommodityCode());

		//检测商品状态+门店采购状态
		SplitOrderListEntry entry = checkCommodityState(vo);

		// 组装 OrderDto 对象
		OrderDto orderDto = new OrderDto();
		orderDto.setLogisticsModel(mdShopOrderSetting.getLogisticsModel());
		orderDto.setStoreId(vo.getStoreId());
		Boolean ifAdmin = ThreadLocalUtils.getAdmin();
		if(ifAdmin){
			orderDto.setOrderTime(vo.getOrderTime());
		}else {
			orderDto.setOrderTime(DateTimeUtil.defaultDeliveryDate());
		}
		orderDto.setItems(new ArrayList<OrderItemDto>());
		//orderRequestVo.getItemsList().forEach(i->{
			orderDto.addItem(new OrderItemDto(vo.getCommodityId().toString(), BigDecimal.ZERO, vo.getQuantity(), "", ProductTypeEnums.PRODUCT.getCode()));
		//});

		//验证箱规
		orderService.checkStoreFrozenProduct(orderDto, false);


		//商品库存限量;
		//orderService.checkProductLimit(orderDto);

		insertShoppingCart(vo, entry, mdShopOrderSetting);
		return true;
	}


	@Transactional
	public Boolean batchAddShoppingCart(ShoppingCartBatchVo vo) {
		for (ShoppingCartBatchListVo shoppingCartBatchListVo : vo.getList()) {
			ShoppingCartVo cartVo = new ShoppingCartVo();
			cartVo.setCreateId(vo.getCreateId());
			cartVo.setStoreId(vo.getStoreId());
			cartVo.setEnterpriseId(vo.getEnterpriseId());
			cartVo.setInternal(vo.getInternal());
			cartVo.setCommodityId(shoppingCartBatchListVo.getCommodityId());
			cartVo.setQuantity(shoppingCartBatchListVo.getQuantity());
			cartVo.setOrderTime(vo.getOrderTime());
			cartVo.setDeliveryBatch(vo.getDeliveryBatch());
			cartVo.setStallId(vo.getStallId());
			//调用加入购物车
			if(!addShoppingCart(cartVo)) {return false;}
		}
		return true;
	}

	@Transactional(rollbackFor = Exception.class)
	public void insertShoppingCart(ShoppingCartVo vo, SplitOrderListEntry entry, MdShopOrderSettingEntry mdShopOrderSettingEntry){
		Boolean ifAdmin = ThreadLocalUtils.getAdmin();
		Long consignmentSupplierId = null;
		List<ConsignmentSupplierInfoODTO> consignmentSupplierList = consignmentSupplierService.queryConsignmentSupplierList(Collections.singletonList(vo.getStoreId()), Collections.singletonList(vo.getCommodityId()));
		if(!CollectionUtils.isEmpty(consignmentSupplierList)){
			if(ifAdmin){
				consignmentSupplierId = consignmentSupplierList.get(0).getSupplierId();
			}else {
				Commodity commodity = commodityMapper.selectByPrimaryKey(vo.getCommodityId());
				log.warn("不能加购代销商品, storeId {}  commodityId {}", vo.getStoreId(), vo.getCommodityId());
				QYAssert.isFalse("不能加购代销商品 " + commodity.getCommodityCode() + " - " + commodity.getCommodityName());
			}
		}
		// 查询商品是否自动订货
		List<Long> autoCommodityIdList = autoShopCommodityService.getAutoCommodityList(vo.getStoreId());
		Boolean autoCommodity = !CollectionUtils.isEmpty(autoCommodityIdList) && autoCommodityIdList.contains(entry.getCommodityId());
		if(!ifAdmin && autoCommodity
				&& mdShopOrderSettingEntry.getLogisticsModel() == IogisticsModelEnums.DIRECT_SENDING.getCode()){
			QYAssert.isTrue(false,"直送商品" + mdShopOrderSettingEntry.getCommodityCode() + "需从自动订货池中剔除掉");
		}
		int adminStatus = ThreadLocalUtils.getAdminStatus();
		// 根据代销商id拆购物车，直送商品保持原样
		//Map<Long,Long> consigneeCommMap = consigneeCommonService.getConsigneeCommMap(null,vo.getStoreId());
		//Long consignmentId = consigneeCommMap.get(entry.getCommodityId()) != null ? consigneeCommMap.get(entry.getCommodityId()) : -1L;
		Long consignmentId = -1L;
		Boolean isDirect = mdShopOrderSettingEntry.getLogisticsModel() == IogisticsModelEnums.DIRECT_SENDING.getCode();

		// B端库存依据拆单,获取库存依据
		Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
		orderQuantityMap.put(Long.valueOf(vo.getCommodityId()), BigDecimal.ZERO);
		Map<Long, CommodityInventoryODTO> toBStockMap = bStockService.getbStockMap(DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"), orderQuantityMap);
		Integer stockType = StockTypeEnum.UN_LIMIT.getCode();
		if(toBStockMap != null && toBStockMap.containsKey(entry.getCommodityId())){
			stockType = toBStockMap.get(entry.getCommodityId()).getStockType();
		}

		// 档口id
		Long stallId = (vo.getStallId() == null ? -1L : vo.getStallId());

		// 首选查询该店铺的购物车是否为空
		Example ex = getShoppingCartExample(vo, ifAdmin, autoCommodity, adminStatus, consignmentId, isDirect, stockType, consignmentSupplierId, stallId);
		List<ShoppingCart> carts =shoppingCartMapper.selectByExample(ex);

		int count = (null != carts && !carts.isEmpty() ? carts.size() : 0);

		// 根据 storeId + suplierId + commodityId 查询订单设置
		//MdShopOrderSettingEntry mdShopOrderSettingEntry = this.findMdShopOrderSetting(vo.getStoreId(), entry.getCommodityId().toString());
        String deleveryTimeRange = mdShopOrderSettingEntry.getDeleveryTimeRange();
		Integer logisticsModel = mdShopOrderSettingEntry.getLogisticsModel();
		entry.setDeleveryTimeRange(deleveryTimeRange);
		// 购物车的物流模式来自于订单配置中的物流模式
		entry.setLogisticsModel(logisticsModel);
		entry.setWarehouseId(Long.valueOf(mdShopOrderSettingEntry.getWarehouseId()));
		entry.setSupplierId(Long.valueOf(mdShopOrderSettingEntry.getSupplierId()));

		if(count == 0){  // 购物车为空,直接拆单即可;
			newAddShoppingCart(vo, entry, autoCommodity, consignmentId, stockType, consignmentSupplierId, stallId);
		} else { // 购物车不为空，则购物车已拆过单了
			// 根据 【店铺+物流模式+供应商+仓库】+ 获取送货日期范围 查询购物车记录是否存在
			ShoppingCart shoppingCart = this.findShoppingCart(vo.getStoreId(), logisticsModel, entry.getSupplierId(), entry.getWarehouseId(), vo.getEnterpriseId(),
										deleveryTimeRange,vo.getOrderTime(),vo.getDeliveryBatch(),vo.getCreateId(), autoCommodity, consignmentId, stockType, consignmentSupplierId, stallId);
			if(null == shoppingCart){
				// 如果 【店铺+物流模式+仓库】+ 获取送货日期范围, 记录不存在，相当于该类型的订单不存在，相当于购物车为空的情况，直接加入购物车即可
                newAddShoppingCart(vo, entry, autoCommodity, consignmentId, stockType, consignmentSupplierId, stallId);
			}else{
				//【店铺+物流模式+供应商+仓库】+ 获取送货日期范围, 记录存在，查询购物车项中是否存在该商品个购物车item
				Example itemEx =new Example(ShoppingCartItem.class);
				itemEx.createCriteria().andEqualTo("shoppingCartId", shoppingCart.getId()).andEqualTo("commodityId",entry.getCommodityId());
				List<ShoppingCartItem> items =itemMapper.selectByExample(itemEx);

				Date date = new Date();
				if(null !=items && !items.isEmpty()){
					// 【店铺+物流模式+供应商+仓库 +送货日期范围 + 商品id】 存在，属于该商品又加入了购物车一次，同一个商品买了多次，这种情况只需要更改购物车item中该商品数量即可
					ShoppingCartItem item =items.get(0);
					item.setCommodityNum(vo.getQuantity());
					item.setAdviseCommodityNum(item.getCommodityNum());
					item.setCreateId(vo.getCreateId());
					item.setCreateTime(date);
					itemMapper.updateByPrimaryKey(item);
				}else{
					// 【店铺+物流模式+供应商+仓库+送货日期范围+商品id】不存在，但是该商品是第一次购买，直接作为购物车item插入进去
					ShoppingCartItem item =new ShoppingCartItem();
					item.setCommodityId(vo.getCommodityId());
					item.setCommodityNum(vo.getQuantity());
					item.setAdviseCommodityNum(item.getCommodityNum());
					item.setCreateTime(date);
					item.setShoppingCartId(shoppingCart.getId());
					item.setCreateId(vo.getCreateId());
					itemMapper.insert(item);

					// 并更改购物车的商品品类数量
					shoppingCart.setVarietyTotal((shoppingCart.getVarietyTotal()==null?0:shoppingCart.getVarietyTotal())+1);
					shoppingCartMapper.updateByPrimaryKey(shoppingCart);
				}
			}
		}
	}

	@NotNull
	private Example getShoppingCartExample(ShoppingCartVo vo, Boolean ifAdmin, Boolean autoCommodity, int adminStatus,
										   Long consignmentId, Boolean isDirect, Integer stockType, Long consignmentSupplierId, Long stallId) {
		Example ex =new Example(ShoppingCart.class);
		Criteria c = ex.createCriteria();
		if(ifAdmin){
			// 如果是代理快速订货提交购物车，则根据createId 拆单
			c.andEqualTo("enterpriseId", vo.getEnterpriseId())
				.andEqualTo("storeId", vo.getStoreId())
				.andEqualTo("adminStatus", adminStatus)
				.andEqualTo("createId",vo.getCreateId());
		}else {
			c.andEqualTo("enterpriseId", vo.getEnterpriseId())
					.andEqualTo("storeId", vo.getStoreId())
					.andEqualTo("adminStatus", adminStatus)
					.andEqualTo("autoStatus", autoCommodity ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
		}

		if(consignmentSupplierId != null){
			c.andEqualTo("consignmentId", consignmentSupplierId);
		}else {
			// 直送按照以前拆单
			c.andEqualTo("consignmentId",isDirect ? -1 : consignmentId);
		}

		c.andEqualTo("stockType", stockType);
		c.andEqualTo("stallId", stallId);
		return ex;
	}


	@Transactional(rollbackFor = Exception.class)
	public void newAddShoppingCart(ShoppingCartVo vo, SplitOrderListEntry entry, Boolean autoCommodity, Long consignmentId,
								   Integer stockType, Long consignmentSupplierId, Long stallId){
		int adminStatus = ThreadLocalUtils.getAdminStatus();
		ShoppingCart shoppingCart  =new ShoppingCart();
		shoppingCart.setCreateId(vo.getCreateId());
		Date date =new Date();
		shoppingCart.setCreateTime(date);
		shoppingCart.setEnterpriseId(vo.getEnterpriseId());
		shoppingCart.setLogisticsModel(entry.getLogisticsModel());
		shoppingCart.setStoreId(vo.getStoreId());
		shoppingCart.setSupplierId(entry.getSupplierId());
		shoppingCart.setDeleveryTimeRange(entry.getDeleveryTimeRange());
		shoppingCart.setVarietyTotal(1);
		shoppingCart.setWarehouseId(entry.getWarehouseId());
		shoppingCart.setAdminStatus(adminStatus);
		Boolean ifAdmin = ThreadLocalUtils.getAdmin();
		if(ifAdmin){
			shoppingCart.setOrderTime(vo.getOrderTime());
			shoppingCart.setDeliveryBatch(vo.getDeliveryBatch());
		}
		shoppingCart.setAutoStatus(autoCommodity ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode()); // 设置是否自动订货
		shoppingCart.setConsignmentId(consignmentId != null ? consignmentId : -1);
		shoppingCart.setStockType(stockType);

		if(consignmentSupplierId != null){
			shoppingCart.setConsignmentId(consignmentSupplierId);
		}
		// 档口id
		shoppingCart.setStallId(stallId);
		shoppingCartMapper.insert(shoppingCart);
		
		ShoppingCartItem item =new ShoppingCartItem();
		item.setCommodityId(vo.getCommodityId());
		item.setCommodityNum(vo.getQuantity());
		item.setAdviseCommodityNum(item.getCommodityNum());
		item.setCreateTime(date);
		item.setShoppingCartId(shoppingCart.getId());
		item.setCreateId(vo.getCreateId());
		itemMapper.insert(item);

	}
	
	public ShoppingCart findShoppingCart(Long storeId, Integer logisticsModel, Long supplierId, Long warehouseId, Long enterpriseId, String deleveryTimeRange, String orderTime, String deliveryBatch, Long createId,
										 Boolean autoCommodity, Long consignmentId, Integer stockType, Long consignmentSupplierId, Long stallId){
		Boolean ifAdmin = ThreadLocalUtils.getAdmin();
		QYAssert.isTrue(null  !=storeId , "客户id不能为空");
		QYAssert.isTrue(null  !=logisticsModel , "商品物流模式不能为空");
		QYAssert.isTrue(null  !=enterpriseId , "企业id不能为空");
		QYAssert.isTrue(null  !=deleveryTimeRange , "配送日期范围不能为空");
		if(logisticsModel.intValue() ==IogisticsModelEnums.DISPATCHING.getCode()){
			QYAssert.isTrue(null  !=warehouseId , "物流模式是:"+IogisticsModelEnums.getName(logisticsModel.intValue())+" 首选仓库 不能为空");
		}else if(logisticsModel.intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode()){
			QYAssert.isTrue(null  !=supplierId , "物流模式是:"+IogisticsModelEnums.getName(logisticsModel.intValue())+"  首选供应商不能为空");
		}
		if(logisticsModel.intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
			QYAssert.isTrue(null  !=warehouseId ,  "物流模式是:"+IogisticsModelEnums.getName(logisticsModel.intValue())+" 首选仓库 不能为空");
			QYAssert.isTrue(null  !=supplierId , "物流模式是:"+IogisticsModelEnums.getName(logisticsModel.intValue())+"  首选供应商不能为空");
		}
		Example ex =new Example(ShoppingCart.class);
		Criteria c =ex.createCriteria();
		c.andEqualTo("storeId", storeId)
				.andEqualTo("enterpriseId", enterpriseId)
				.andEqualTo("logisticsModel", logisticsModel);

		if(logisticsModel.intValue() ==IogisticsModelEnums.DISPATCHING.getCode()){
			// 配送
			c.andEqualTo("warehouseId", warehouseId);
			if(ifAdmin){
				c.andEqualTo("orderTime", orderTime);
				c.andEqualTo("deliveryBatch", deliveryBatch);
				c.andEqualTo("createId", createId);
			}else {
				c.andEqualTo("deleveryTimeRange", deleveryTimeRange.trim());
				c.andEqualTo("autoStatus",autoCommodity ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
			}

		}else if(logisticsModel.intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode()){
			c.andEqualTo("supplierId", supplierId);
			if(ifAdmin){
				c.andEqualTo("orderTime", orderTime);
				c.andEqualTo("deliveryBatch", deliveryBatch);
				c.andEqualTo("createId", createId);
			}else {
				c.andEqualTo("autoStatus", autoCommodity ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
			}
		}

		// 直通
		if(logisticsModel.intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
			c.andEqualTo("warehouseId", warehouseId);
			c.andEqualTo("supplierId", supplierId);
			if(ifAdmin){
				c.andEqualTo("orderTime", orderTime);
				c.andEqualTo("deliveryBatch", deliveryBatch);
				c.andEqualTo("createId", createId);
			}else {
				c.andEqualTo("deleveryTimeRange", deleveryTimeRange.trim());
				c.andEqualTo("autoStatus", autoCommodity ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
			}
		}

		if(consignmentSupplierId != null){
			c.andEqualTo("consignmentId", consignmentSupplierId);
		}else {
			c.andEqualTo("consignmentId", logisticsModel.intValue() == IogisticsModelEnums.DIRECT_SENDING.getCode() ? -1 : consignmentId);
		}

		int adminStatus = ThreadLocalUtils.getAdminStatus();
		c.andEqualTo("adminStatus", adminStatus);
		c.andEqualTo("stockType", stockType);
		c.andEqualTo("stallId", stallId);
		List<ShoppingCart> list =shoppingCartMapper.selectByExample(ex);
		return list!=null && !list.isEmpty()? list.get(0):null;
	}
	//获取促销信息
	public Map<String, CommodityResultEntry> getCommodityCodeAndPromotionPriceMap(String storeId, String orderTime){
		Map<String, CommodityResultEntry> commodityCodeAndPromotionPriceMap=new HashMap<>();
		CommodityListRequestVO vo=new CommodityListRequestVO();
		vo.setStoreId(storeId);
		String orderTm = StringUtils.isNotBlank(orderTime) ? orderTime : DateTimeUtil.defaultDeliveryDate();
		vo.setOrderTime(orderTm);
		List<CommodityResultEntry> promotionCommodityCodeList = commonService.findPromotionCommodityCodeList(vo);
		commodityCodeAndPromotionPriceMap = promotionCommodityCodeList.stream()
				.collect(Collectors.toMap(CommodityResultEntry::getCommodityCode, Function.identity()));
		return commodityCodeAndPromotionPriceMap;
	}

	public ShoppingCartDetailVo shoppingCartDetailAdmin(boolean isInternal,Long shoppingCartId,Boolean handle,Long userId){
		ShoppingCart shoppingCart = shoppingCartMapper.selectByPrimaryKey(shoppingCartId);
		QYAssert.isTrue(shoppingCart != null, "购物车数据异常,请刷新页面重试");
		return shoppingCartDetail(shoppingCart.getStoreId(), isInternal,shoppingCartId,handle,userId, null);
	}

	public ShoppingCartDetailVo shoppingCartDetail(Long storeId, boolean isInternal, Long shoppingCartId, Boolean handle, Long userId, String orderTime){
		// 是否大店
		Boolean isBigShop = false;
		Example shopEx = new Example(Shop.class);
		shopEx.createCriteria().andEqualTo("storeId", storeId);
		List<Shop> shopList = shopMapper.selectByExample(shopEx);
		// 是否是鲜道前置仓
		Boolean isXd = shopList.get(0).getShopType().equals(ShopTypeEnums.XD.getCode());

		Map<String, CommodityResultEntry> commodityCodeAndPromotionPriceMap = new HashMap<>();
		if(handle && !isXd){
			commodityCodeAndPromotionPriceMap = getCommodityCodeAndPromotionPriceMap(storeId+"",orderTime);
		}
		ShoppingCartDetailVo vo = new ShoppingCartDetailVo();

		List<ShoppingCartEntry> entry;
		if(ThreadLocalUtils.getAdmin()){
			entry = shoppingCartMapper.shoppingCartDetailAdmin(storeId,shoppingCartId,isXd,isInternal,userId,StallUtils.isStallSubcontractor(shopList.get(0).getManagementMode()));
		}else{
			TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
			isBigShop = StallUtils.isStallSubcontractor(shopList.get(0).getManagementMode());
			List<Long> stallIdList = new ArrayList<>();
			if(isBigShop){
				stallIdList = consignmentSupplierService.selectUserStallIdList(tokenInfo.getShopId());
				if(CollectionUtils.isEmpty(stallIdList)){
					return vo;
				}
			}
			entry = shoppingCartMapper.shoppingCartDetailConsignment(storeId,shoppingCartId,isXd,isInternal,userId,tokenInfo.getConsignmentId(), stallIdList);
		}
		if (Objects.nonNull(shoppingCartId)) {
			// 有购物车ID查商品详情， 购物车列表 无购物车ID，不查商品详情
			commonService.setShopCartItemInfo(entry);

			//  渲染近15天日均销量、当日销售数量、近15天毛利率
			orderReferenceService.renderShoppingCartMetrics(shopList.get(0).getId(), entry);
		}
		//获取时间
		vo.setDeliveryBatchDays(getDeliveryBatchDayStr());
		List<DeliveryBatchEntry> allEntrys =shoppingCartMapper.findDeliveryBatchByCode("");
		vo.setEntrys(allEntrys);

		// 获取批次map
		Map<String, DeliveryBatchEntry> deliveryBatchMap = allEntrys.stream().collect(Collectors.toMap(DeliveryBatchEntry::getOptionName, Function.identity()));

		if(null != entry && !entry.isEmpty()){

			// 设置供应商和仓库信息
            setSupplierAndWarehouseInfo(entry);
			List<Long> ids =new ArrayList<Long>();
			entry.forEach(shoppingCartEntry ->{
				List<ShoppingCartItemEntry> items =shoppingCartEntry.getItems();
				if(null !=items && !items.isEmpty()){
					items.forEach(shoppingCartItemEntry ->{
						ids.add(shoppingCartItemEntry.getCommodityId());
					});
				}
			});

			String orderTm = StringUtils.isNotBlank(orderTime) ? orderTime : DateTimeUtil.defaultDeliveryDate();

			Map<String, ProductPriceEntry> priceHashMap = new HashMap<>();
			//查找所有商品价格;
			priceHashMap = getCommodityPrices(ids, storeId, orderTm);
			if(!isXd){
				if(SpringUtil.isEmpty(priceHashMap)){
					// 删除加事务
					shoppingCartDeleteService.deleteShoppingCartAndItems(entry);
					return new ShoppingCartDetailVo();
				}
			}

			Map<String, ProductPriceEntry> limitHashmap = new HashMap<>();
			if(!isXd){
				//查找所有商品的限量
				limitHashmap =getCommodityLimit(ids, storeId, orderTm);
			}

			// 查询门店商品在途数量
			String orderTimeStr = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd");
			List<ShopOrderedQuantityODTO> orderedQuantityList = orderMapper.selectShopOrderQuantity(shopList.get(0).getId(),ids,orderTimeStr);
			Map<Long, ShopOrderedQuantityODTO> orderedQuantityMap = orderedQuantityList.stream().collect(Collectors.toMap(ShopOrderedQuantityODTO::getCommodityId, Function.identity()));

			ShoppingCartDetailVo.TableData[] sites =new ShoppingCartDetailVo.TableData[entry.size()];
		    vo.setSites(sites);
		    int i=0;

			SupplierODTO globalEntry = supplierClient.getSupplierByCode(GlobalSupplierNameConstant.XS_SUPPLIER_CODE);
			Boolean isXsShop= true;

			// 返回档口名称信息
			List<Long> stallIdList = entry.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
					.stream().map(item -> item.getStallId()).collect(Collectors.toList());
			Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIdList);

			for(ShoppingCartEntry shoppingCartEntry: entry){
				// 同一个购物车里面仓库，供应商，物流模式都是一样的.所以随便取第一个商品
				List<String> commodityIds = new ArrayList<>();
				if(CollectionUtils.isEmpty(shoppingCartEntry.getItems())){
					shoppingCartDeleteService.deleteShoppingCart(Long.valueOf(shoppingCartEntry.getId()));
					QYAssert.isTrue(false,"请刷新购物车");
				}
				commodityIds.add(shoppingCartEntry.getItems().get(0).getCommodityId()+"");
				List<MdShopOrderSettingEntry> list = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(storeId,commodityIds);
				MdShopOrderSettingEntry mdShopOrderSettingEntry = new MdShopOrderSettingEntry();
				if(!CollectionUtils.isEmpty(list)){
					mdShopOrderSettingEntry = list.get(0);
				}
		    	ShoppingCartDetailVo.TableData table =new ShoppingCartDetailVo.TableData();
			    sites[i++] =table;
			    ShoppingCartDetailVo.TitleRow[] titleRows =new ShoppingCartDetailVo.TitleRow[1];
			   table.setTitle(titleRows);
			   ShoppingCartDetailVo.TitleRow title= new ShoppingCartDetailVo.TitleRow();
			   BeanUtils.copyProperties(shoppingCartEntry,title);
			   title.setAutoCommodity(shoppingCartEntry.getAutoCommodity()); // 设置是否自动订货
			   title.setLogisticsModelName(IogisticsModelEnums.getName(shoppingCartEntry.getLogisticsModel()));
			   title.setShoppingCartId(shoppingCartEntry.getId());
			   title.setDisable(false);
			   title.setShowDeliveryBatch(false);
			   title.setOrderTime(orderTm);
			   String xdOrderTime = StringUtils.isNotBlank(orderTime) ? orderTime : OrderUtil.getOrderTime(shoppingCartEntry.getDeleveryTimeRange());
			   // 前端传了 就用前端的
			   title.setXdOrderTime(xdOrderTime);
			   title.setXdDeliveryBatch(getXdDeliveryBatch(allEntrys));
				// orderTime,deliveryBatch 不为空表示代理购物车
				if(StringUtils.isNotBlank(shoppingCartEntry.getOrderTime()) && StringUtils.isNotBlank(shoppingCartEntry.getDeliveryBatch())){
					title.setXdOrderTime(shoppingCartEntry.getOrderTime());
					DeliveryBatchEntry deliveryBatchEntry = deliveryBatchMap.get(shoppingCartEntry.getDeliveryBatch());
					if(deliveryBatchEntry != null){
						title.setXdDeliveryBatch(deliveryBatchEntry.getOptionName() + "_" + deliveryBatchEntry.getMemo());
					}
					title.setDeliveryBatch(null);
				}

				List<DeliveryBatchEntry> batchEntryList = this.findDeliveryBatch(storeId, title.getOrderTime());
				if(!CollectionUtils.isEmpty(batchEntryList)){
					title.setAdminDeliveryBatch(batchEntryList.get(0).getOptionCode());
				}
			   if((shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DISPATCHING.getCode())){
				   title.setCompanyName(globalEntry.getSupplierName());
				   title.setSupplyTime((mdShopOrderSettingEntry.getDefaultWarehouseBeginTime() ==null?"":mdShopOrderSettingEntry.getDefaultWarehouseBeginTime()) +"-"+(mdShopOrderSettingEntry.getDefaultWarehouseEndTime()==null?"":mdShopOrderSettingEntry.getDefaultWarehouseEndTime()));
				   if(isXsShop){title.setShowDeliveryBatch(true);}
				   table.setXdCloseTime(mdShopOrderSettingEntry.getDefaultWarehouseEndTime());
			   }else if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode()) {
				   title.setShowDeliveryBatch(false);
				   title.setSupplyTime((mdShopOrderSettingEntry.getDefaultSupplierBeginTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierBeginTime()) + "-" + (mdShopOrderSettingEntry.getDefaultSupplierEndTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierEndTime()));
				   table.setXdCloseTime(mdShopOrderSettingEntry.getDefaultSupplierEndTime());
			   }else if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
				   if(isXsShop){ title.setShowDeliveryBatch(true);}
				   title.setSupplyTime((mdShopOrderSettingEntry.getDefaultSupplierBeginTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierBeginTime()) + "-" + (mdShopOrderSettingEntry.getDefaultSupplierEndTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierEndTime()));
				   table.setXdCloseTime(mdShopOrderSettingEntry.getDefaultSupplierEndTime());
			   }
				if(!isInternal){
					   //检查客户下单时间
					 if(!DateTimeUtil.compareNewDate(shoppingCartEntry.getBeginTime(), shoppingCartEntry.getEndTime())){
						   title.setDisable(true);
					 }

					//直送，直通 检查供应商时间
					if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode()
						   || shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
					   if(StringUtils.isBlank(mdShopOrderSettingEntry.getDefaultSupplierBeginTime()) || StringUtils.isBlank(mdShopOrderSettingEntry.getDefaultSupplierEndTime())){
						   title.setDisable(true);
					   }
					   if(!DateTimeUtil.compareNewDate(mdShopOrderSettingEntry.getDefaultSupplierBeginTime(), mdShopOrderSettingEntry.getDefaultSupplierEndTime())){
						   title.setDisable(true);
					   }
				   }
				  //配送模式 时间校验 仓库时间
				  if(shoppingCartEntry.getLogisticsModel().intValue() == IogisticsModelEnums.DISPATCHING.getCode()){
					  if(!DateTimeUtil.compareNewDate(mdShopOrderSettingEntry.getDefaultWarehouseBeginTime(), mdShopOrderSettingEntry.getDefaultWarehouseEndTime())){
						  title.setDisable(true);
					  }
				  }
			  }
			   if(shoppingCartEntry.getLogisticsModel().intValue() !=IogisticsModelEnums.DIRECT_SENDING.getCode()){
				   title.setWarehouseName(shoppingCartEntry.getWarehouseName());
			   }else{ // 直送的仓库名字为空
				   title.setWarehouseName("");
			   }
			   title.setStallName(stallIdAndNameMap.get(shoppingCartEntry.getStallId()));

			   titleRows[0] =title;
			   table.setTitle(titleRows);

			   List<ShoppingCartItemEntry> items =shoppingCartEntry.getItems();
			   BigDecimal totalPrice =BigDecimal.ZERO;
			   BigDecimal totalVarietyTotal =BigDecimal.ZERO;
			   BigDecimal totalQuantity =BigDecimal.ZERO;
			   if(null != items && !items.isEmpty()){
				   List<Long> commodityIdList = items.stream().map(ShoppingCartItemEntry::getCommodityId).collect(Collectors.toList());
				   // 获取鲜到库存,只有购物车详情再去查看库存
				   Map<Long, ShopCommodityInfoODTO> xdStockMap = new HashMap<>();
				   Map<String, ShopCommodityInfoODTO> bigShopStockMap = new HashMap<>();
				   if(shoppingCartId != null && shoppingCartId > 0){
					   if(shoppingCartEntry.getStallId() != null && shoppingCartEntry.getStallId() > 0){
						   bigShopStockMap = consignmentSupplierService.queryBigShopCommodityStock(shopList.get(0).getId(), Collections.singletonList(shoppingCartEntry.getStallId()), commodityIdList);
					   }else {
						   xdStockMap = productService.getXdShopCommodityStock(shopList.get(0).getId(),commodityIdList);
					   }
				   }

				   //if (Objects.nonNull(shoppingCartId)) {
					   // 查购物车详情才加载商品信息
					   ShoppingCartDetailVo.CommodityRow[] crs =new ShoppingCartDetailVo.CommodityRow[items.size()];
					   table.setCommoditys(crs);
					   int k=0;
					   for(ShoppingCartItemEntry shoppingCartItemEntry:items ){
						   totalVarietyTotal =totalVarietyTotal.add(BigDecimal.valueOf(1L));
						   totalQuantity =totalQuantity.add(shoppingCartItemEntry.getQuantity());
						   ShoppingCartDetailVo.CommodityRow cr =new ShoppingCartDetailVo.CommodityRow();
						   BeanUtils.copyProperties(shoppingCartItemEntry,cr);
						   cr.setShoppingCartItemId(shoppingCartItemEntry.getShoppingCartItemId().toString());
						   cr.setCommodityId(shoppingCartItemEntry.getCommodityId().toString());
						   BigDecimal  price = priceHashMap.get(shoppingCartItemEntry.getCommodityId()+"") ==null ? BigDecimal.ZERO : priceHashMap.get(shoppingCartItemEntry.getCommodityId()+"").getPrice();
						   //取实时价格
						   cr.setPrice(price);
						   //取限量
						   if(null !=limitHashmap && limitHashmap.containsKey(shoppingCartItemEntry.getCommodityId().toString())){
							   BigDecimal limit =limitHashmap.get(shoppingCartItemEntry.getCommodityId().toString()).getLimitNum();
							   cr.setCommodityNumberLimit(null ==limit ?BigDecimal.ZERO:limit);
						   }else{
							   cr.setCommodityNumberLimit(BigDecimal.ZERO);
						   }

						   //设置是否促销
						   cr.setPromotionProduct(commodityCodeAndPromotionPriceMap.get(shoppingCartItemEntry.getCommodityCode())!=null?true:false);
						   //设置是否新品
						   if(null !=shoppingCartItemEntry.getNewProductFlag()){
							   cr.setNewProduct(shoppingCartItemEntry.getNewProductFlag().endsWith("01")?true:false);
						   }
						   cr.setQuantity(shoppingCartItemEntry.getQuantity());
						   cr.setFrozen(shoppingCartItemEntry.getFrozen());
						   cr.setPackedType(shoppingCartItemEntry.getPackedType());
						   if(null ==shoppingCartItemEntry.getPrice()){
							   shoppingCartItemEntry.setPrice(BigDecimal.ZERO);
						   }
						   cr.setTotalPrice(cr.getPrice().multiply(shoppingCartItemEntry.getQuantity()).setScale(2,BigDecimal.ROUND_HALF_UP));
						   totalPrice =totalPrice.add(cr.getTotalPrice());
						   cr.setCommodityPackageSpec(shoppingCartItemEntry.getCommodityPackageSpec());
						   if (Objects.nonNull(shoppingCartItemEntry.getCommodityPackageSpec())){
							   cr.setShares(shoppingCartItemEntry.getQuantity().divide(shoppingCartItemEntry.getCommodityPackageSpec(),0, RoundingMode.UP).longValue());
						   }
						   cr.setIsWeight(shoppingCartItemEntry.getIsWeight());
						   cr.setCommodityPackageKind(shoppingCartItemEntry.getCommodityPackageKind());

						   ShopOrderedQuantityODTO shopOrderedQuantityODTO = orderedQuantityMap.get(Long.valueOf(cr.getCommodityId()));
						   if(shopOrderedQuantityODTO != null){
							   cr.setOrderedQuantity(shopOrderedQuantityODTO.getQuantity());
						   }

						   // 档口不为空，查询档口下面库存
						   if(shoppingCartEntry.getStallId() != null && shoppingCartEntry.getStallId() > 0){
							   String stallCommKey = shoppingCartEntry.getStallId() + "_" + shoppingCartItemEntry.getCommodityId();
							   ShopCommodityInfoODTO shopCommodityInfoODTO = bigShopStockMap.get(stallCommKey);
							   if(shopCommodityInfoODTO != null) {
								   cr.setStockQuantity(shopCommodityInfoODTO.getStockQuantity());
								   cr.setStockNumber(shopCommodityInfoODTO.getStockNumber());
							   }

						   }else {
							   ShopCommodityInfoODTO shopCommodityInfoODTO = xdStockMap.get(Long.valueOf(cr.getCommodityId()));
							   if(shopCommodityInfoODTO != null){
								   cr.setStockQuantity(shopCommodityInfoODTO.getStockQuantity());
								   cr.setStockNumber(shopCommodityInfoODTO.getStockNumber());
							   }
						   }
						   crs[k++] =cr;
					   };
				   //}
			   }
			   List<DeliveryBatchDay> deliveryBatchDays = this.getDeliveryBatchDays(shoppingCartEntry.getDeleveryTimeRange(), isInternal);
			   table.setDeliveryBatchDays(deliveryBatchDays);

			   // 送货日期默认会选中deliveryBatchDays中的第一条数据，此时配送批次也要是默认日期对应的批次
               List<DeliveryBatchEntry> entrys = this.findDeliveryBatch(storeId, title.getOrderTime());
               table.setEntrys(entrys);
			   title.setTotalPrice(totalPrice);
			   title.setVarietyTotal(totalVarietyTotal.intValue());
			   title.setQuantityTotal(totalQuantity);
		    };
		}
		// 前置仓按照截单时间倒序排
		sortShoppingCart(vo,isXd);
		return vo;
	}

	/**
	 * 前置仓设置根据截单时间正序排
	 * @param vo
	 * @param isXd
	 */
	public void sortShoppingCart(ShoppingCartDetailVo vo,Boolean isXd) {
		if(isXd){
			ShoppingCartDetailVo.TableData[] sites1 = vo.getSites();
			if(null != sites1 && sites1.length > 0 ){
				for(int i = 1; i < sites1.length; i++){
					for(int j = 0; j < sites1.length - i; j++){
						if(!DateTimeUtil.compareTime(sites1[j].getXdCloseTime(),sites1[j+1].getXdCloseTime())){
							ShoppingCartDetailVo.TableData aa = sites1[j];
							sites1[j] = sites1[j+1];
							sites1[j+1] = aa;
						}
					}
				}
			}
		}
	}

	// 鲜道营业中固定一配
	public String getXdDeliveryBatch(List<DeliveryBatchEntry> list){
		list = list.stream().filter(entry ->
				Integer.parseInt(entry.getOptionCode()) == DeliveryBatchTypeEnum.ONE_BATCH.getCode()
		).collect(Collectors.toList());
		return !CollectionUtils.isEmpty(list) ? list.get(0).getOptionName()+"_"+list.get(0).getMemo() : "";
	}


	/**
	 * 设置供应商和仓库信息
	 * @param entry
	 */
	public void setSupplierAndWarehouseInfo(List<ShoppingCartEntry> entry) {
		if (SpringUtil.isEmpty(entry)) {
			return;
		}

		List<Long> supplierIdList = entry.stream().map(item -> item.getSupplyId()).distinct().collect(Collectors.toList());
		QuerySupplierByIdsIDTO idsIDTO = new QuerySupplierByIdsIDTO();
		idsIDTO.setSupplierIds(supplierIdList);
		Map<Long, FullSupplierODTO> supplierIdAndODTOMap = supplierClient.querySupplierByIds(idsIDTO);

		List<Long> warehouseIdList = entry.stream().map(item -> item.getWarehouseId()).distinct().collect(Collectors.toList());
		Map<Long, WarehouseODto> warehouseIdAndODtoMap = warehouseClient.queryWarehouseListByIds(warehouseIdList);

		entry.forEach(item -> {
			Long supplyId = item.getSupplyId();
			FullSupplierODTO fullSupplierODTO = supplierIdAndODTOMap.get(supplyId);
			if (fullSupplierODTO != null) {
				item.setCompanyName(fullSupplierODTO.getSupplierName());
				item.setSupplyStartTime(fullSupplierODTO.getSupplyStartTime());
				item.setSupplyEndTime(fullSupplierODTO.getSupplyEndTime());
			}

			// 仓库名
			Long warehouseId = item.getWarehouseId();
			WarehouseODto warehouseODto = warehouseIdAndODtoMap.get(warehouseId);
			if (warehouseODto != null) {
				item.setWarehouseName(warehouseODto.getWarehouseName());
			}
		});
	}

	/*
	 * 根据商品id获取商品价格信息;
	 */
	public Map<String, ProductPriceEntry> getCommodityPrices(List<Long> commodityIds, Long storeId, String orderTime){
		QYAssert.isTrue(null !=storeId, "客户id参数不能为空");
		QYAssert.isTrue(null !=orderTime, "下单时间参数不能为空");
		QYAssert.isTrue(null !=commodityIds, "商品不能为null");
		Map<String, ProductPriceEntry> entry =new HashMap<String, ProductPriceEntry>();
		Map<String,Object> paramMap =new HashMap<String,Object>();	
		paramMap.put("storeId",storeId);
		paramMap.put("commodityIds",commodityIds);
		paramMap.put("orderTime", orderTime);
		//List<ProductPriceEntry> productPrices = commonService.findProductCommodityPriceByStoreIdAndOrderTime(paramMap);
		Map<String, PromotionProduct> priceMap = commonService.findPromotionPrice(storeId + "",orderTime);
		//List<ProductPriceEntry> storePrices =shoppingCartMapper.findStoreCommodityPriceByStoreIdAndCommodityIds(paramMap);
		List<ProductPriceEntry> storePrices = new ArrayList<>();
		CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
		idto.setStoreId(storeId + "");
		idto.setCommodityIdListAll(commodityIds);
		List<CommodityResultODTO> commodityList = productPriceModelClient.findStoreCommodityList(idto);
		if(!CollectionUtils.isEmpty(commodityList)) {
			for (CommodityResultODTO commodityResult : commodityList) {
				ProductPriceEntry productPriceEntry = new ProductPriceEntry();
				productPriceEntry.setProductId(commodityResult.getCommodityId());
				productPriceEntry.setProductCode(commodityResult.getCommodityCode());
				productPriceEntry.setProductName(commodityResult.getCommodityName());
				productPriceEntry.setPrice(commodityResult.getCommodityPrice());
				storePrices.add(productPriceEntry);
			}
		}

		if(null !=storePrices && !storePrices.isEmpty()){
			storePrices.forEach((s)->{
				PromotionProduct promotionProduct = priceMap.get(s.getProductCode());
				if(promotionProduct != null){
					s.setPrice(new BigDecimal(promotionProduct.getPrice()));
					s.setLimitNum(new BigDecimal(promotionProduct.getLimitNumber()));
				}
				entry.put(s.getProductId(), s);
				/*if(null !=productPrices && !productPrices.isEmpty()){
					productPrices.forEach((p)->{
						if(s.getProductId().equals(p.getProductId())){
							s.setPrice(p.getPrice());
							s.setLimitNum(p.getLimitNum());
						}
					});
				}*/
			});
		}
		return entry;
	}
	
	/*
	 * 根据商品id获取商品限量信息;
	 */
	public Map<String, ProductPriceEntry> getCommodityLimit(List<Long> commodityIds, Long storeId, String orderTime){
		List<ProductPriceEntry> entrys =new ArrayList<ProductPriceEntry>();
		Example ex =new Example(Commodity.class);
		ex.createCriteria().andIn("id", commodityIds);
		List<Commodity> commoditys =commodityMapper.selectByExample(ex);
		if(null !=commoditys && !commoditys.isEmpty()){
			commoditys.forEach(c ->{
				ProductPriceEntry p =new ProductPriceEntry();
				p.setProductId(c.getId().toString());
				p.setProductCode(c.getCommodityCode());
				p.setProductName(c.getCommodityName());
				entrys.add(p);
			});
		}
		//处理特价限量;
		orderService.processProductPrice(entrys, storeId.toString(), orderTime);
		
		//处理客户/产品价格方案限量
		List<ProductLimitEntry> limitProductList = this.findLimitProductByStoreId(storeId.toString());
		if(SpringUtil.isNotEmpty(limitProductList)){
			entrys.forEach(i->{
				limitProductList.forEach(l->{
					if(null ==i.getLimitNum() && i.getProductId().equals(l.getCommodityId())){
						//用客户/产品价格方案限量;
						i.setLimitNum(l.getLimitNumber());
					}
				});
			});
		}

		Map<String, ProductPriceEntry> maps =new HashMap<String, ProductPriceEntry>();
		entrys.forEach(entry ->{
			maps.put(entry.getProductId(), entry);
		});
		return maps;
	}
	
	public List<ProductLimitEntry> findLimitProductByStoreId(String storeId){
    	
    	List<ProductLimitEntry> resultDto = new ArrayList<ProductLimitEntry>();
    	
    	if(StringUtils.isBlank(storeId)){
    		return null;
    	}
		
		String productPriceModelId = commonService.findProductPriceModelIdByStoreId(storeId);
		//客户价格方案限量
		List<ProductLimitEntry> customerLimitProductList = commonService.findCustomerLimitByStoreId(storeId);
		//产品价格方案限量
		List<ProductLimitEntry> priceModelLimitProductList = new ArrayList<ProductLimitEntry>();
	
		if(null != productPriceModelId){
			priceModelLimitProductList = commonService.findProductPriceModelLimitByPriceModelId(productPriceModelId);
		}
		
		if(SpringUtil.isNotEmpty(priceModelLimitProductList)){
			priceModelLimitProductList.forEach(p->{
				ProductLimitEntry dto = new ProductLimitEntry();
				dto.setCommodityId(p.getCommodityId());
				dto.setCommodityName(p.getCommodityName());
				dto.setLimitNumber(p.getLimitNumber());
				resultDto.add(dto);
			});
		}
		
		if(SpringUtil.isNotEmpty(customerLimitProductList)){
			if(SpringUtil.isEmpty(resultDto)){
				customerLimitProductList.forEach(c->{
					ProductLimitEntry dto = new ProductLimitEntry();
					dto.setCommodityId(c.getCommodityId());
					dto.setCommodityName(c.getCommodityName());
					dto.setLimitNumber(c.getLimitNumber());
					resultDto.add(dto);
				});
			}else{
				List<ProductLimitEntry> existProductList = customerLimitProductList.stream().filter(c->{
					return resultDto.stream().anyMatch(i->{
						return i.getCommodityId().equals(c.getCommodityId());
					});
				}).collect(Collectors.toList());
				
				if(SpringUtil.isNotEmpty(existProductList)){
					existProductList.forEach(p->{
						resultDto.forEach(i->{
							if(i.getCommodityId().equals(p.getCommodityId())){
								i.setLimitNumber(p.getLimitNumber());
							}
						});
					});
				}
				
				List<ProductLimitEntry> noExistProductList = customerLimitProductList.stream().filter(c->{
					return !resultDto.stream().anyMatch(i->{
						return i.getCommodityId().equals(c.getCommodityId());
					});
				}).collect(Collectors.toList());
				
				if(SpringUtil.isNotEmpty(noExistProductList)){
					noExistProductList.forEach(p->{
						ProductLimitEntry dto = new ProductLimitEntry();
						dto.setCommodityId(p.getCommodityId());
						dto.setCommodityName(p.getCommodityName());
						dto.setLimitNumber(p.getLimitNumber());
						resultDto.add(dto);
					});
				}
			}
		}
    	return resultDto;
    }

	@Transactional(rollbackFor = Exception.class)
	public Boolean updateItemQuantity(ShoppingCartVo vo){
		ShoppingCartItem item =itemMapper.selectByPrimaryKey(vo.getShoppingCartItemId());
		QYAssert.isTrue(null !=item && null !=item.getCommodityId(), "购物车item不存在或商品id为空");
		ShoppingCart ShoppingCart = shoppingCartMapper.selectByPrimaryKey(item.getShoppingCartId());
		//验证包装类型
		OrderDto orderDto =new OrderDto();
		List<OrderItemDto> list = new ArrayList<OrderItemDto>();
		OrderItemDto itemDto =new OrderItemDto(item.getCommodityId().toString(), vo.getQuantity());
		list.add(itemDto);
		orderDto.setItems(list);

		// 设置storeId
		orderDto.setStoreId(ShoppingCart.getStoreId());
		//orderService.checkProductPackedType(orderDto);
		//验证箱规
		orderService.checkStoreFrozenProduct(orderDto, false);
		
		item.setCommodityNum(vo.getQuantity());
		item.setCreateTime(new Date());
		if(vo.getCreateId() != null){
			item.setCreateId(vo.getCreateId());
		}
		itemMapper.updateByPrimaryKey(item);
		vo.setCommodityId(item.getCommodityId());
		return true;
	}

	/**
	 * 批量修改购物车商品数量(以单个购物车为维度)
	 * @param
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public void batchUpdateItemQuantity(ShoppingCartUpdateVO shoppingCartUpdateVO){
		// 确认数量按订货时间截止
		QYAssert.isTrue(null != shoppingCartUpdateVO.getShoppingCartId(), "购物车id参数为null");
		QYAssert.isTrue(null != shoppingCartUpdateVO.getItemList(), "购物车商品明细null");
		ShoppingCart shoppingCart = shoppingCartMapper.selectByPrimaryKey(shoppingCartUpdateVO.getShoppingCartId());

		//根据商品id，供应商id获取门店订货通用设置
		List<String> commodityIds = new ArrayList<>();
		commodityIds.add(shoppingCartUpdateVO.getItemList().get(0).getCommodityId()+"");
		List<MdShopOrderSettingEntry> list = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(shoppingCart.getStoreId(),commodityIds);
		String supplyBeginTime = "";
		String supplyEedTime = "";
		if(!CollectionUtils.isEmpty(list)){
			MdShopOrderSettingEntry mShopOrderSettingEntry = list.get(0);
			if((shoppingCart.getLogisticsModel().intValue() ==IogisticsModelEnums.DISPATCHING.getCode())){
				supplyBeginTime = mShopOrderSettingEntry.getDefaultWarehouseBeginTime();
				supplyEedTime = mShopOrderSettingEntry.getDefaultWarehouseEndTime();

			}else if(shoppingCart.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
				supplyBeginTime = mShopOrderSettingEntry.getDefaultSupplierBeginTime();
				supplyEedTime = mShopOrderSettingEntry.getDefaultSupplierEndTime();
			}
		}
		QYAssert.isTrue(StringUtils.isNotBlank(supplyBeginTime) && StringUtils.isNotBlank(supplyEedTime), "请检查门店订货通用设置!");

		// 比较当前时间是否在订货时间段
		QYAssert.isTrue(DateTimeUtil.compareNewDate(supplyBeginTime, supplyEedTime), "超出订货时间,无法确认数量!");
		ThreadLocalUtils.setXd(true);
		for(ShoppingCartUpdateVO.shoppingCartItem item:shoppingCartUpdateVO.getItemList()){
			QYAssert.isTrue(null != item && null != item.getShoppingCartItemId(), "购物车itemId不能为空");
			QYAssert.isTrue(null != item && null != item.getShares(), "份数不能为空");

			ShoppingCartVo vo = new ShoppingCartVo();
			vo.setShoppingCartItemId(item.getShoppingCartItemId());
			vo.setQuantity(item.getShares().multiply(item.getCommodityPackageSpec()));
			this.updateItemQuantity(vo);
		}
		ThreadLocalUtils.remove();
	}
	/*
	 * 根据商品id list及企业id,返回拆单分组信息;
	 */
	public Map<Integer, Map<String, List<String>>> getSplitOrderInfo(List<Long> commodityIds, Long enterpriseId){
		QYAssert.isTrue(null !=enterpriseId, "企业id参数不能为空");
		QYAssert.isTrue(null !=commodityIds, "商品list不能为空");
		Map<Integer, Map<String, List<String>>> result =new HashMap<Integer, Map<String,List<String>>>();
		List<SplitOrderListEntry> resultList = shoppingCartMapper.getSplitOrderInfo(commodityIds, enterpriseId);
		// 设置商品供应商id和默认仓库id
		setCommodityDefaultSupplierIdAndWarehouseId(resultList);

		List<SplitOrderListEntry> list = new ArrayList<SplitOrderListEntry>();
		
		if(resultList !=null && !resultList.isEmpty()){
			Iterator<SplitOrderListEntry> it = resultList.iterator();
			while(it.hasNext()){
				SplitOrderListEntry vo =it.next();
				if(vo.getLogisticsModel() !=null
						&& ((vo.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode() && null !=vo.getSupplierId())
								|| (vo.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode() && null !=vo.getWarehouseId())
								|| (vo.getLogisticsModel().intValue() ==IogisticsModelEnums.DISPATCHING.getCode() && null !=vo.getWarehouseId()))){
					list.add(vo);
				}
			}
		}
		//直送
		Map<Integer, List<SplitOrderListEntry>> map=list.stream().filter(vo -> null !=vo.getLogisticsModel() 
				&& vo.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode())
				.collect(Collectors.groupingBy(SplitOrderListEntry::getLogisticsModel));
		map.forEach((key,value)->{
			Map<String, List<String>> maps =new HashMap<String, List<String>>();
			result.put(key, maps);
			Map<Long, List<SplitOrderListEntry>> secondMap =value.stream()
					.filter( i->null !=i.getSupplierId()).collect(Collectors.groupingBy(SplitOrderListEntry::getSupplierId));
			secondMap.forEach((supplierId, items)->{
				List<String> commoditys =new ArrayList<String>();
				items.forEach(e ->{
					commoditys.add(e.getCommodityId().toString());
				});
				maps.put(supplierId.toString(), commoditys);
			});
		});
		
		//直通-配送
		map=list.stream().filter(vo -> null !=vo.getLogisticsModel() && 
				(vo.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode() 
				|| vo.getLogisticsModel().intValue() ==IogisticsModelEnums.DISPATCHING.getCode()))
				.collect(Collectors.groupingBy(SplitOrderListEntry::getLogisticsModel));
		map.forEach((key,value)->{
			Map<String, List<String>> maps =new HashMap<String, List<String>>();
			result.put(key, maps);
			Map<Long, List<SplitOrderListEntry>> secondMap =value.stream().filter( i->null !=i.getWarehouseId()).collect(Collectors.groupingBy(SplitOrderListEntry::getWarehouseId));
			secondMap.forEach((warehouseId, items)->{
				List<String> commoditys =new ArrayList<String>();
				items.forEach(e ->{
					commoditys.add(e.getCommodityId().toString());
				});
				maps.put(warehouseId.toString(), commoditys);
			});
		}); 
		return result;
	}

	private void setCommodityDefaultSupplierIdAndWarehouseId(List<SplitOrderListEntry> resultList) {
		// 根据商品id获取供应商id
		List<Long> commodityIdList = resultList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
		Map<Long, CommoditySupplierODto> commodityIdAndODTOMap = commoditySupplierClient.queryCommodityDefaultSupplier(commodityIdList);
		Map<Long, CommodityWarehouseODto> commodityIdAndWarehouseODtoMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);
		resultList.forEach(item -> {
			Long commodityId = item.getCommodityId();
			CommoditySupplierODto supplierODto = commodityIdAndODTOMap.get(commodityId);
			if (supplierODto != null) {
				item.setSupplierId(supplierODto.getSupplierId());
			}

			CommodityWarehouseODto warehouseODto = commodityIdAndWarehouseODtoMap.get(commodityId);
			if (warehouseODto != null) {
				item.setWarehouseId(warehouseODto.getWarehouseId());
			}
		});
	}

	public Map<String, BigDecimal> getShoppingCartItemNums(Long storeId, Long stallId){
		Map<String, BigDecimal> result =new HashMap<String, BigDecimal>();
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

		List<Long> stallIdList = new ArrayList<>();
		if(StallUtils.isStallSubcontractor(tokenInfo.getManagementMode())){
			stallIdList.add(stallId);
		}
		List<ShoppingCartEntry> entry =shoppingCartMapper.shoppingCartDetailConsignment(storeId,null,false,false,null,tokenInfo.getConsignmentId(), stallIdList);
		if(null !=entry && !entry.isEmpty()){
			setSupplierAndWarehouseInfo(entry);

			entry.forEach(shoppingCartEntry ->{
				List<ShoppingCartItemEntry> items =shoppingCartEntry.getItems();
				if(null !=items && !items.isEmpty()){
					items.forEach(shoppingCartItemEntry ->{
						result.put(shoppingCartItemEntry.getCommodityId().toString(), shoppingCartItemEntry.getQuantity());
					});
				}
			});
		}
		return result;
	}
	
	private Integer getDeliveryBatchDay(){
		return shoppingCartMapper.getDeliveryBatchDay();
	}
	
	//合并代码时,该方法以master为主
	public List<DeliveryBatchDay> getDeliveryBatchDayStr(){
		Integer day =getDeliveryBatchDay();
		Calendar c =null;
		List<DeliveryBatchDay> list =new ArrayList<DeliveryBatchDay>();
		if(null !=day && day >0){
			for(int i=0; i<day;i++){
				DeliveryBatchDay d =new DeliveryBatchDay();
				c =Calendar.getInstance();
				c.add(Calendar.DAY_OF_YEAR, i);
				d.setValue(i+"");
				d.setDay(format.format(c.getTime()));
				list.add(d);
			}
		}
		return list;
	}

	public List<DeliveryBatchDay> getDeliveryBatchDays(String deleveryTimeRange, boolean isInternal){
		List<DeliveryBatchDay> deliveryBatchDays = new ArrayList<>();
		if (!StringUtil.isBlank(deleveryTimeRange)){
			String[] ranges = deleveryTimeRange.split("-");
			// 门店代理时间范围固定为0-2
			if (isInternal || ThreadLocalUtils.getAdmin()) {
				ranges = new String[] {"0", "2"};
			}
			int x = Integer.parseInt(ranges[0]);
			int y = Integer.parseInt(ranges[1]);
			int length = y - x + 1;
			for (int j = 0; j < length; j++) {
				DeliveryBatchDay deliveryBatchDay = new DeliveryBatchDay();
				deliveryBatchDay.setValue(String.valueOf(j));

				Calendar calendar =Calendar.getInstance();
				calendar.add(Calendar.DAY_OF_YEAR, x + j);
				deliveryBatchDay.setDay(format.format(calendar.getTime()));
				deliveryBatchDays.add(deliveryBatchDay);
			}
		}

		return deliveryBatchDays;
	}
	
	public List<DeliveryBatchEntry> findDeliveryBatch(Long storeId, String date){
		 Shop shop = shopService.getShopByStoreId(storeId);
		 Boolean isBigShop = StallUtils.isStallSubcontractor(shop.getManagementMode());

		 List<DeliveryBatchEntry> list =shoppingCartMapper.findDeliveryBatchByCode(null);
		 Calendar c =Calendar.getInstance();
		 String now =format.format(c.getTime());
		 if(date.equals(now)){

			 // 大店当天送货日期可以选择2配
			if(isBigShop){
				list = list.stream().filter(entry ->
						Integer.parseInt(entry.getOptionCode()) != DeliveryBatchTypeEnum.ONE_BATCH.getCode()
								&& Integer.parseInt(entry.getOptionCode()) != DeliveryBatchTypeEnum.NEW_SHOP_BATCH.getCode()
								&& Integer.parseInt(entry.getOptionCode()) != DeliveryBatchTypeEnum.REPLENISHMENT.getCode()
				).collect(Collectors.toList());
			}else {
				// 若送货日期=T（即今天），则配送批次不能选择1配、新开店（注：不排除将来增加3配）、配货
				list = list.stream().filter(entry ->
						Integer.parseInt(entry.getOptionCode()) != DeliveryBatchTypeEnum.ONE_BATCH.getCode()
								&& Integer.parseInt(entry.getOptionCode()) != DeliveryBatchTypeEnum.NEW_SHOP_BATCH.getCode()
								&& Integer.parseInt(entry.getOptionCode()) != DeliveryBatchTypeEnum.REPLENISHMENT.getCode()
								&& Integer.parseInt(entry.getOptionCode()) != DeliveryBatchTypeEnum.TWO_BATCH.getCode()
				).collect(Collectors.toList());
			}
		 }else{
		 	 // 补货批次
			 List<DeliveryBatchEntry> replenishmentList = list.stream().filter(entry ->
					 Integer.parseInt(entry.getOptionCode()) == DeliveryBatchTypeEnum.REPLENISHMENT.getCode()
			 ).collect(Collectors.toList());

		 	 // 若送货日期≥T+1（即明天及以后），则配送批次只能选择1配、新开店
			 // 仅当“门店状态=开业前 2”时，配送批次列表才显示“新开店”批次
			 if (shop.getShopStatus() == 2) {
                 list = list.stream().filter(entry ->
                         Integer.parseInt(entry.getOptionCode()) == DeliveryBatchTypeEnum.ONE_BATCH.getCode()
                                 || Integer.parseInt(entry.getOptionCode()) ==DeliveryBatchTypeEnum.NEW_SHOP_BATCH.getCode()
                 ).collect(Collectors.toList());
			 } else {
                 list = list.stream().filter(entry -> Integer.parseInt(entry.getOptionCode()) == DeliveryBatchTypeEnum.ONE_BATCH.getCode()).collect(Collectors.toList());
			 }

			 // 只用送货日期= T+1 时，才能看到选择补货批次
			 Boolean ifReplenishment = date.equals(format.format(DateUtil.addDay(new Date(), 1)));
			 if(ifReplenishment){
				 list.addAll(replenishmentList);
			 }
		 }

		return list;
	}

	//(手持)删除购物车
	@Transactional(rollbackFor = Exception.class)
	public HandCartRspVO handleRemoveShoppingCart(ShoppingCartVo vo) {
		HandCartRspVO rspvo = new HandCartRspVO();

		BigDecimal commodityPackageSpec = getUpdateDeletePackageSpec(vo.getShoppingCartItemId());

		//删除购物车
		removeShoppingCart(vo);

		//获取购物车数量，合计
		setRspVO(commodityPackageSpec, vo, rspvo);
		return rspvo;
	}

	/**
	 * 获取删除或者修改的商品的包装规格
	 * @return
	 */
	public BigDecimal getUpdateDeletePackageSpec(Long shoppingCartItemId){
		if(null != shoppingCartItemId){
			ShoppingCartItem item = itemMapper.selectByPrimaryKey(shoppingCartItemId);
			QYAssert.isTrue(item != null, "请刷新购物车！");
			CommodityVO vo = new CommodityVO();
			vo.setCommodityId(item.getCommodityId());
			List<CommodityBasicEntry> basicEntryList = commodityMapper.findCommodityBasicListByParam(vo);
			return basicEntryList.get(0).getCommodityPackageSpec();
		}
		return null;
	}

	//获取购物车数量，合计
	private void setRspVO(BigDecimal commodityPackageSpec, ShoppingCartVo vo, HandCartRspVO rspvo) {
		List<ShoppingCartEntry> list = shoppingCartMapper.shoppingCartDetail(vo.getStoreId(),vo.getShoppingCartId(),false,false,null);
		BigDecimal totalQuanty = BigDecimal.ZERO;
		BigDecimal totalAmount = BigDecimal.ZERO;
		if(!CollectionUtils.isEmpty(list)){
			commonService.setShopCartItemInfo(list);

			List<ShoppingCartItemEntry> itemList = list.get(0).getItems();
			if(!CollectionUtils.isEmpty(itemList)){
				List<Long> commmodityIds = new ArrayList<>();
				for(ShoppingCartItemEntry dto:itemList){
					commmodityIds.add(dto.getCommodityId());
				}

				//查找购物车里面的商品价格;
				Map<String, ProductPriceEntry> priceHashMap =getCommodityPrices(commmodityIds, vo.getStoreId(), DateTimeUtil.defaultDeliveryDate());

				for(ShoppingCartItemEntry dto:itemList){
					BigDecimal  price = priceHashMap.get(dto.getCommodityId()+"") == null ? BigDecimal.ZERO : priceHashMap.get(dto.getCommodityId()+"").getPrice();
					totalQuanty = totalQuanty.add(dto.getQuantity());
					totalAmount = totalAmount.add(dto.getQuantity().multiply(price).setScale(2, BigDecimal.ROUND_HALF_UP));
				}
				rspvo.setQuantity(vo.getQuantity());
				rspvo.setVarietyTotal(new BigDecimal(itemList.size()));
				rspvo.setTotalQuantity(totalQuanty);
				rspvo.setTotalAmount(totalAmount);
				rspvo.setShares(vo.getQuantity().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP));
			}
		}
	}

	//(手持)修改购物车商品数量;
	@Transactional(rollbackFor = Exception.class)
	public HandCartRspVO handleUpdateItemQuantity(ShoppingCartVo vo) {
		Boolean isXd = getIsXd(vo.getStoreId());
		ThreadLocalUtils.setXd(isXd);

		HandCartRspVO rspvo = new HandCartRspVO();

		ShoppingCartItem item =itemMapper.selectByPrimaryKey(vo.getShoppingCartItemId());
		QYAssert.isTrue(null !=item && null !=item.getCommodityId(), "购物车item不存在或商品id为空");
		/*// 校验B端库存依据
		Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
		orderQuantityMap.put(item.getCommodityId(), vo.getQuantity());
		List<BStockShortResponseVO> responseVOList = bStockService.checkBStock(vo.getStoreId(), vo.getOrderType(), DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"), orderQuantityMap, "修改购物车", null,vo.getCreateId());
		if(!CollectionUtils.isEmpty(responseVOList)){
			rspvo.setCommodityId(item.getCommodityId() + "");
			rspvo.setInventoryQuantity(responseVOList.get(0).getInventoryQuantity());
			return rspvo;
		}*/

		BigDecimal commodityPackageSpec = getUpdateDeletePackageSpec(vo.getShoppingCartItemId());

		//修改购物车数量
		updateItemQuantity(vo);

		//获取购物车数量，合计
		setRspVO(commodityPackageSpec, vo, rspvo);
		ThreadLocalUtils.remove();
		return rspvo;
	}

	public ShoppingCart queryShoppingCartById(Long cartId){
		return shoppingCartMapper.selectByPrimaryKey(cartId);
	}

	public Boolean getIsXd(Long storeId){
		Shop shop = shopService.getShopFromCacheThenDb(storeId);
		// 是否是鲜道前置仓
		Boolean isXd = shop.getShopType().equals(ShopTypeEnums.XD.getCode());
		return isXd;
	}


	/**
	 * 管理员版   购物车列表
	 * @param
	 * @return
	 */
	public TablePageInfo<ShoppingCartDetailVo> queryShoppingCartDetailAdmin(ShoppingCartAdminPageVo shoppingCartAdminPageVo){
		Long userId = shoppingCartAdminPageVo.getUserId();
		// 是否大店判断条件
		shoppingCartAdminPageVo.setBigShop(shoppingCartAdminPageVo.getBigShop() == null ? false : shoppingCartAdminPageVo.getBigShop());

		// 分页查询去除重复的代理购物车的客户id(storeId)
		PageInfo<ShoppingCart> storePageInfo = PageHelper.startPage(shoppingCartAdminPageVo.getPageNo(), shoppingCartAdminPageVo.getPageSize()).doSelectPageInfo(() ->{
			shoppingCartMapper.getDistinctShoppingCartAdmin(shoppingCartAdminPageVo);
		});
		TablePageInfo<ShoppingCartDetailVo> shoppingCartPageInfo = BeanUtil.pageInfo2TablePageInfo(storePageInfo, TablePageInfo.class);

		if(SpringUtil.isEmpty(storePageInfo.getList())){
			return shoppingCartPageInfo;
		}

		List<Long> storeIdList = storePageInfo.getList().stream().map(item -> item.getStoreId()).collect(Collectors.toList());
		/*ShoppingCartDetailVo detail1 = shoppingCartAdminService.queryShoppingCart(storePageInfo.getList().get(0).getStoreId(), true, null, false, userId);
		for (int i = 1; i < storeIdList.size(); i++) {
			ShoppingCartDetailVo detail2 = shoppingCartAdminService.queryShoppingCart(storeIdList.get(i), true, null, false, userId);
			detail1.setSites(ArrayUtils.addAll(detail1.getSites(), detail2.getSites()));
		}
		List<ShoppingCartDetailVo> cartList = new ArrayList<>();
		cartList.add(detail1);
		shoppingCartPageInfo.setList(cartList);*/
		List<ShoppingCartDetailVo> cartList = new ArrayList<>();
		ShoppingCartDetailVo detail1 = shoppingCartAdminService.queryShoppingCartAdmin(storeIdList, userId, shoppingCartAdminPageVo.getBigShop());
		cartList.add(detail1);
		shoppingCartPageInfo.setList(cartList);
		return  shoppingCartPageInfo;
	}

	/**
	 * 门店非代理，校验不能添加代销商商品
	 *//*
	public void checkAddConsignmentCart(List<Long> commodityList){
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

		if(!tokenInfo.getIsInternal() && (tokenInfo.getConsignmentId() == null || tokenInfo.getConsignmentId() == 0 )){
			List<ConsignmentCommodityODTO> consignmentCommodityList = consignmentClient.selectConsignmentCommodityByShopId(tokenInfo.getShopId());
			if(!CollectionUtils.isEmpty(consignmentCommodityList)){
				List<Long> consignmentCommodityIdList = consignmentCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
				commodityList.forEach(commodityId ->{
					if(consignmentCommodityIdList.contains(commodityId)){
						Commodity commodity = commodityMapper.selectByPrimaryKey(commodityId);
						QYAssert.isTrue(false, "代销商品不允许添加门店购物车，请用代销商户账号订货,商品编码：" + commodity.getCommodityCode());
					}
				});
			}
		}
	}*/
}