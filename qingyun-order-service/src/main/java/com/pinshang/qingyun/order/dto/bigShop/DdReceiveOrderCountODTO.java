package com.pinshang.qingyun.order.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Data
public class DdReceiveOrderCountODTO {

    @ApiModelProperty("档口id")
    private Long stallId;

    /** 配送批次 1=1配 + 补货  2=2配  8=新开店 */
    private Integer deliveryBatch;

    @ApiModelProperty("关联订单数")
    private Integer orderCodeCount;

    private String orderCode;

    private Long docId;
}
