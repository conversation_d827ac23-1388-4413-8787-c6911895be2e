/*
package com.pinshang.qingyun.order.dto.cup;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrderItemDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private String remark;
    private String productId;
    private BigDecimal price;
    private BigDecimal productNum;
    private Integer type;
    private Integer status = 0;

    private BigDecimal productNumLimit;
    private String productName;

    private Long itemId;
    private Integer presaleStatus; // 是否预售  0 否   1 是

    private Integer stockType; // 库存依据  1=依据大仓, 2=不限量订货, 3=限量供应
    private BigDecimal originalPrice;

    //是否称重(单位是否为kg)0-不称量,1-称重
    private Integer isWeight;

    //包装规格
    private BigDecimal commodityPackageSpec;

    *//*

*/
/**限制购买的数量*//*
*/
/*

    private Integer limitNumber;

    *//*

*/
/** 剩余可用的总限量*//*
*/
/*

    private BigDecimal availableTotalLimit;

    *//*

*/
/** 剩余可用的客户限购*//*
*/
/*

    private BigDecimal availableStoreLimit;

    *//*

*/
/** 特价Id*//*
*/
/*

    private Long pricePromotionId;

    *//*

*/
/** 是否存在特价*//*
*/
/*

    private boolean specialFlag;

    *//*


import java.math.BigDecimal; */
/** 销售箱规*//*
*/
/*

    private BigDecimal salesBoxCapacity;

    /** 买赠方案id*//*

    private Long giftModelId;

    */
/** 小类(后台分类id) *//*

    private Long commodityThirdId;

    public Long getCommodityThirdId() {
        return commodityThirdId;
    }

    public void setCommodityThirdId(Long commodityThirdId) {
        this.commodityThirdId = commodityThirdId;
    }

    public Long getGiftModelId() {
        return giftModelId;
    }

    public void setGiftModelId(Long giftModelId) {
        this.giftModelId = giftModelId;
    }

    public BigDecimal getSalesBoxCapacity() {
        return salesBoxCapacity;
    }

    public void setSalesBoxCapacity(BigDecimal salesBoxCapacity) {
        this.salesBoxCapacity = salesBoxCapacity;
    }

    public boolean isSpecialFlag() {
        return specialFlag;
    }

    public void setSpecialFlag(boolean specialFlag) {
        this.specialFlag = specialFlag;
    }

    public Integer getLimitNumber() {
        return limitNumber;
    }

    public void setLimitNumber(Integer limitNumber) {
        this.limitNumber = limitNumber;
    }

    public BigDecimal getAvailableTotalLimit() {
        return availableTotalLimit;
    }

    public void setAvailableTotalLimit(BigDecimal availableTotalLimit) {
        this.availableTotalLimit = availableTotalLimit;
    }

    public BigDecimal getAvailableStoreLimit() {
        return availableStoreLimit;
    }

    public void setAvailableStoreLimit(BigDecimal availableStoreLimit) {
        this.availableStoreLimit = availableStoreLimit;
    }

    public Long getPricePromotionId() {
        return pricePromotionId;
    }

    public void setPricePromotionId(Long pricePromotionId) {
        this.pricePromotionId = pricePromotionId;
    }

    public BigDecimal getCommodityPackageSpec() {
        return commodityPackageSpec;
    }

    public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
        this.commodityPackageSpec = commodityPackageSpec;
    }

    public Integer getIsWeight() {
        return isWeight;
    }

    public void setIsWeight(Integer isWeight) {
        this.isWeight = isWeight;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Integer getStockType() {
        return stockType;
    }

    public void setStockType(Integer stockType) {
        this.stockType = stockType;
    }

    @SuppressWarnings("unused")
    private OrderItemDto() {
    }

    public OrderItemDto(String remark, String productId, String productName, BigDecimal originalPrice, BigDecimal price,
                        BigDecimal productNum, Integer type, int status,
                        BigDecimal productNumLimit,Long giftModelId) {
        super();
        this.remark = remark;
        this.productId = productId;
        this.productName = productName;
        this.price = price;
        this.productNum = productNum;
        this.type = type;
        this.status = status;
        this.productNumLimit = productNumLimit;
        this.originalPrice = originalPrice;
        this.giftModelId = giftModelId;
    }

    public OrderItemDto(String productId, BigDecimal originalPrice, BigDecimal price, BigDecimal productNum, String remark, Integer type) {
        this.remark = remark;
        this.productId = productId;
        this.price = price;
        this.productNum = productNum;
        this.type = type;
        this.originalPrice = originalPrice;
    }

    public OrderItemDto(String productId, BigDecimal originalPrice, BigDecimal price, BigDecimal productNum, String remark, Integer type,Long giftModelId) {
        this(productId,originalPrice,price,productNum,remark,type);
        this.giftModelId = giftModelId;
    }

    public OrderItemDto(String remark, String productId, String productName, BigDecimal price,
                        BigDecimal productNum, Integer type, Integer status,
                        BigDecimal productNumLimit) {
        super();
        this.remark = remark;
        this.productId = productId;
        this.productName = productName;
        this.price = price;
        this.productNum = productNum;
        this.type = type;
        this.status = status;
        this.productNumLimit = productNumLimit;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public String getProductId() {
        return productId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public BigDecimal getProductNum() {
        return productNum;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public BigDecimal amount() {
        if (null == this.price) {
            this.price = BigDecimal.ZERO;
        }
        return price.multiply(productNum).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal originalAmount(){ // 原始金额
        if(null == this.originalPrice){
            this.originalPrice = BigDecimal.ZERO;
        }
        return originalPrice.multiply(productNum).setScale(2,BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal promotionAmount(){ // 促销金额
        BigDecimal promotionAmount = originalAmount().subtract(amount());
        return promotionAmount.compareTo(BigDecimal.ZERO) > 0 ? promotionAmount : BigDecimal.ZERO;
    }

    public void updatePrice(BigDecimal price) {
        if (null != price && price.compareTo(BigDecimal.ZERO) > 0) {
            this.price = price;
        }
    }

    public Integer getType() {
        if (this.type == null || this.type == 0) {
            this.type = 1;
        }
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setProductNum(BigDecimal productNum) {
        this.productNum = productNum;
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getProductNumLimit() {
        return productNumLimit;
    }

    public void setProductNumLimit(BigDecimal productNumLimit) {
        this.productNumLimit = productNumLimit;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    //用于属性拷贝，不在此设置价格
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Override
    public String toString() {
        return "OrderItemDto [remark=" + remark + ", productId="
                + productId + ", price=" + price + ", productNum="
                + productNum + ", type=" + type + ", status=" + status
                + ", productNumLimit=" + productNumLimit + "]";
    }
}

*/
