package com.pinshang.qingyun.order.model.bigShop;

import com.pinshang.qingyun.base.po.BasePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Entity
@Table(name="t_dd_receive_doc_log")
public class DdReceiveDocLog extends BasePO {

    private Long docId;

    /** 送货日期 */
    private Date orderTime;

    /** 门店id **/
    private Long shopId;

    /** 档口id */
    private Long stallId;

    /** 配送批次 1=1配 + 补货  2=2配  8=新开店 */
    private Integer deliveryBatch;


    /** 商品id **/
    private Long  commodityId;

    /** 订货数量 */
    private BigDecimal quantity;

    /** 实发数量 */
    private BigDecimal realDeliveryQuantity;

    /** 收货数量 */
    private BigDecimal realReceiveQuantity;


    /** 少收数量 */
    private BigDecimal shortReceiveQuantity;

    /** 库区 1排面区 2拣货区 3存储区 */
    private Integer storageArea;

    /** 货位号id */
    private Long goodsAllocationId;

    /** 是否已经生成少货单 0-未生成  1-已生成 */
    private Integer shortStatus = 0;

    public Integer getShortStatus() {
        return shortStatus;
    }

    public void setShortStatus(Integer shortStatus) {
        this.shortStatus = shortStatus;
    }

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getStallId() {
        return stallId;
    }

    public void setStallId(Long stallId) {
        this.stallId = stallId;
    }

    public Integer getDeliveryBatch() {
        return deliveryBatch;
    }

    public void setDeliveryBatch(Integer deliveryBatch) {
        this.deliveryBatch = deliveryBatch;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getRealDeliveryQuantity() {
        return realDeliveryQuantity;
    }

    public void setRealDeliveryQuantity(BigDecimal realDeliveryQuantity) {
        this.realDeliveryQuantity = realDeliveryQuantity;
    }

    public BigDecimal getRealReceiveQuantity() {
        return realReceiveQuantity;
    }

    public void setRealReceiveQuantity(BigDecimal realReceiveQuantity) {
        this.realReceiveQuantity = realReceiveQuantity;
    }

    public BigDecimal getShortReceiveQuantity() {
        return shortReceiveQuantity;
    }

    public void setShortReceiveQuantity(BigDecimal shortReceiveQuantity) {
        this.shortReceiveQuantity = shortReceiveQuantity;
    }

    public Integer getStorageArea() {
        return storageArea;
    }

    public void setStorageArea(Integer storageArea) {
        this.storageArea = storageArea;
    }

    public Long getGoodsAllocationId() {
        return goodsAllocationId;
    }

    public void setGoodsAllocationId(Long goodsAllocationId) {
        this.goodsAllocationId = goodsAllocationId;
    }
}
