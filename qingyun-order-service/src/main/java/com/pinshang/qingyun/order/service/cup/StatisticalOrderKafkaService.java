package com.pinshang.qingyun.order.service.cup;

import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderList;
import com.pinshang.qingyun.order.service.OrderAsyncKafkaService;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class StatisticalOrderKafkaService {


    @Autowired
    OrderAsyncKafkaService orderAsyncKafkaService;

    public void sendStatisticalOrderKafka(Order order, OrderDto orderDto, KafkaMessageOperationTypeEnum operationTypeEnum, Long userId) {

        List<OrderList> orderList = new ArrayList<>();
        orderDto.getItems().forEach(i -> {
            OrderList ol = new OrderList();
            ol.setId(i.getItemId());
            ol.setCommodityId(Long.valueOf(i.getProductId()));
            ol.setCommodityNum(i.getProductNum());
            ol.setType(i.getType());
            ol.setRemark(i.getRemark());
            ol.setCommodityPrice(i.getPrice());
            ol.setOrderId(order.getId());
            ol.setTotalPrice(i.amount());
            orderList.add(ol);
        });
        order.setOrderList(orderList);
        if(KafkaMessageOperationTypeEnum.UPDATE.equals(operationTypeEnum)){
            orderAsyncKafkaService.sendKafkaUpdateOrderMessage(order);
        }
    }



}
