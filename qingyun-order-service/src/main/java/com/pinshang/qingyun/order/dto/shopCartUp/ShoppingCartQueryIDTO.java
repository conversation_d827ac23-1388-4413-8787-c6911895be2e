package com.pinshang.qingyun.order.dto.shopCartUp;

import com.pinshang.qingyun.order.dto.Pagination;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2025/4/8
 */
@Data
public class ShoppingCartQueryIDTO extends Pagination {

    /** 客户id */
    private Long storeId;

    /** 是否代理 */
    private Boolean isInternal = false;

    /** 当前登录用户id */
    private Long userId;

    /** true 大店  false 非大店*/
    private Boolean bigShop = false;

    /** 购物车详情必传 */
    private Long shoppingCartId;
    /** 购物车详情传送货日期 */
    private String orderTime;

    private Boolean handle;
}
