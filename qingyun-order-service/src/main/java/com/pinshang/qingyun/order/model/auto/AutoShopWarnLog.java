package com.pinshang.qingyun.order.model.auto;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Builder;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name="t_md_shop_warn_log")
@Builder
public class AutoShopWarnLog extends BaseIDPO  {

    /**
     * 关联门店id
     */
    private Long shopId;

    /**
     * 截单时间
     */
    private String orderTime;

    /**
     * 商品id列表
     */
    private String commodityIds;

    /**
     * 日志详情
     */
    private String remark;

    private Long createId;

    private Date createTime;




}
