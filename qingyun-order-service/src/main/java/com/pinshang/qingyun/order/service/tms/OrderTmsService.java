package com.pinshang.qingyun.order.service.tms;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.tms.RefOrderForWaybillODTO;
import com.pinshang.qingyun.order.dto.tms.RefOrderItemForWaybillODTO;
import com.pinshang.qingyun.order.dto.tms.SelectRefOrderForWaybillIDTO;
import com.pinshang.qingyun.order.dto.tms.WarehouseConfirmInfoODTO;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.mapper.tms.OrderTmsMapper;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import com.pinshang.qingyun.tms.dto.sfopen.CheckTransportFlowIDTO;
import com.pinshang.qingyun.tms.dto.sfopen.CheckTransportFlowODTO;
import com.pinshang.qingyun.tms.service.TmsSfopenClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 【订单系统 <-> 物流系统】
 */
@Service
public class OrderTmsService {
	
    @Autowired
    private OrderTmsMapper orderTmsMapper;

	@Autowired
	private TmsSfopenClient tmsSfopenClient;

	@Autowired
	private DictionaryClient dictionaryClient;

	@Autowired
	private TdaOrderService tdaOrderService;
    
    /**
     * 查询【源单-用于运单】
     * 
     * @param idto
     * @return
     */
    public RefOrderForWaybillODTO selectRefOrder(SelectRefOrderForWaybillIDTO idto) {
    	if (null == idto) {
    		return null;
    	}
    	
    	Integer refType = idto.getRefType();
    	Long refOrderId = idto.getRefOrderId();
    	if (null == refType || null == refOrderId) {
    		return null;
    	}
    	
    	RefOrderForWaybillODTO refOrder = null;
    	if (refType.intValue() == 11) {
    		refOrder = orderTmsMapper.selectOrder(idto);
    		if (null != refOrder) {
    			List<RefOrderItemForWaybillODTO> refOrderItemList = orderTmsMapper.selectOrderItemList(idto);
    			refOrder.setRefOrderItemList(refOrderItemList);
    		}
    	} else if (refType.intValue() == 21) {
    		refOrder = orderTmsMapper.selectReturnOrder(idto);
    		if (null != refOrder) {
    			List<RefOrderItemForWaybillODTO> refOrderItemList = orderTmsMapper.selectReturnOrderItemList(idto);
    			refOrder.setRefOrderItemList(refOrderItemList);
    		}
    	}
    	
    	return refOrder;
    }
    
    /**
	 * 查询【大仓确认信息】列表
	 * 
	 * @param refOrderId
	 * @return
	 */
	public List<WarehouseConfirmInfoODTO> selectWarehouseConfirmInfoList(Long refOrderId) {
		if (null == refOrderId) {
			return Collections.emptyList();
		}
		
		return orderTmsMapper.selectWarehouseConfirmInfoList(refOrderId);
	}

	public CheckTransportFlowODTO checkTransportFlow(Long storeId,
													 Integer businessType,
													 Integer deliveryBatch,
													 Long logisticsCenterId,
													 String logisticsCarrierCode){
		CheckTransportFlowIDTO idto = new CheckTransportFlowIDTO();
		idto.setStoreId(storeId);
		idto.setDeliveryBatch(deliveryBatch);
		idto.setBusinessType(businessType);
		idto.setLogisticsCenterId(logisticsCenterId);
		idto.setProductCode(logisticsCarrierCode);
		return tmsSfopenClient.checkTransportFlow(idto);

	}

	public void bCountryCheckTransportFlow(Long storeId){
		DictionaryODTO sfProductCode = dictionaryClient.getDictionaryByCode("sfProductCode");
		String logisticsCarrierCode = Optional.ofNullable(sfProductCode).map(DictionaryODTO::getOptionValue).orElse(null);
		TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO = tdaOrderService.queryBCountryDeliveryTimeRangeListByStoreId(storeId);
		QYAssert.notNull(tdaDeliveryTimeRangeODTO,"送货时间段为空");
		checkTransportFlow(storeId, BusinessTypeEnums.B_COUNTRY.getCode(),
				tdaDeliveryTimeRangeODTO.getDeliveryBatch(),tdaDeliveryTimeRangeODTO.getLogisticsCenterId(), logisticsCarrierCode);

	}
    
}
