package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.dto.tob.TobCommodityStockIDTO;
import com.pinshang.qingyun.order.mapper.CommodityItemMapper;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.TobCommodityStockMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntry;
import com.pinshang.qingyun.order.model.tob.TobCommodityStock;
import com.pinshang.qingyun.order.service.xda.v4.ToBService;
import com.pinshang.qingyun.order.vo.tob.TobCommodityStockKafkaVo;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.tms.service.LogisticsCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2024/6/24
 */
@Slf4j
@Service
public class TobCommodityStockService {


    @Autowired
    private TobCommodityStockMapper tobCommodityStockMapper;

    @Autowired
    private ToBService toBService;

    @Autowired
    private BStockService bStockService;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private CommodityItemMapper commodityItemMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private LogisticsCenterClient logisticsCenterClient;

    @Transactional(rollbackFor = Exception.class)
    public void updateTobCommodityStock(
            TobCommodityStockKafkaVo vo,
            Long commodityId) {
        XDCommodityODTO xdCommodityODTOById = commodityMapper.getXDCommodityODTOById(commodityId);
        Integer productType = xdCommodityODTOById.getProductType();
        if(productType !=2){
            //非组合品（子品）的处理
            processKafkaCommodityStock(vo,commodityId);
        }else{
            if (BusinessTypeEnums.B_COUNTRY.getCode().equals(vo.getType())) {
                //B端全国不处理组合品
                return;
            }
            //组合品的处理
            insertOrIgnoreUpdateTobCommodityStock(vo, commodityId);

        }

    }

    /**
     *
     * @param vo kafka消息转换vo
     * @param commodityId 发来消息的商品id
     */
    private void processKafkaCommodityStock(TobCommodityStockKafkaVo vo,
                                            Long commodityId
    ) {
        insertOrUpdateTobCommodityStock(vo, commodityId);

        //查看子品下面是否存在父组合，并循环处理
        try {
            processCommodityItemMasterStock(vo,commodityId);
        }catch (Exception e){
            log.warn("处理组合品库存时发生异常，商品id为：{}",commodityId,e);
        }
    }


    private void insertOrIgnoreUpdateTobCommodityStock(TobCommodityStockKafkaVo vo, Long commodityId) {
        //组合品下面有子品没有库存
        //非组合品的处理
        Example example = new Example(TobCommodityStock.class);
        example.createCriteria().andEqualTo("commodityId", commodityId)
                .andEqualTo("logisticsCenterId", 1);
        TobCommodityStock tobCommodityStock = tobCommodityStockMapper.selectOneByExample(example);

        if (tobCommodityStock == null) {
            // 新增
            TobCommodityStock insert = BeanCloneUtils.copyTo(vo, TobCommodityStock.class);
            insert.setCreateId(-1L);
            insert.setCreateTime(new Date());
            insert.setUpdateId(-1L);
            insert.setUpdateTime(new Date());

            // 限量的鲜达和通达的库存一致
            if(StockTypeEnum.LIMIT.getCode().equals(vo.getStockType())){
                insert.setTdaStockStatus(vo.getStockStatus());
            }else {
                if(BusinessTypeEnums.TD_SALE.getCode() == vo.getType()){
                    insert.setTdaStockStatus(vo.getStockStatus());

                    // 通达的没有库存，鲜达的一定没有库存(通达库存 = 鲜达库存 +通达共享库存)
                    if(YesOrNoEnums.NO.getCode().equals(vo.getStockStatus())){
                        insert.setStockStatus(YesOrNoEnums.NO.getCode());
                    }
                }else {
                    insert.setTdaStockStatus(YesOrNoEnums.YES.getCode().equals(vo.getStockStatus()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
                }
            }

            insert.setTdaStockStatus(insert.getTdaStockStatus() != null ? insert.getTdaStockStatus() : insert.getStockStatus());
            tobCommodityStockMapper.insert(insert);
        }
    }

    private void insertOrUpdateTobCommodityStock(TobCommodityStockKafkaVo vo, Long commodityId) {
        // 根据业务类型判断
        if (BusinessTypeEnums.B_COUNTRY.getCode().equals(vo.getType())) {
            // B端全国 库存的处理
            updateOrInsertStock(vo, commodityId, vo.getLogisticsCenterId());
        } else {
            // 非组合品的处理
            updateOrInsertStock(vo, commodityId, 1L);
        }
    }

    private void updateOrInsertStock(TobCommodityStockKafkaVo vo, Long commodityId, Long logisticsCenterId) {
        Example example = new Example(TobCommodityStock.class);
        example.createCriteria().andEqualTo("commodityId", commodityId)
                .andEqualTo("logisticsCenterId", logisticsCenterId);

        TobCommodityStock tobCommodityStock = tobCommodityStockMapper.selectOneByExample(example);

        if (tobCommodityStock != null) {
            Integer dbStockStatus = getStockStatus(vo, tobCommodityStock);

            // 如果当前消息的库存和数据库里面一致就不更新了
            if (Objects.equals(dbStockStatus, vo.getStockStatus())) {
                return;
            }

            // 更新操作
            updateStock(vo, tobCommodityStock, example);
        } else {
            // 新增操作
            insertStock(vo);
        }
    }

    private Integer getStockStatus(TobCommodityStockKafkaVo vo, TobCommodityStock tobCommodityStock) {
        if (BusinessTypeEnums.TD_SALE.getCode().equals(vo.getType())) {
            return tobCommodityStock.getTdaStockStatus();
        } else if (BusinessTypeEnums.B_COUNTRY.getCode().equals(vo.getType())) {
            // 根据业务类型判断
            return tobCommodityStock.getNationalStockStatus();
        } else {
            return tobCommodityStock.getStockStatus();
        }
    }

    private void updateStock(TobCommodityStockKafkaVo vo, TobCommodityStock tobCommodityStock, Example example) {
        TobCommodityStock update = BeanCloneUtils.copyTo(vo, TobCommodityStock.class);
        update.setUpdateTime(new Date());
        update.setUpdateId(-1L);
        update.setId(tobCommodityStock.getId());

        if (BusinessTypeEnums.B_COUNTRY.getCode().equals(vo.getType())) {
            // 根据业务类型判断
            update.setNationalStockStatus(vo.getStockStatus());
        } else {
            // 根据库存类型更新通达库存状态
            if (StockTypeEnum.LIMIT.getCode().equals(vo.getStockType())) {
                update.setTdaStockStatus(vo.getStockStatus());
            } else {
                if (BusinessTypeEnums.TD_SALE.getCode().equals(vo.getType())) {
                    update.setTdaStockStatus(vo.getStockStatus());

                    // 通达的没有库存，鲜达的一定没有库存(通达库存 = 鲜达库存 +通达共享库存)
                    if (YesOrNoEnums.NO.getCode().equals(vo.getStockStatus())) {
                        update.setStockStatus(YesOrNoEnums.NO.getCode());
                    } else {
                        update.setStockStatus(null);
                    }
                }
            }

            // 兼容没有通达库存的情况
            if (tobCommodityStock.getTdaStockStatus() == null && update.getTdaStockStatus() == null) {
                update.setTdaStockStatus(update.getStockStatus());
            }
        }

        int updateCount = tobCommodityStockMapper.updateByPrimaryKeySelective(update);
        QYAssert.isTrue(updateCount == 1, "修改失败。");
    }

    private void insertStock(TobCommodityStockKafkaVo vo) {
        TobCommodityStock insert = BeanCloneUtils.copyTo(vo, TobCommodityStock.class);
        insert.setCreateId(-1L);
        insert.setCreateTime(new Date());
        insert.setUpdateId(-1L);
        insert.setUpdateTime(new Date());
        insert.setLogisticsCenterId(vo.getLogisticsCenterId());

        if (BusinessTypeEnums.B_COUNTRY.getCode().equals(vo.getType())) {
            // 根据业务类型判断
            insert.setNationalStockStatus(vo.getStockStatus());
            insert.setStockStatus(0);
            insert.setTdaStockStatus(0);
        } else {
            // 设置库存状态
            if (StockTypeEnum.LIMIT.getCode().equals(vo.getStockType())) {
                insert.setTdaStockStatus(vo.getStockStatus());
            } else {
                if (BusinessTypeEnums.TD_SALE.getCode().equals(vo.getType())) {
                    insert.setTdaStockStatus(vo.getStockStatus());
                    if (YesOrNoEnums.NO.getCode().equals(vo.getStockStatus())) {
                        insert.setStockStatus(YesOrNoEnums.NO.getCode());
                    }
                } else {
                    insert.setTdaStockStatus(YesOrNoEnums.YES.getCode().equals(vo.getStockStatus()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
                }
            }
        }

        insert.setTdaStockStatus(insert.getTdaStockStatus() != null ? insert.getTdaStockStatus() : insert.getStockStatus());

        tobCommodityStockMapper.insert(insert);
    }

    private void processCommodityItemMasterStock(TobCommodityStockKafkaVo vo,
                                                 Long commodityId) {
        if (BusinessTypeEnums.B_COUNTRY.getCode().equals(vo.getType())) {
            // 根据业务类型判断，不考虑组合品
            return;
        }
        //查出包含子品的所有组合品id
        List<CommodityItemEntry> masterCommodityList =
                commodityItemMapper.findCommodityMasterListByCommodityItemId(commodityId);
        if(CollectionUtils.isEmpty(masterCommodityList))
            return;

        //依据子品有无库存更新组合品
        masterCommodityList.forEach(
                masterCommodity->{

                    // 避免2个子品同时发消息更新同一个主品
                    RLock lock = redissonClient.getLock("masterComm:order:" + masterCommodity.getCommodityId());
                    lock.lock(3L, TimeUnit.SECONDS);
                    try {
                        TobCommodityStockKafkaVo tmp = null;
                        Map<Long, BigDecimal> orderQuantityMap  = new HashMap<>();
                        Long masterCommodityId = masterCommodity.getCommodityId();
                        orderQuantityMap.put(masterCommodityId,new BigDecimal(1));
                        Map<Long, CommodityInventoryODTO> longCommodityInventoryODTOMap =
                                bStockService.getbStockMap(null, orderQuantityMap);

                        CommodityInventoryODTO commodityInventoryODTO = longCommodityInventoryODTOMap.get(masterCommodityId);
                        tmp = new TobCommodityStockKafkaVo();
                        tmp.setCommodityId(masterCommodityId);
                        tmp.setWarehouseId(commodityInventoryODTO.getWarehouseId());
                        tmp.setStockType(commodityInventoryODTO.getStockType());
                        tmp.setEffectType(commodityInventoryODTO.getEffectType());
                        tmp.setStockStatus(vo.getStockStatus());
                        tmp.setType(vo.getType());
                        insertOrUpdateTobCommodityStock(tmp,masterCommodity.getCommodityId());
                    }finally {
                        if(lock.isLocked() && lock.isHeldByCurrentThread()){
                            lock.unlock(); // 释放锁
                        }
                    }
                }
        );

    }

    /**
     *鲜达app详情页时候，刷新库存
     * @param idto
     * @return
     */
    @Deprecated
    public Boolean updateXdaCommodityStock(TobCommodityStockIDTO idto) {
        if(idto.getSoldOut() == null || idto.getCommodityId() == null || idto.getBusinessType() == null){
            return Boolean.FALSE;
        }

        Example example = new Example(TobCommodityStock.class);
        example.createCriteria().andEqualTo("commodityId", idto.getCommodityId());
        TobCommodityStock tobCommodityStock = tobCommodityStockMapper.selectOneByExample(example);

        if (tobCommodityStock != null) {
            Integer dbStockStatus = (BusinessTypeEnums.TD_SALE.getCode().equals(idto.getBusinessType()) ? tobCommodityStock.getTdaStockStatus() : tobCommodityStock.getStockStatus());
            // 库存不一致，则修改
            if(!dbStockStatus.equals(idto.getSoldOut())){
                TobCommodityStock update = BeanCloneUtils.copyTo(tobCommodityStock, TobCommodityStock.class);
                update.setUpdateTime(new Date());
                update.setUpdateId(-1L);

                if(BusinessTypeEnums.TD_SALE.getCode().equals(idto.getBusinessType())){
                    update.setTdaStockStatus(idto.getSoldOut());

                    // 通达的没有库存，鲜达的一定没有库存(通达库存 = 鲜达库存 +通达共享库存)
                    if(YesOrNoEnums.NO.getCode().equals(idto.getSoldOut())){
                        update.setStockStatus(YesOrNoEnums.NO.getCode());
                    }
                }else {
                    update.setStockStatus(idto.getSoldOut());
                }

                int updateCount = tobCommodityStockMapper.updateByPrimaryKeySelective(update);
                QYAssert.isTrue(updateCount == 1, "修改失败。");
            }
        }

        return Boolean.TRUE;
    }

}
