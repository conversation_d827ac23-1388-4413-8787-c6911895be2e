package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.box.utils.SpringUtil;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * 
 **/
@Entity
@Table(name = "t_order_list_gift")
@Data
public class OrderListGift extends BaseIDPO {
	
    private Long orderId;
    private Long commodityId;
    private BigDecimal commodityNum;
    private BigDecimal totalPrice;
    private BigDecimal commodityPrice;
    private Integer type;
    private String remark;
    /** 实发数量 **/
    private BigDecimal realQuantity;
    /** 实发总金额 **/
    private BigDecimal realTotalPrice;

    /** 赠送方案id **/
    private Long giftModelId;

    /** 配货或配比方案id **/
    private Long promotionId;

    /**原价**/
    private BigDecimal originalPrice;

    /**原金额(原价*数量)**/
    private BigDecimal originalTotalPrice;
    /**
     * 商品类型-1-非组合 2-组合  3-组合子品
     */
    private Integer combType = 1;
    /**
     * 属于组合商品id
     */
    private Long combCommodityId;

    /** 鲜达B端特价id */
    private Long pricePromotionId;

    /** 优惠券分摊金额 */
    private BigDecimal couponDiscountAmount;

    /** 优惠券ID */
    private Long couponId;

    /** 用户券ID */
    private Long couponUserId;

    public Long getPricePromotionId() {
        return pricePromotionId;
    }

    public void setPricePromotionId(Long pricePromotionId) {
        this.pricePromotionId = pricePromotionId;
    }

    @Transient
    private Boolean orderDone = false;
    @Transient
    private Boolean giftDone = false;

    @Transient
    /** 订单明细ID 或 退货单ID */
    private Long itemId;

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public BigDecimal getTotalPrice(){
        if(null == totalPrice){
            return null;
        }
        return totalPrice.setScale(2,BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal getCommodityPrice(){
        if(null == commodityPrice){
            return null;
        }
        return commodityPrice.setScale(2,BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal getRealTotalPrice(){
        if(null == realTotalPrice){
            return null;
        }
        return realTotalPrice.setScale(2,BigDecimal.ROUND_HALF_UP);
    }
    public static OrderListGift initForDo(Long orderId,Long commodityId,BigDecimal ordrNum,BigDecimal price,Integer type){
        OrderListGift gift = new OrderListGift();
        gift.setOrderId(orderId);
        gift.setCommodityId(commodityId);
        gift.setCommodityNum(ordrNum);
        gift.setCommodityPrice(price);
        gift.setTotalPrice(price.multiply(ordrNum));
        gift.setType(type);
        gift.setRemark(type==1?null:(type==2?"促销赠品":"促销配货"));
        return gift;
    }

    public static OrderList covert(OrderListGift orderListGift) {
        OrderList result = new OrderList();
        SpringUtil.copyProperties(orderListGift, result);
        return result;
    }
}