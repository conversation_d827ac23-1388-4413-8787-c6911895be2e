package com.pinshang.qingyun.order.service.xda;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.OrderStatusEnums;
import com.pinshang.qingyun.base.enums.SubOrderStatusEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.base.spring.MockMultipartFile;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.order.config.FileConfigProperties;
import com.pinshang.qingyun.order.dto.file.FileODTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartCommodityODTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartODTO;
import com.pinshang.qingyun.order.dto.xda.*;
import com.pinshang.qingyun.order.enums.OrderModeType;
import com.pinshang.qingyun.order.enums.OrderPrintTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.enums.XdaPayTypeEnum;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.common.CompanyMapper;
import com.pinshang.qingyun.order.mapper.common.EmployeeUserMapper;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderItemEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderRealDeliveryFinishEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreDurationEntry;
import com.pinshang.qingyun.order.model.common.Company;
import com.pinshang.qingyun.order.model.common.EmployeeUser;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.pdf.OrderPdfA4Creator;
import com.pinshang.qingyun.order.service.OrderAsyncKafkaService;
import com.pinshang.qingyun.order.service.OrderHistoryService;
import com.pinshang.qingyun.order.service.OrderService;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import com.pinshang.qingyun.order.service.xda.util.XdaShoppingCartToolUtil;
import com.pinshang.qingyun.order.util.UploadFileUtil;
import com.pinshang.qingyun.order.vo.store.StoreCompanyVo;
import com.pinshang.qingyun.storage.dto.CommodityDefaultDcODto;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import com.pinshang.qingyun.store.dto.xda.QueryXdaUserAccountDTO;
import com.pinshang.qingyun.store.dto.xda.XdaPayPasswordDTO;
import com.pinshang.qingyun.store.dto.xda.XdaPayResultDTO;
import com.pinshang.qingyun.store.dto.xda.XdaUserAccountDTO;
import com.pinshang.qingyun.store.service.StoreCompanyClient;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.store.service.XdaStoreUserClient;
import com.pinshang.qingyun.tms.dto.logisticscenterbatch.QueryCarrierNameAndThirdPartyCodeODTO;
import com.pinshang.qingyun.tms.dto.logisticscenterbatch.QueryCarrierNameAndThirdPartyCodeSearchIDTO;
import com.pinshang.qingyun.tms.service.LogisticsCenterBatchClient;
import com.pinshang.qingyun.upload.dto.odto.FileUploadRestSingleODTO;
import com.pinshang.qingyun.upload.dto.odto.FileUploadResultODTO;
import com.pinshang.qingyun.upload.service.FileUploadClientFactory;
import com.pinshang.qingyun.upload.service.XsFileUploadClient;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectXdaCommodityInfoListIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityInfoODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;



@Service
@Slf4j
public class XdaOrderService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private CompanyMapper companyMapper;
    @Autowired
    private OrderFileMapper orderFileMapper;
    @Autowired
    private OrderMirrorMapper orderMirrorMapper;
    @Autowired
    private EmployeeUserMapper employeeUserMapper;
    @Autowired
    private FileConfigProperties fileConfigProperties;
    @Autowired
    private OrderListGiftMapper orderListGiftMapper;
    @Autowired
    private SubOrderMapper subOrderMapper;
    @Autowired
    private StoreMapper storeMapper;
	@Autowired
    private FileUploadClientFactory fileUploadClientFactory;

	@Autowired
	private LogisticsCenterBatchClient logisticsCenterBatchClient;
	/**
	 * 定时任务 将昨天配送中的数据改为配送完成
	 * @param dateTime
	 * @return
	 */
    public Integer autoCompleteXdaOrder(String dateTime){
        if(StringUtil.isNullOrEmpty(dateTime)){
            dateTime = DateUtil.get4yMd(DateUtil.addDay(-1));
        }
       /* Example example = new Example(Order.class);
        example.createCriteria().andEqualTo("orderStatus", 0)
                .andEqualTo("processStatus", 15).
                andEqualTo("orderTime", dateTime);
        Order order = Order.buildCompleteOrder(19, -1L);*/
        return orderMapper.updateCompleteOrderByParams(dateTime);
    }

    /**
     * 查询  订单PDF文件
     * 
     * @param orderId
     * @return
     */
	@Transactional(rollbackFor = Exception.class)
    public FileODTO queryOrderPdfFile(Long orderId) {
    	QYAssert.isTrue(null != orderId, "orderId is not null !");
    	OrderFile orderFile = orderFileMapper.selectOne(new OrderFile(orderId));
    	if (null != orderFile) {
    		return new FileODTO(fileConfigProperties.getVisitUrl() + orderFile.getFileUrl(), orderFile.getFileSize());
    	}
    	
    	// 1、订单信息
    	Order order = orderMapper.selectByPrimaryKey(orderId);
    	QYAssert.isTrue(null != order, "该订单不存在!");
    	QYAssert.isTrue(OrderTypeEnum.XDA_APP_ORDER.getCode().equals(order.getOrderType()), "该订单类型有误!");
        Boolean delivery = Integer.valueOf(XdaOrderProcessStatusEunm.DELIVERYING.getCode()).equals(order.getProcessStatus())
                 || Integer.valueOf(XdaOrderProcessStatusEunm.WAITING_DELIVERY.getCode()).equals(order.getProcessStatus())
                 ||  Integer.valueOf(XdaOrderProcessStatusEunm.DELIVERY_COMPLETED.getCode()).equals(order.getProcessStatus());
    	QYAssert.isTrue(delivery, "该订单尚未发货!");
        /***
         * APP 我的订单列表实发金额显示问题：背景由于同一个订单多个仓库发货完成时间点不同，导致实发金额会变化 希望能整单完成拣货（出货）才能计算实发金额，才能生成送货单
         * 查询订单拆单数量（不包括直送类型：t_sub_order表logistics_model=0，直送类型大仓不发送发货消息）及实际大仓发货数
         * 实际大仓发货数=根据t_sub_order表字段writeback_real_qty_flag该字段等于1 时从而知道子订单在大仓发货并发送发货消息：topic=PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC 来判断拆分的子单是否都发货
         */
        List<OrderRealDeliveryFinishEntry> deliveryFinishEntryList=subOrderMapper.findOrderRealDeliveryFinishList(Arrays.asList(orderId));
        if(SpringUtil.isNotEmpty(deliveryFinishEntryList)) {
            OrderRealDeliveryFinishEntry entry = deliveryFinishEntryList.get(0);
            /*** 订单拆单数量不等于大仓实际发货数 不能生成送货单*/
            QYAssert.isTrue(entry.getSubNum().compareTo(entry.getRealFinishNum()) == 0, "大仓发货流程尚未完成，暂时无法生成送货单。如有疑问，请联系您的专属督导。");
        }
    	
    	XdaOrderInfoODTO deliveryOrderInfo = new XdaOrderInfoODTO(order);
    	Long companyId = order.getCompanyId();
    	if (null != companyId) {
    		Company company = companyMapper.selectByPrimaryKey(companyId);
    		if (null != company) {
    			deliveryOrderInfo.setCompanyName(company.getCompanyName());
    		}

    	}

		QueryCarrierNameAndThirdPartyCodeSearchIDTO queryCarrierNameAndThirdPartyCodeSearchIDTO =
				new QueryCarrierNameAndThirdPartyCodeSearchIDTO(order.getId());
		QueryCarrierNameAndThirdPartyCodeODTO queryCarrierNameAndThirdPartyCodeODTO = logisticsCenterBatchClient.queryCarrierNameAndThirdPartyCodeByOrderId(queryCarrierNameAndThirdPartyCodeSearchIDTO);
		if(null != queryCarrierNameAndThirdPartyCodeODTO){
			deliveryOrderInfo.setCarrierName(queryCarrierNameAndThirdPartyCodeODTO.getCarrierName());
			deliveryOrderInfo.setThirdPartyCode(queryCarrierNameAndThirdPartyCodeODTO.getThirdPartyCode());
		}
		// 2、客户信息
    	Long storeId = order.getStoreId();
    	Store store = storeMapper.selectByPrimaryKey(storeId);
    	deliveryOrderInfo.setStoreInfo(store);
    	// 客户信息 - 线路组、操作员（对于鲜达来说肯定是客户）
    	deliveryOrderInfo.setStoreLineGroupName(storeMapper.selectStoreLineGroupName(storeId));
    	deliveryOrderInfo.setCreateName(store.getStoreName());
    	
    	// 3、订单镜像信息
    	OrderMirror orderMirror = new OrderMirror();
    	orderMirror.setOrderId(orderId);
    	orderMirror = orderMirrorMapper.selectOne(orderMirror);
    	deliveryOrderInfo.setOrderMirrorInfo(orderMirror);
    	// 订单镜像 - 设置职员信息
    	if (null != orderMirror) {
    		List<Long> employeeIdList = new ArrayList<>();
    		if (null != orderMirror.getDeliveryManId()) {
    			employeeIdList.add(orderMirror.getDeliveryManId());
    		}
    		if (null != orderMirror.getRegionalManagerId()) {
    			employeeIdList.add(orderMirror.getRegionalManagerId());
    		}
    		if (null != orderMirror.getSupervisionId()) {
    			employeeIdList.add(orderMirror.getSupervisionId());
    		}
    		if (SpringUtil.isNotEmpty(employeeIdList)) {
    			Example example = new Example(EmployeeUser.class);
    			example.createCriteria().andIn("employeeId", employeeIdList);
    	        example.selectProperties("employeeId", /*"employeeCode", "employeeName", */"employeePhone");
    			List<EmployeeUser> employeeUserList = employeeUserMapper.selectByExample(example);
    			if (SpringUtil.isNotEmpty(employeeUserList)) {
    				Map<Long, String> employeePhoneMap = employeeUserList.stream().filter(o -> {return !StringUtil.isNullOrEmpty(o.getEmployeePhone());}).collect(Collectors.toMap(EmployeeUser::getEmployeeId, EmployeeUser::getEmployeePhone));
    				deliveryOrderInfo.setRegionalManagerPhone(employeePhoneMap.get(deliveryOrderInfo.getRegionalManagerId()));
    				deliveryOrderInfo.setSupervisionPhone(employeePhoneMap.get(deliveryOrderInfo.getSupervisionId()));
    				deliveryOrderInfo.setDeliveryManPhone(employeePhoneMap.get(deliveryOrderInfo.getDeliveryManId()));
    			}
    		}
    	}
    	
    	// 4、订单项信息
    	deliveryOrderInfo.setOrderItemList(orderListGiftMapper.selectOrderItemList(orderId));
    	
    	// 5、生成文件 & 上传文件 & 删除本地文件
    	String fullFileName = OrderPdfA4Creator.createPdfFile(deliveryOrderInfo, OrderPdfA4Creator.DEFAULT_PAGE_SIZE);
//    	FileUploadResultVo fileUploadResult = null;
//    	try {
//    		fileUploadResult = UploadFileUtil.postFile(fileConfigProperties.getServerUrl(), fullFileName);
//		} catch (IOException e) {
//			log.error("\n订单文件上传到资源库异常", e);
//			e.printStackTrace();
//		}
//    	UploadFileUtil.deleteLocalFile(fullFileName);
//    	QYAssert.isTrue(null != fileUploadResult, "该订单文件上传到资源库失败!");
    	
    	FileUploadRestSingleODTO fileUploadRestSingleODTO = null;
    	FileUploadResultODTO data = null;
    	try {
    		
    		File file = new File(fullFileName);
            InputStream inputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), MediaType.MULTIPART_FORM_DATA_VALUE, inputStream);
            XsFileUploadClient cli = fileUploadClientFactory.getXsClient();
            fileUploadRestSingleODTO = cli.restUpload(multipartFile, "XDA_DELIVERY_ORDER_FILE");
            log.info("xsFileUploadClient上传pdf-result ： {}", fileUploadRestSingleODTO);
            if (fileUploadRestSingleODTO == null) {
                QYAssert.isFalse("上传pdf异常!");
            }
            data = fileUploadRestSingleODTO.getData();
            if (data == null) {
                QYAssert.isFalse("上传pdf异常!");
            }
		} catch (Exception e) {
			log.error("\n订单文件上传到资源库异常", e);
			QYAssert.isFalse("上传pdf异常!");
		} finally {
			UploadFileUtil.deleteLocalFile(fullFileName);
		}
    	
    	// 6、记录本地记录
    	orderFile = OrderFile.forInsert(orderId, data.getRelativeUrl(), data.getPicSpace());
		orderFileMapper.insert(orderFile);
		return new FileODTO(fileConfigProperties.getVisitUrl() + orderFile.getFileUrl(), orderFile.getFileSize());
    }

}
