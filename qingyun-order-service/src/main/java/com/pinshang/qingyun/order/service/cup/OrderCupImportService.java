package com.pinshang.qingyun.order.service.cup;

import com.google.common.collect.Maps;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.commodity.CommodityPackageTypeEnums;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.dto.cup.*;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ProductFrozenMultipleEnum;
import com.pinshang.qingyun.order.mapper.CommodityFreezeGroupMapper;
import com.pinshang.qingyun.order.mapper.cup.OrderCupReportMapper;
import com.pinshang.qingyun.order.mapper.entry.store.StoreEntry;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.service.CommodityService;
import com.pinshang.qingyun.order.service.SplitOrderSendKfkService;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.tms.OrderTmsService;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import com.pinshang.qingyun.order.util.NumberUtils;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.util.TimeUtil;
import com.pinshang.qingyun.order.vo.cup.CommodityListQueryReqVO;
import com.pinshang.qingyun.order.vo.order.OrderDto;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceODTO;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceSearchIDTO;
import com.pinshang.qingyun.price.service.StorePromotionClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * @Author: sk
 * @Date: 2024/7/16
 */
@Slf4j
@Service
public class OrderCupImportService {

    @Autowired
    private StoreService storeService;
    @Autowired
    private OrderCupReportMapper orderCupReportMapper;
    @Autowired
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;
    @Autowired
    private StorePromotionClient storePromotionClient;
    @Autowired
    private OrderCupService orderCupService;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private SplitOrderSendKfkService splitOrderSendKfkService;
    @Autowired
    private OrderTmsService orderTmsService;
    @Autowired
    private TdaOrderService tdaOrderService;
    @Autowired
    private DictionaryClient dictionaryClient;
    private static final int MAX_IMPORT_LIMIT = 600;
    private static final int MAX_COMMODITY_LIMIT = 100;

    /**
     * addCommodityType 1 按条码导入 2 按商品编码导入
     * @param workbook
     * @return
     */
    public JsonMsgBean importOrder(Workbook workbook, Integer addCommodityType) {

        JsonMsgBean json = new JsonMsgBean(true);

        if(null == workbook){
            QYAssert.isFalse("工作簿不能为空");
        }

        Sheet sheet = workbook.getSheetAt(0);
        // 校验订单导入模板
        validateImportTemplate(addCommodityType, sheet);

        List<Commodity> commodityImportList = new ArrayList<>();

        // 获取excel数据
        if (setImportData(addCommodityType, json, sheet, commodityImportList)) return json;

        QYAssert.isTrue(commodityImportList.size() <= MAX_IMPORT_LIMIT, "导入数量不能超过" + MAX_IMPORT_LIMIT);

        //校验导入文件中是否存在商品重复
        String repeatErrMsg = checkRepeatImportEle(commodityImportList);

        if(StringUtils.isNotBlank(repeatErrMsg)){
            json.setSuccess(Boolean.FALSE);
            json.setErrMsgList(Arrays.asList(repeatErrMsg));
            return json;
        }

        // 判断 客户编码真实存在
        // 客户关系启用状态
        // 不支持预付客户导入订单，即客户必须是非预付的
        // 业务类型为通达，不允许导入
        // 内部客户不允许导入
        Map<String, StoreEntry> storeMap = Maps.newHashMap();
        String checkStoreValidErrMsg = checkStoreValid(commodityImportList,storeMap);
        if(StringUtils.isNotBlank(checkStoreValidErrMsg)){
            json.setSuccess(Boolean.FALSE);
            json.setErrMsgList(Arrays.asList(checkStoreValidErrMsg));
            return json;
        }


        //跟据导入的文件获取客户商品价格方案数据
        List<Long> storeIdList = storeMap.values().stream().map(StoreEntry::getId).distinct().collect(toList());
        List<String> commodityCodeList = commodityImportList.stream().map(Commodity::getCommodityCode).distinct().collect(toList());
        List<Commodity> storeProductModelList = orderCupReportMapper.findStoreCommodityByStoreIdAndCommodityCode(storeIdList, commodityCodeList, addCommodityType);
        Map<String, Commodity> storeProductModelMap = storeProductModelList.stream().collect(
                toMap(commodity->generateDbStoreCodeAndCommodityCodeOrBarCodeKey(commodity,addCommodityType),Function.identity()));


        //校验客户商品在价格方案中是否存在，且不能为组合品
        String checkStoreCommodityValidErrMsg = checkStoreCommodityValid(commodityImportList,storeProductModelMap);
        if(StringUtils.isNotBlank(checkStoreCommodityValidErrMsg)){
            json.setSuccess(Boolean.FALSE);
            json.setErrMsgList(Arrays.asList(checkStoreCommodityValidErrMsg));
            return json;
        }

        setStoreCommodityInfo(commodityImportList,storeProductModelMap);

        //B端全国地址校验
        batchValidLogisticsCarrier(new ArrayList<>(storeMap.values()));

        //按照送货日期+客户编码 构建客户价格方案商品map（导入的商品查询客户价格方案），并设置特价查询参数
        Map<String, Map<String,Commodity>> storeCommodityMap = buildStoreCommodityMap(commodityImportList);

        BigDecimal totalAmount = BigDecimal.ZERO;
        StringBuffer sb = new StringBuffer();
        List<Commodity> returnList = new ArrayList<>();
        for (Commodity commodity:commodityImportList){
            String storeCode = commodity.getStoreCode();
            String commodityCode = commodity.getCommodityCode();
            StoreEntry storeEntry = storeMap.get(storeCode);
            Map<String, Commodity> stringCommodityMap = storeCommodityMap.get(generateOrderTimeAndStoreIdKey(commodity));
            Commodity dbCommodity = stringCommodityMap.get(commodityCode);
            Commodity returnCommodity = new Commodity();
            BeanUtils.copyProperties(dbCommodity, returnCommodity);
            returnCommodity.setStoreId(storeEntry.getId() + "");
            returnCommodity.setStoreName(storeMap.get(storeCode).getStoreName());
            returnCommodity.setCommodityIds(dbCommodity.getId() + "");
            returnCommodity.setProductNumber(commodity.getProductNumber());
            //returnCommodity.setAmount(returnCommodity.getProductNumber().multiply(returnCommodity.getCommodityPrice()));
            //如果是特价商品，则总金额取原金额*数量，如果不是特价商品，则按原逻辑处理
            if(YesOrNoEnums.YES.getCode().equals(returnCommodity.getPromotionFlag())){
                returnCommodity.setAmount(returnCommodity.getProductNumber().multiply(returnCommodity.getOriginalCommodityPrice()).setScale(2,BigDecimal.ROUND_HALF_UP));
            }else{
                returnCommodity.setAmount(returnCommodity.getProductNumber().multiply(returnCommodity.getCommodityPrice()).setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            returnList.add(returnCommodity);
            totalAmount = totalAmount.add(returnCommodity.getAmount()).setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        /*

        if(Objects.equals(storeEntry.getBusinessType(),BusinessTypeEnums.PLAN_SALE.getCode())){
                    json.setSuccess(false);
                    json.setErrMsgList(Arrays.asList("第"+(index+1)+"行，客户类型为计划销售，不允许导入"));
                    return json;
                }

        * */
        if(returnList.size() < commodityImportList.size()){
            json.setSuccess(false);
            json.setErrMsgList(Arrays.asList(sb.toString().substring(0, sb.toString().length() - 1)));
            return json;
        }

        /*** 验证凑整商品 */
        JsonMsgBean jsonMsgBean = checkRoundedGoodsAndGetJsonBean(returnList);
        if(jsonMsgBean != null){
            return jsonMsgBean;
        }

        json.setData(returnList);
        json.setMessage(commodityImportList.stream().map(Commodity::getStoreCode).distinct().count() + "," + storeCommodityMap.size() + ","+ totalAmount);
        return json;
    }

    private void setStoreCommodityInfo(List<Commodity> commodityImportList, Map<String, Commodity> storeProductModelMap) {

        for (Commodity commodity:commodityImportList){
            Commodity dbCommodity = storeProductModelMap.get(generateImportStoreCodeAndCommodityCodeKey(commodity));
            commodity.setCommodityPrice(dbCommodity.getCommodityPrice());
            commodity.setOriginalCommodityPrice(dbCommodity.getCommodityPrice());
            commodity.setCommodityName(dbCommodity.getCommodityName());
            commodity.setCommoditySpec(dbCommodity.getCommoditySpec());
            commodity.setIsRoundedGoods(dbCommodity.getIsRoundedGoods());
            commodity.setCommodityUnitName(dbCommodity.getCommodityUnitName());
            commodity.setId(dbCommodity.getId());
        }
    }

    private String checkStoreCommodityValid(List<Commodity> commodityImportList,Map<String, Commodity> storeProductModelMap) {

        StringBuilder sb = new StringBuilder();
        for (Commodity commodity:commodityImportList){
            Commodity dbCommodity = storeProductModelMap.get(generateImportStoreCodeAndCommodityCodeKey(commodity));
            if(null != dbCommodity){
                if(dbCommodity.getProductType()==2){
                    sb.append("商品"+commodity.getCommodityCode()+" 不允许为组合品;");
                }
            }else {
                sb.append("客户 " + commodity.getStoreCode() + " 商品 " + commodity.getCommodityCode() + "不存在;");
            }
        }

        return sb.toString();
    }

    private String generateDbStoreCodeAndCommodityCodeOrBarCodeKey(Commodity commodity,Integer addCommodityType) {
        if(addCommodityType == 1){
            return commodity.getStoreCode() + "," + commodity.getBarCode();
        }else{
            return commodity.getStoreCode() + "," + commodity.getCommodityCode();
        }
    }

    private String generateImportStoreCodeAndCommodityCodeKey(Commodity commodity) {
        return commodity.getStoreCode() + "," + commodity.getCommodityCode();
    }

    private String generateStoreIdAndCommodityCodeKey(Commodity commodity) {
        return commodity.getStoreId() + "," + commodity.getCommodityCode();
    }

    private void processStorePromotionPrice(List<StorePromotionCommodityPriceSearchIDTO> searchPriceIDTOList,Map<String,Map<String,Commodity>> storeCommodityMap) {
        // 获取特价信息，处理特价
        Map<String, Map<Long, StorePromotionCommodityPriceODTO>> priceMap = storePromotionClient.batchGetStorePromotionCommodityMapByParams(searchPriceIDTOList);
        if(priceMap != null && priceMap.size() > 0){
            storeCommodityMap.forEach((key, value) -> {
                Map<String, Commodity> commodityMap = value;
                Map<Long, StorePromotionCommodityPriceODTO> longStorePromotionCommodityPriceODTOMap = priceMap.get(key);
                if (SpringUtil.isNotEmpty(longStorePromotionCommodityPriceODTOMap)) {
                    longStorePromotionCommodityPriceODTOMap.values().forEach(
                            storePromotionCommodityPriceODTO -> {
                                commodityMap.values().forEach(
                                        commodity -> {
                                            if (commodity.getId().equals(storePromotionCommodityPriceODTO.getCommodityId())
                                                    &&storePromotionCommodityPriceODTO.getPrice().compareTo(commodity.getCommodityPrice()) < 0) {
                                                commodity.setCommodityPrice(storePromotionCommodityPriceODTO.getPrice());
                                                commodity.setLimitNumber(storePromotionCommodityPriceODTO.getLimitNumber());
                                                commodity.setPromotionFlag(YesOrNoEnums.YES.getCode());
                                            }
                                        }
                                );
                            });

                }
            });
        }
    }

    private  Map<String, Map<String,Commodity>> buildStoreCommodityMap(List<Commodity> commodityImportList) {

        // 根据客户编码、送货日期 进行分组
        Map<String, Map<String,Commodity>> storeCommodityMap = commodityImportList.stream()
                .collect(Collectors.groupingBy(this::generateOrderTimeAndStoreIdKey,Collectors.toMap(Commodity::getCommodityCode,Function.identity())));

        List<StorePromotionCommodityPriceSearchIDTO> searchPriceIDTOList = Lists.newArrayList();
        storeCommodityMap.forEach((key,value)->{

            QYAssert.isTrue(value.size() <= MAX_COMMODITY_LIMIT, "同客户同送货日期不可导入超过" + MAX_COMMODITY_LIMIT + "个商品");
            String [] str = key.split(",");
            String storeIdStr = str[0];
            String orderTime = str[1];

            StorePromotionCommodityPriceSearchIDTO searchIDTO = new StorePromotionCommodityPriceSearchIDTO();
            searchIDTO.setStoreId(Long.valueOf(storeIdStr));
            searchIDTO.setOrderTime(DateUtil.parseDate(orderTime, "yyyy-MM-dd"));
            searchIDTO.setCommodityIdList(value.values().stream().map(Commodity::getId).collect(toList()));
            searchIDTO.setNeedAvailableLimit(0);
            searchPriceIDTOList.add(searchIDTO);
        });

        //设置特价信息
        processStorePromotionPrice(searchPriceIDTOList,storeCommodityMap);
        return storeCommodityMap;

    }

    private String checkStoreValid(List<Commodity> commodityImportList,Map<String, StoreEntry> storeMap) {

        List<String> storeCodeList = commodityImportList.stream().map(item -> item.getStoreCode()).distinct().collect(Collectors.toList());
        List<StoreEntry> storeList = null;
        try {
            storeList = storeService.queryStoreByStoreCodeList(storeCodeList);
        }catch (Exception e){
            return "客户编码格式不正确";
        }
        if(CollectionUtils.isEmpty(storeList)){
            return "未匹配到有效客户";
        }

        for (StoreEntry storeEntry : storeList) {
            storeMap.put(storeEntry.getStoreCode(),storeEntry);
        }

        for (Commodity commodity : commodityImportList) {
            String storeCode = commodity.getStoreCode();
            StoreEntry storeEntry = storeMap.get(storeCode);

            if(storeEntry == null){
                return "客户编码" + storeCode + "不存在";
            }else {
                if(!storeEntry.getStoreStatus().equals(1)){
                    return "客户编码" + storeCode + "非启用状态";
                }

                if(!"0".equals(storeEntry.getCollectStatus() + "")){
                    return "客户编码" + storeCode + "必须是非预付的";
                }

                if(DeliveryOrderTypeEnums.TD_SALE.getCode().equals(storeEntry.getBusinessType())){
                    return "客户编码" + storeCode + "业务类型为通达，不允许导入";
                }

                if("1".equals(storeEntry.getStoreType())){
                    return storeEntry.getStoreName()  +  "是内部客户，请导入外部客户！";
                }
            }
            commodity.setStoreId(String.valueOf(storeEntry.getId()));
        }

        return null;

    }

    private String checkRepeatImportEle(List<Commodity> commodityImportList) {
        //获取重复项 下单时间+客户号+商品编码
        List<String> distinctList = commodityImportList.stream().collect(Collectors.groupingBy(this::generateOrderTimeAndStoreCodeAndCommodityCodeKey,
                        Collectors.counting())).entrySet().stream().filter(e -> e.getValue() > 1)
                .map(Map.Entry::getKey).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(distinctList)){
            StringBuilder sb = new StringBuilder();
            sb.append("第");
            distinctList.forEach(item1->{
                int i = 0;
                for (Commodity commodity : commodityImportList) {
                    i++;
                    if(item1.equals(generateOrderTimeAndStoreCodeAndCommodityCodeKey(commodity))){
                        sb.append((i+1) + "、");
                    }
                }
            });
            sb.deleteCharAt(sb.length()-1);
            sb.append("行重复;");
            return sb.toString();
        }
        return null;
    }

    /**
     * 生成唯一key
     * @param commodity
     * @return
     */
    private String generateOrderTimeAndStoreCodeAndCommodityCodeKey(Commodity commodity){
        return commodity.getOrderTime() + "," + commodity.getStoreCode() + "," +commodity.getCommodityCode();
    }


    private String generateOrderTimeAndStoreIdKey(Commodity commodity){
        return commodity.getStoreId() + "," +  commodity.getOrderTime();
    }

    private void batchValidLogisticsCarrier(List<StoreEntry> storeList) {
        List<Long> bCountryStoreIdList = storeList.stream().filter(
                store -> Objects.equals(store.getBusinessType(), BusinessTypeEnums.B_COUNTRY.getCode())
        ).map(StoreEntry::getId).collect(toList());
        if(CollectionUtils.isNotEmpty(bCountryStoreIdList)){
            bCountryStoreIdList.forEach(orderTmsService::bCountryCheckTransportFlow);
        }
    }

    private JsonMsgBean checkRoundedGoodsAndGetJsonBean(List<Commodity> commodityList) {

        Map<String, List<Commodity>> map = commodityList.stream().collect(Collectors.groupingBy(k -> k.getStoreCode() + "," + k.getOrderTime()));
        for (Map.Entry<String, List<Commodity>> m : map.entrySet()) {
            List<Commodity> storeOrderTimeCommodityList = m.getValue();
            List<Long> commodityIds = storeOrderTimeCommodityList.stream().map(c -> Long.valueOf(c.getCommodityIds())).distinct().collect(toList());
            List<Long> commodityFreezeGroupList = commodityFreezeGroupMapper.selectOldCommodity(commodityIds);
            if (SpringUtil.isEmpty(commodityFreezeGroupList)) {
                continue;
            }
            boolean flag = false;
            BigDecimal roundedGoodsNumber = BigDecimal.ZERO;
            for (Commodity dto : storeOrderTimeCommodityList) {
                if (commodityFreezeGroupList.contains(Long.valueOf(dto.getCommodityIds()))) {
                    flag = true;
                    roundedGoodsNumber = roundedGoodsNumber.add(dto.getProductNumber());
                }
            }
            if (flag) {
                if (roundedGoodsNumber.compareTo(BigDecimal.ZERO) == 0) {
                    return new JsonMsgBean(false, "客户编码" + m.getKey().split(",")[0] + "；下单日期"+ m.getKey().split(",")[1]+"：凑整商品必须是" + ProductFrozenMultipleEnum.PRODUCT_FROZEN_MULTIPLE.getFrozenMultiple() + "的倍数,当前数量是" + roundedGoodsNumber);
                }
                if (roundedGoodsNumber.remainder(BigDecimal.valueOf(ProductFrozenMultipleEnum.PRODUCT_FROZEN_MULTIPLE.getFrozenMultiple())).compareTo(BigDecimal.ZERO) != 0) {
                    return new JsonMsgBean(false, "客户编码" + m.getKey().split(",")[0] + "；下单日期"+ m.getKey().split(",")[1]+ "：凑整商品必须是" + ProductFrozenMultipleEnum.PRODUCT_FROZEN_MULTIPLE.getFrozenMultiple() + "的倍数,当前数量是" + roundedGoodsNumber);
                }
            }
        }
        return null;
    }

    /**
     * 获取excel数据
     * @return
     */
    private boolean setImportData(Integer addCommodityType, JsonMsgBean json, Sheet sheet, List<Commodity> commodityReturnList) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 校验非空
        String comm = addCommodityType == 1 ? "商品条码":"商品编码";

        for(int i = 1; i <= sheet.getLastRowNum(); i ++){
            Commodity commodity = new Commodity();
            Row row = sheet.getRow(i);
            if(row == null
                    || row.getCell(0) == null || row.getCell(0).equals("")
                    || row.getCell(1) == null || row.getCell(1).equals("")
                    || row.getCell(2) == null || row.getCell(2).equals("")
                    || row.getCell(3) == null || row.getCell(3).equals("")
                    || (row.getCell(0).getCellType() == CellType.STRING && (row.getCell(0).getStringCellValue() == null || row.getCell(0).getStringCellValue().equals("")))
                    || (row.getCell(1).getCellType() == CellType.STRING && (row.getCell(1).getStringCellValue() == null || row.getCell(1).getStringCellValue().equals("")))
                    || (row.getCell(2).getCellType() == CellType.STRING &&  (row.getCell(2).getStringCellValue() == null || row.getCell(2).getStringCellValue().equals("")))
                    || (row.getCell(3).getCellType() == CellType.STRING && (row.getCell(3).getStringCellValue() == null || row.getCell(3).getStringCellValue().equals("")))){
                json.setSuccess(false);
                json.setErrMsgList(Arrays.asList("第"+(i+1)+"行送货日期、客户编码、"+ comm + "或数量不能为空"));
                return true;
            }

            row.cellIterator().forEachRemaining(c->c.setCellType(CellType.STRING));

            // 送货日期
            String orderTime = row.getCell(0).getStringCellValue().trim();
            Date date = null;
            try {
                date = sdf.parse(orderTime);
            } catch (Exception e) {
                json.setSuccess(false);
                json.setErrMsgList(Arrays.asList("第"+(i+1)+"行送货日期格式不正确 yyyy-MM-dd"));
                return true;
            }
            // 输入 2021-7-12
            if(orderTime.length() != 10){
                json.setSuccess(false);
                json.setErrMsgList(Arrays.asList("第"+(i+1)+"行送货日期格式不正确 yyyy-MM-dd"));
                return true;
            }
            if(!TimeUtil.isValidDate(orderTime)){
                json.setSuccess(false);
                json.setErrMsgList(Arrays.asList("第"+(i+1)+"行送货日期无效"));
                return true;
            }
            if(date.getTime() <= TimeUtil.getNowDate().getTime()){
                json.setSuccess(false);
                json.setErrMsgList(Arrays.asList("第"+(i+1)+"行送货日期必须大于今日"));
                return true;
            }

            // 客户编码
            String storeCode = row.getCell(1).getStringCellValue().trim();
            // 商品编码/条码
            String commodityCode = row.getCell(2).getStringCellValue().trim();

            // 数量
            String quantity = row.getCell(3).getStringCellValue().trim();
            if(!NumberUtils.isNumber(quantity)){
                json.setSuccess(false);
                json.setErrMsgList(Arrays.asList("第"+(i+1)+"行订货数量格式不正确,必须大于0并且长度最多15位，允许2位小数"));
                return true;
            }
            commodity.setOrderTime(orderTime.trim());
            commodity.setStoreCode(storeCode.trim());
            commodity.setCommodityCode(commodityCode.trim());
            commodity.setProductNumber(new BigDecimal(quantity.trim()));
            commodityReturnList.add(commodity);
        }

        // 校验整包必须是整数
        Set<String> commodityCodeList = commodityReturnList.stream().map(item -> item.getCommodityCode()).collect(Collectors.toSet());
        List<Commodity> commodityList = commodityService.findCommodityByCodeList(new ArrayList<>(commodityCodeList), addCommodityType);
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "所有商品系统不存在！");
        Map<String, Commodity> commMap = new HashMap<>();
        // addCommodityType 1:商品条码  2:商品编码
        if(addCommodityType.equals(1)){
            commMap = commodityList.stream().collect(Collectors.toMap(Commodity::getBarCode, Function.identity(), (key1, key2) -> key2));
        }else {
            commMap = commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityCode, Function.identity(), (key1, key2) -> key2));
        }
        for(Commodity item : commodityReturnList) {
            QYAssert.isTrue(commMap.containsKey(item.getCommodityCode()), comm + item.getCommodityCode() + " 系统不存在！");
            Commodity commodity = commMap.get(item.getCommodityCode());

            // 整包非称重只能输入整数
            if(CommodityPackageTypeEnums.整包.getCode().equals(Long.valueOf(commodity.getCommodityPackageId() + ""))
               &&  YesOrNoEnums.NO.getCode().equals(commodity.getIsWeight())) {
                if(!NumberUtils.isInteger(item.getProductNumber() + "")) {
                    //QYAssert.isFalse("商品编码" + item.getCommodityCode() + " 订货数量格式不正确,必须大于0并且长度最多15位的整数");
                    json.setSuccess(false);
                    json.setErrMsgList(Arrays.asList(comm + item.getCommodityCode() + " 订货数量格式不正确,必须大于0并且长度最多15位的整数"));
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 校验订单导入模板
     * @param addCommodityType
     * @param sheet
     * @return
     */
    private void validateImportTemplate(Integer addCommodityType, Sheet sheet) {
        if(null == sheet){
            QYAssert.isFalse("表单不能为空");
        }
        if(sheet.getLastRowNum() == 0){
            QYAssert.isFalse("模板内容不能为空");
        }

        sheet.forEach(row ->{
            int rowNum = row.getRowNum();
            // check模板是否正确
            if (rowNum == 0) {
                if(!(row.getLastCellNum() == 4)
                        || !checkRowNumZero(row, 0, "送货日期")
                        || !checkRowNumZero(row, 1, "客户编码")
                        || !checkRowNumZero(row, 2, addCommodityType == 1 ? "商品条码" : "商品编码")
                        || !checkRowNumZero(row, 3, "订货数量")){
                    QYAssert.isFalse("模板不正确");
                }
            }
        });
    }

    private boolean checkRowNumZero(Row row, int index, String cellName) {
        boolean result = true;
        if (row.getCell(index) == null || !cellName.equals(row.getCell(index).getStringCellValue())) {
            result = false;
        }
        return result;
    }

    /*public  boolean isNumber(String str){
        // 长度最多15位，允许2位小数
        Pattern pattern= Pattern.compile("^(\\d{1,15}|\\d{1,15}\\.\\d{1,3})$");
        Matcher match=pattern.matcher(str);
        if(match.matches() == false){
            return false;
        }else{
            return true;
        }
    }*/


    /**
     * 订单导入
     * @return
     */
    public JsonMsgBean importSave(OrderRequestDto dto, TokenInfo tokenInfo) {

        Long userId = tokenInfo.getUserId();
        Long enterpriseId = tokenInfo.getEnterpriseId();
        String userName = tokenInfo.getRealName();

        JsonMsgBean json = new JsonMsgBean(true);

        List<OrderImportSaveIDTO> importList = dto.getImportList();
        if (CollectionUtils.isEmpty(importList)) {
            QYAssert.isFalse("提交列表不能为空");
        }

        Map<String, List<OrderImportSaveIDTO>> storeCommodityMap = importList.stream().collect(Collectors.groupingBy(k -> String.format("%s%s%s", k.getStoreId().toString(), ",", k.getOrderTime())));
        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.IMPORT_ORDER_THREADPOOL);

        long currentTimeMillis = System.currentTimeMillis();
        Map<String, String> underStockMap = new ConcurrentHashMap<>();
        Map<String, String> storeCreateOrderSuccessMap = new ConcurrentHashMap<>();
        StringBuffer sb = new StringBuffer();

        // 初始化和任务数量一致
        CountDownLatch countDownLatch = new CountDownLatch(storeCommodityMap.size());
        for (Map.Entry<String, List<OrderImportSaveIDTO>> entry : storeCommodityMap.entrySet()) {
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    String[] str = entry.getKey().split(",");
                    List<OrderImportSaveIDTO> storeCommodityList = entry.getValue();
                    Long storeId = Long.valueOf(str[0]);
                    String orderTime = str[1];

                    OrderRequestDto requestDto = new OrderRequestDto();
                    requestDto.setIfImportSave(YesOrNoEnums.YES.getCode());
                    requestDto.setEnterpriseId(enterpriseId);
                    requestDto.setUserId(userId);
                    requestDto.setUserName(userName);
                    requestDto.setStoreId(storeId);
                    requestDto.setOrderTime(orderTime);
                    requestDto.setOrderRemark("订单导入");
                    requestDto.setPrintNum(1); //默认打印份数1
                    requestDto.setPrintType(dto.getPrintType()); // 默认本地打印
                    requestDto.setBusinessType(storeService.getStoreBussinessType(storeId));

                    List<OrderItemRequestDto> itemsList = new ArrayList<>();
                    for (OrderImportSaveIDTO importDto : storeCommodityList) {
                        OrderItemRequestDto item = new OrderItemRequestDto();
                        item.setProductId(importDto.getProductId());
                        item.setProductNum(importDto.getProductNum());
                        item.setProductCode(importDto.getProductCode());
                        itemsList.add(item);
                    }
                    requestDto.setItemsList(itemsList);

                    try {
                        requestDto.setUserId(userId);
                        OrderDto orderDto = orderCupService.createCupOrder(requestDto);
                        if (SpringUtil.isNotEmpty(orderDto.getCommodityUnderstockMap())) {
                            underStockMap.putAll(orderDto.getCommodityUnderstockMap());
                            return;
                        }
                        storeCreateOrderSuccessMap.put(entry.getKey(), entry.getKey());

                        // 拆单
                        SplitOrderKafkaVo splitOrderKafkaVo = new SplitOrderKafkaVo();
                        splitOrderKafkaVo.setOrderId(orderDto.getId());
                        splitOrderKafkaVo.setOrderTime(DateUtil.parseDate(orderDto.getOrderTime(), "yyyy-MM-dd"));
                        splitOrderKafkaVo.setType(KafkaMessageOperationTypeEnum.INSERT);
                        splitOrderKafkaVo.setCreateId(userId);
                        splitOrderKafkaVo.setEnterpriseId(78L);
                        splitOrderKafkaVo.setCommWarehouseMap(orderDto.getCommWarehouseMap());
                        //splitOrderService.execute(splitOrderKafkaVo, splitOrderService, weChatSendMessageService);
                        splitOrderSendKfkService.sendSplitOrderKfkMsg(splitOrderKafkaVo);
                    } catch (Exception e) {
                        log.error("订单导入失败", e);
                        sb.append(StringUtils.isBlank(e.getMessage()) ? "订单导入异常" : e.getMessage());

                    }finally {
                        // 进行记时，当一个线程完成时候countDownLatch -1
                        ThreadLocalUtils.remove();
                        countDownLatch.countDown();
                    }
                }
            });
        }

        // 阻塞主线程，当countDownLatch = 0时，线程都完成了
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.warn("countDownLatch.await() error", e);
        }

        if (sb.length() > 0) {
            if (SpringUtil.isNotEmpty(storeCreateOrderSuccessMap)) {
                json.setData(setImportDto(storeCreateOrderSuccessMap, storeCommodityMap));
            }
            json.setSuccess(false);
            json.setErrMsgList(Arrays.asList(sb.toString()));
            return json;
        }

        if (SpringUtil.isNotEmpty(underStockMap)) {
            OrderBatchImportODTO importDto = null;
            if (SpringUtil.isNotEmpty(storeCreateOrderSuccessMap)) {
                importDto = setImportDto(storeCreateOrderSuccessMap, storeCommodityMap);
            }

            if (importDto == null) {
                importDto = new OrderBatchImportODTO();
            }
            importDto.setUnderStockMap(underStockMap);
            json.setData(importDto);
            json.setSuccess(false);
            json.setMessage("underStock");
//            json.setErrMsgList(Arrays.asList("underStock"));
            return json;
        }

        return json;
    }


    private OrderBatchImportODTO setImportDto(Map<String, String> storeCreateOrderSuccessMap, Map<String, List<OrderImportSaveIDTO>> storeCommodityMap) {

        OrderBatchImportODTO importDto = new OrderBatchImportODTO();
        importDto.setStoreCreateOrderSuccessMap(storeCreateOrderSuccessMap);
        try {
            List<String> list = storeCommodityMap.keySet().stream().filter(s -> !storeCreateOrderSuccessMap.containsKey(s)).collect(Collectors.toList());
            importDto.setOrderNum(list.size());
            importDto.setStoreNum(list.stream().map(s -> s.split(",")[0]).collect(Collectors.toSet()).size());
        } catch (Exception e) {
            log.error("订单导入失败", e);
        }
        return importDto;
    }

    /**
     * 线程执行时间
     *
     * @param threadPool
     * @param currentTimeMillis
     */
    public void threadOver(ThreadPoolExecutor threadPool, long currentTimeMillis, String remark) {
        try {
            boolean loop = true;
            do {
                //等待所有线程执行完毕当前任务结束
                loop = !threadPool.awaitTermination(2, TimeUnit.SECONDS);//等待2秒
            } while (loop);

            if (loop != true) {
                log.info(remark + " 所有线程执行完毕");
            }

        } catch (InterruptedException e) {
            log.error(remark + " 线程执行时间异常", e);
        } finally {
            log.info(remark + " 耗时：" + (System.currentTimeMillis() - currentTimeMillis) + " 毫秒");
        }
    }

    /**
     * addCommodityType 1 按条码导入 2 按商品编码导入
     *
     * @return
     */
    public JsonMsgBean importOrderCommodity(String storeId, String orderTime, Workbook workbook, int addCommodityType,Integer planOrderType) {
        JsonMsgBean json = new JsonMsgBean(true);

        if (null == workbook) {
            json.setSuccess(false);
            json.setMessage("工作簿不能为空");
            return json;
        }
        Sheet sheet = workbook.getSheetAt(0);
        //QYAssert.notNull(sheet,"表单不能为空");
        if (null == sheet) {
            json.setSuccess(false);
            json.setMessage("表单不能为空");
            return json;
        }

        if (sheet.getLastRowNum() == 0) {
            json.setSuccess(false);
            json.setMessage("模板内容不能为空");
            json.setErrMsgList(Arrays.asList("模板内容不能为空"));
            return json;
        }
        final Boolean[] error = {false};
        sheet.forEach(row -> {
            int rowNum = row.getRowNum();
            // check模板是否正确
            if (rowNum == 0) {
                if (!(row.getLastCellNum() == 2)
                        || !checkRowNumZero(row, 0, addCommodityType == 1 ? "条码" : "商品编码")
                        || !checkRowNumZero(row, 1, "数量")) {
                    json.setSuccess(false);
                    json.setMessage("模板不正确");
                    json.setErrMsgList(Arrays.asList("模板不正确"));
                    error[0] = true;
                    //return json;
                }
            }
        });
        if (error[0]) {
            return json;
        }

        List<Commodity> commodityReturnList = new ArrayList<>();
        Set<String> repeatCommodity = new HashSet<>();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Commodity commodity = new Commodity();
            Row row = sheet.getRow(i);

            if(row != null) {
                row.cellIterator().forEachRemaining(c -> c.setCellType(CellType.STRING));
            }

            // 校验非空
            String comm = addCommodityType == 1 ? "条码" : "商品编码";
            if (row == null
                    || row.getCell(0) == null
                    || row.getCell(1) == null
                    || row.getCell(0).getStringCellValue() == null
                    || row.getCell(1).getStringCellValue() == null) {
                json.setSuccess(false);
                json.setMessage("第" + (i + 1) + "行" + comm + "或数量不能为空");
                json.setErrMsgList(Arrays.asList(json.getMessage()));
                return json;
            }

            // 商品编码/条码
            String commodityCode = row.getCell(0).getStringCellValue().trim();
            if (!repeatCommodity.add(commodityCode)) {
                json.setSuccess(false);
                json.setMessage("第" + (i + 1) + "行" + comm + "重复");
                json.setErrMsgList(Arrays.asList(json.getMessage()));
                return json;
            }

            // 数量
            String quantity = row.getCell(1).getStringCellValue().trim();
            if (!NumberUtils.isNumber(quantity)) {
                json.setSuccess(false);
                json.setMessage("第" + (i + 1) + "行订货数量必须大于0并且长度最多15位，允许2位小数");
                json.setErrMsgList(Arrays.asList(json.getMessage()));
                return json;
            }
            commodity.setCommodityCode(commodityCode);
            commodity.setProductNumber(new BigDecimal(quantity));
            commodityReturnList.add(commodity);
        }

        StringBuffer sb = new StringBuffer();
        // 校验 商品是否客户编码中的、可售的商品
        if (CollectionUtils.isNotEmpty(commodityReturnList)) {
            List<String> commodityCodeList = commodityReturnList.stream().map(item -> item.getCommodityCode()).collect(Collectors.toList());

            CommodityListQueryReqVO commodityListQueryReqVO = new  CommodityListQueryReqVO();
            commodityListQueryReqVO.setStoreId(Long.valueOf(storeId));
            String[] selectedCodesArr = commodityCodeList.toArray(new String[commodityCodeList.size()]);
            commodityListQueryReqVO.setCodes(selectedCodesArr);
            commodityListQueryReqVO.setAddCommodityType(addCommodityType);
            if(Objects.equals(planOrderType,1)){
                commodityListQueryReqVO.setPlanOrderType(1);
            }
            List<Commodity> commodityList = commodityService.findStoreCommodityListByParams(commodityListQueryReqVO);
            commodityList = commodityList.stream().filter(commodity -> CombTypeEnum.COMB.getCode() != commodity.getProductType()).collect(toList());


            Map<Long, StorePromotionCommodityPriceODTO> storePromotionMap = commodityService.selectCommodityPromotionToMap(
                    commodityList
                            .stream()
                            .map(Commodity::getId)
                            .distinct()
                            .collect(Collectors.toList()),Long.parseLong(storeId),orderTime);


            Map<String, Commodity> idAndObjMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(commodityList)) {
                if (addCommodityType == 1) {
                    idAndObjMap = commodityList.stream().collect(Collectors.toMap(Commodity::getBarCode, Function.identity()));
                }
                if (addCommodityType == 2) {
                    idAndObjMap = commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityCode, Function.identity()));
                }
            }

            for (int i = 0; i < commodityReturnList.size(); i++) {
                Commodity c = commodityReturnList.get(i);
                if (idAndObjMap.get(c.getCommodityCode()) != null) {
                    BigDecimal productNumber = c.getProductNumber();
                    BeanUtils.copyProperties(idAndObjMap.get(c.getCommodityCode()), c);
                    c.setProductNumber(productNumber);
                } else {
                    json.setSuccess(false);
                    sb.append("第" + (i + 2) + "行、");
                    //return json;
                }
            }


            if (sb.length() > 1) {
                if(Objects.equals(planOrderType,1)){
                    json.setMessage(sb.toString().substring(0, sb.toString().length() - 1) + "商品不存在或计划销售商品未同步！");
                }else{
                    json.setMessage(sb.toString().substring(0, sb.toString().length() - 1) + "商品不存在");
                }
                json.setErrMsgList(Arrays.asList(json.getMessage()));
                return json;
            }

            json.setData(commodityReturnList);

            orderCupService.processCommodityPromotion(commodityReturnList,storePromotionMap);

            for (Commodity c : commodityReturnList) {
                if(c.getPromotionFlag() == 1){
                    BigDecimal lineOrderAmount =
                            c.getCommodityPrice().multiply(c.getPromotionCount())
                                    .add(c.getOriginalCommodityPrice().multiply(c.getNormalCount()))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP);
                    c.setCommodityLinePrice(lineOrderAmount);
                }else{
                    BigDecimal lineOrderAmount = c.getCommodityPrice().multiply(c.getProductNumber()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    c.setCommodityLinePrice(lineOrderAmount);
                }
            }

        }

        return json;
    }
}
