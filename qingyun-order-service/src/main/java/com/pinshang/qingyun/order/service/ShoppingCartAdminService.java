package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.GlobalSupplierNameConstant;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.order.*;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.util.OrderUtil;
import com.pinshang.qingyun.order.vo.order.ShoppingCartDetailVo;
import com.pinshang.qingyun.storage.dto.WarehouseODto;
import com.pinshang.qingyun.storage.service.WarehouseClient;
import com.pinshang.qingyun.supplier.dto.FullSupplierODTO;
import com.pinshang.qingyun.supplier.dto.QuerySupplierByIdsIDTO;
import com.pinshang.qingyun.supplier.dto.SupplierODTO;
import com.pinshang.qingyun.supplier.service.SupplierClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/3/1
 */
@Service
public class ShoppingCartAdminService {
    @Autowired
    ShoppingCartMapper shoppingCartMapper;
    @Autowired
    OrderService orderService;
    @Autowired
    ShoppingCartItemMapper itemMapper;
    @Autowired
    CommodityMapper commodityMapper;
    @Autowired
    OrderMapper orderMapper;
    @Autowired
    ShopMapper shopMapper;
    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private SupplierClient supplierClient;
    @Autowired
    private ShoppingCartService shoppingCartService;
    @Autowired
    private ShoppingCartDeleteService shoppingCartDeleteService;
    @Autowired
    private WarehouseClient warehouseClient;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;

    public ShoppingCartDetailVo queryShoppingCartAdmin(List<Long> storeIdList, Long userId, Boolean bigShop){
        ShoppingCartDetailVo vo = new ShoppingCartDetailVo();

        List<ShoppingCartAdminEntry> shoppingCartAdminList = shoppingCartMapper.shoppingCartAdminByStoreIdList(storeIdList, userId, bigShop);

        Set<String> commodityIdList = new HashSet<>();
        Set<Integer> shopTypeList = new HashSet<>();
        Set<Long> supplierIdList = new HashSet<>();
        Set<Long> warehouseIdList = new HashSet<>();
        List<Long> shoppingCartIdList = new ArrayList<>();
        shoppingCartAdminList.forEach(item ->{
            commodityIdList.add(item.getCommodityId() + "");
            shopTypeList.add(item.getShopType());
            supplierIdList.add(item.getSupplyId());
            warehouseIdList.add(item.getWarehouseId());
            if(!shoppingCartIdList.contains(item.getShoppingCartId())){
                shoppingCartIdList.add(item.getShoppingCartId());
            }
        });

        // 查询门店订货通用设置
        Map<Integer, List<MdShopOrderSettingEntry>> settingMap = new HashMap<>();
        for(Integer shopType : shopTypeList){
            List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByShopType(shopType, new ArrayList<>(commodityIdList));
            if(!CollectionUtils.isEmpty(settingList)){
                settingMap.put(shopType, settingList);
            }
        }

        // 供应商和仓库信息
        QuerySupplierByIdsIDTO idsIDTO = new QuerySupplierByIdsIDTO();
        idsIDTO.setSupplierIds(new ArrayList<>(supplierIdList));
        Map<Long, FullSupplierODTO> supplierIdAndODTOMap = supplierClient.querySupplierByIds(idsIDTO);
        Map<Long, WarehouseODto> warehouseIdAndODtoMap = warehouseClient.queryWarehouseListByIds(new ArrayList<>(warehouseIdList));
        SupplierODTO globalEntry = supplierClient.getSupplierByCode(GlobalSupplierNameConstant.XS_SUPPLIER_CODE);

        List<Long> stallIdList = shoppingCartAdminList.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                .stream().map(item -> item.getStallId()).collect(Collectors.toList());
        Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIdList);

        // 按购物车id分组
        Map<Long, List<ShoppingCartAdminEntry>> shoppingCartAdminEntryMap = shoppingCartAdminList.stream().collect(Collectors.groupingBy(ShoppingCartAdminEntry::getShoppingCartId));

        ShoppingCartDetailVo.TableData[] sites =new ShoppingCartDetailVo.TableData[shoppingCartAdminEntryMap.size()];
        vo.setSites(sites);
        int i=0;

        for (Long shoppingCartId : shoppingCartIdList) {
            List<ShoppingCartAdminEntry> entries = shoppingCartAdminEntryMap.get(shoppingCartId);
            ShoppingCartAdminEntry shoppingCartEntry = entries.get(0);

            List<MdShopOrderSettingEntry> settingList = settingMap.get(shoppingCartEntry.getShopType());
            Map<Long, MdShopOrderSettingEntry> settMap = settingList.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));
            MdShopOrderSettingEntry mdShopOrderSettingEntry = settMap.get(shoppingCartEntry.getCommodityId());

            ShoppingCartDetailVo.TableData table =new ShoppingCartDetailVo.TableData();
            sites[i++] = table;
            ShoppingCartDetailVo.TitleRow[] titleRows =new ShoppingCartDetailVo.TitleRow[1];
            table.setTitle(titleRows);
            ShoppingCartDetailVo.TitleRow title= new ShoppingCartDetailVo.TitleRow();
            BeanUtils.copyProperties(shoppingCartEntry,title);
            title.setLogisticsModelName(IogisticsModelEnums.getName(shoppingCartEntry.getLogisticsModel()));
            title.setShoppingCartId(shoppingCartEntry.getShoppingCartId() + "");
            title.setDisable(false);
            FullSupplierODTO fullSupplierODTO = supplierIdAndODTOMap.get(shoppingCartEntry.getSupplyId());
            if (fullSupplierODTO != null) {
                 title.setCompanyName(fullSupplierODTO.getSupplierName());
            }
            WarehouseODto warehouseODto = warehouseIdAndODtoMap.get(shoppingCartEntry.getWarehouseId());
            if (warehouseODto != null) {
                title.setWarehouseName(warehouseODto.getWarehouseName());
            }

            if((shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DISPATCHING.getCode())){
                title.setCompanyName(globalEntry.getSupplierName());
                title.setSupplyTime((mdShopOrderSettingEntry.getDefaultWarehouseBeginTime() ==null?"":mdShopOrderSettingEntry.getDefaultWarehouseBeginTime()) +"-"+(mdShopOrderSettingEntry.getDefaultWarehouseEndTime()==null?"":mdShopOrderSettingEntry.getDefaultWarehouseEndTime()));
            }else if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode()) {
                title.setSupplyTime((mdShopOrderSettingEntry.getDefaultSupplierBeginTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierBeginTime()) + "-" + (mdShopOrderSettingEntry.getDefaultSupplierEndTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierEndTime()));
            }else if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_CONNECTION.getCode()) {
                title.setSupplyTime((mdShopOrderSettingEntry.getDefaultSupplierBeginTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierBeginTime()) + "-" + (mdShopOrderSettingEntry.getDefaultSupplierEndTime() == null ? "" : mdShopOrderSettingEntry.getDefaultSupplierEndTime()));
            }

            if(shoppingCartEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DIRECT_SENDING.getCode()){
                // 直送的仓库名字为空
                title.setWarehouseName("");
            }

            titleRows[0] = title;
            table.setTitle(titleRows);

            List<ShoppingCartDetailVo.DeliveryBatchDay> deliveryBatchDays = shoppingCartService.getDeliveryBatchDays(shoppingCartEntry.getDeleveryTimeRange(), true);
            table.setDeliveryBatchDays(deliveryBatchDays);
            title.setVarietyTotal(entries.size());
            Double aa = entries.stream().mapToDouble(item -> Double.valueOf(item.getQuantity() + "")).sum();
            title.setQuantityTotal(new BigDecimal(aa));
            title.setStallName(stallIdAndNameMap.get(shoppingCartEntry.getStallId()));
        }
        return vo;
    }

}
