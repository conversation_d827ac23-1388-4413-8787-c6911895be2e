package com.pinshang.qingyun.order.controller.xda.v4;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.TerminalSourceTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.aspect.RequestBodyAndHeader;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.xda.v3.XdaPreOrderItemV3ODTO;
import com.pinshang.qingyun.order.dto.xda.v4.*;
import com.pinshang.qingyun.order.service.xda.v4.CommoditySaleDayStatisticsV4Service;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import com.pinshang.qingyun.order.service.xda.v4.XdaOrderV4Service;
import com.pinshang.qingyun.order.service.xda.v4.XdaPreOrderService;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.tms.dto.transport.WaybillAndRouteInfoODTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2024/3/4 9:51
 */
@RestController
@RequestMapping("/xda/orderV4")
@Api(value = "鲜达APP订单相关接口V4",tags ="XdaAppOrderV4Controller",description ="鲜达APP订单相关接口V4" )
public class XdaAppOrderV4Controller {
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private XdaOrderV4Service xdaOrderV4Service;
    @Autowired
    private XdaPreOrderService xdaPreOrderService;
    @Autowired
    private TdaOrderService tdaOrderService;
    @Autowired
    private CommoditySaleDayStatisticsV4Service commoditySaleDayStatisticsV4Service;


    @ApiOperation(value = "预览订单", notes = "预览订单")
    @ApiImplicitParam(name = "xdaPreOrderV4IDTO", value = "", required = true, paramType = "body", dataTypeClass = XdaPreOrderV4IDTO.class)
    @RequestMapping(value = "/preOrderV4",method = RequestMethod.POST)
    public XdaPreOrderV4ODTO preOrderView(@RequestBodyAndHeader XdaPreOrderV4IDTO xdaPreOrderV4IDTO){
        try {
            XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
            Long storeId = xdaTokenInfo.getStoreId();
            QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
            QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
            ThreadLocalUtils.setLogisticsCenterId(xdaPreOrderV4IDTO.getLogisticscenterid());
            xdaPreOrderV4IDTO.setStoreId(storeId);
            xdaPreOrderV4IDTO.setAppCode(xdaTokenInfo.getAppCode());
            xdaPreOrderV4IDTO.setAppVersion(xdaTokenInfo.getAppVersion());
            return xdaOrderV4Service.preOrderView(xdaPreOrderV4IDTO);
        } finally {
            ThreadLocalUtils.remove();
        }
    }
    /**
     * 预览订单 详细信息
     */
    @ApiOperation(value = "预览订单 -商品详细信息", notes = "预览订单 -商品详细信息")
    @ApiImplicitParam(name = "xdaPreOrderV3IDTO", value = "", required = true, paramType = "body", dataTypeClass = XdaPreOrderV4IDTO.class)
    @RequestMapping(value = "/preOrderViewCommodityListV4",method = RequestMethod.POST)
    public XdaPreOrderItemV4ODTO preOrderViewCommodityListV4(@RequestBodyAndHeader XdaPreOrderV4IDTO xdaPreOrderV3IDTO){
        try {
            XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
            Long storeId = xdaTokenInfo.getStoreId();
            QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
            QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
            ThreadLocalUtils.setLogisticsCenterId(xdaPreOrderV3IDTO.getLogisticscenterid());
            xdaPreOrderV3IDTO.setStoreId(storeId);
            xdaPreOrderV3IDTO.setAppCode(xdaTokenInfo.getAppCode());
            xdaPreOrderV3IDTO.setAppVersion(xdaTokenInfo.getAppVersion());
            return xdaOrderV4Service.preOrderViewCommodityListV4(xdaPreOrderV3IDTO);
        } finally {
            ThreadLocalUtils.remove();
        }
    }
    /**
     * 创建订单
     * @param xdaCreatePrePayOrderV4IDTO
     * @return
     */
    @ApiOperation(value = "创建订单", notes = "创建订单")
    @ApiImplicitParam(name = "xdaCreatePrePayOrderV4IDTO", value = "", required = true, paramType = "body", dataTypeClass = XdaCreatePrePayOrderV4IDTO.class)
    @RequestMapping(value = "/creatOrderV4",method = RequestMethod.POST)
    public XdaSaveOrderODTO creatOrder(@RequestBodyAndHeader XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO){
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId = xdaTokenInfo.getStoreId();
        QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        String appCode = xdaTokenInfo.getAppCode();
        Long userId = xdaTokenInfo.getUserId();
        TerminalSourceTypeEnum typeEnum = xdaTokenInfo.getTerminalSourceType();
        xdaCreatePrePayOrderV4IDTO.convert(storeId, appCode, userId, typeEnum);
        xdaCreatePrePayOrderV4IDTO.setAppVersion(xdaTokenInfo.getAppVersion());
        String lockKey = LockConstants.generateOrderCommitLock(storeId);
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                ThreadLocalUtils.setLogisticsCenterId(xdaCreatePrePayOrderV4IDTO.getLogisticscenterid());
                return xdaOrderV4Service.creatOrder(xdaCreatePrePayOrderV4IDTO);
            } finally {
                ThreadLocalUtils.remove();
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return XdaSaveOrderODTO.Error("");
    }
    @PostMapping("/cancelOrderV4")
    @ApiOperation(value = "订单取消", notes = "订单取消")
    public Integer cancelOrderV4(@RequestBody OrderCancelV4IDTO idto){
        XdaTokenInfo tokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId =  tokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        QYAssert.isTrue(null != idto.getOrderId() , "订单id不存在!");
        QYAssert.isTrue(null != idto.getReasonOptionId() , "取消原因必选!");
        idto.setStoreId(storeId);
        String lockKey = LockConstants.generateOrderStatusLockKey(idto.getOrderId());
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                return xdaOrderV4Service.cancelOrderV4(idto);
            } finally {
                lock.unlock();
            }
        }
        QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        return null;
    }


    @PostMapping("/cancelXdaPreOrder")
    @ApiOperation(value = "鲜达预订单取消", notes = "鲜达预订单取消")
    public Integer cancelXdaPreOrder(@RequestBody OrderCancelV4IDTO idto){
        XdaTokenInfo tokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId =  tokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        QYAssert.isTrue(null != idto.getOrderId() , "订单id不存在!");
        QYAssert.isTrue(null != idto.getReasonOptionId() , "取消原因必选!");
        idto.setStoreId(storeId);
        String lockKey = LockConstants.generateOrderStatusLockKey(idto.getOrderId());
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                return xdaPreOrderService.cancelXdaPreOrder(idto);
            } finally {
                lock.unlock();
            }
        }
        QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        return null;
    }

    /**
     * 订单复制
     * @param orderId
     * @return
     */
    @RequestMapping(value = "/copyOrderByIdXdaAppV4",method = RequestMethod.GET)
    @ApiOperation(value = "订单复制", notes = "订单复制")
    public Integer copyOrderByIdXdaAppV4(@RequestParam(value = "orderId",required = false) Long orderId,@RequestParam(value = "orderDate",required = false) String orderDate){
        XdaTokenInfo tokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId =  tokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");

        String lockKey = LockConstants.generateOrderStatusLockKey(storeId);
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.tryLock()){
            try {
                return xdaOrderV4Service.copyOrderByIdXdaAppV4(orderId,orderDate);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return null;
    }

    @PostMapping("/listV4")
    @ApiOperation(value = "订单列表")
    public ApiResponse<XdaOrderAppV4ODTO> queryOrderListByPageV4(@RequestBody XdaQueryOrderAppParamV4 param, HttpServletResponse response){
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG,QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        XdaTokenInfo tokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId =  tokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        param.setStoreId(storeId);
        return ApiResponse.convert(xdaOrderV4Service.queryOrderListByPageV4(param, tokenInfo.getAppVersion()));
    }

    @GetMapping("/queryOrderDetailByCodeV4")
    @ApiOperation(value = "订单列表详情")
    public XdaOrderAppV4ODTO queryOrderDetailByCodeV4(@RequestParam(value = "orderCode",required = false) String orderCode){
        XdaTokenInfo tokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId =  tokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        return xdaOrderV4Service.queryOrderDetailByCodeV4(orderCode,storeId);
    }

    /**
     * 内部接口代码无变化  无需升级
     * 从新包装方便前端统一版本
     * @param orderCode
     * @return
     */
    @ApiOperation(value = "订单列表详情-订单商品清单", notes = "订单列表详情-订单商品清单")
    @RequestMapping(value = "/queryXdaOrderDetailV4",method = RequestMethod.POST)
    public XdaPreOrderItemV3ODTO queryXdaOrderCommodityDetailV4(@RequestParam(value = "orderCode",required = false) String orderCode){
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId = xdaTokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        return xdaOrderV4Service.queryXdaOrderDetailV4(orderCode);
    }

   /* @ApiOperation(value = "app端选择送货日期时间段", notes = "app端选择送货日期时间段")
    @RequestMapping(value = "/saveDeliveryTimeRange",method = RequestMethod.POST)
    public void saveDeliveryTimeRange(@RequestBody TdaDeliveryTimeRangeODTO timeRangeODTO){
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId = xdaTokenInfo.getStoreId();
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        tdaOrderService.saveDeliveryTimeRange(storeId, timeRangeODTO);
    }*/

    @ApiOperation(value = "job每天汇总鲜达商品销量日统计", notes = "job每天汇总鲜达商品销量日统计")
    @RequestMapping(value = "/xdaCommoditySaleStatisticsDayReport",method = RequestMethod.GET)
    public Boolean xdaCommoditySaleStatisticsDayReport(@RequestParam(value = "orderTime",required = false) String orderTime){
        return commoditySaleDayStatisticsV4Service.xdaCommoditySaleStatisticsDayReport(orderTime);
    }

    @ApiOperation(value = "订单页优惠券列表", notes = "订单页优惠券列表")
    @ApiImplicitParam(name = "xdaPreOrderV4IDTO", required = true, paramType = "body", dataTypeClass = XdaPreOrderV4IDTO.class)
    @RequestMapping(value = "/xdaOrderCouponList", method = RequestMethod.POST)
    public XdaOrderCouponODTO xdaOrderCouponList(@RequestBodyAndHeader XdaPreOrderV4IDTO idto) {
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId = xdaTokenInfo.getStoreId();
        QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        idto.setStoreId(storeId);
        idto.setAppCode(xdaTokenInfo.getAppCode());
        idto.setAppVersion(xdaTokenInfo.getAppVersion());
        return xdaOrderV4Service.xdaOrderCouponList(idto);
    }

    @ApiOperation(value = "根据订单Id查询物流信息", notes = "物流查询")
    @GetMapping(value = "/queryLogisticsByOrderId")
    public WaybillAndRouteInfoODTO queryLogisticsByOrderId(@RequestParam(value = "orderId") Long orderId) {
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId = xdaTokenInfo.getStoreId();
        QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
        QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
        return xdaOrderV4Service.queryLogisticsByOrderId(orderId);
    }
}
