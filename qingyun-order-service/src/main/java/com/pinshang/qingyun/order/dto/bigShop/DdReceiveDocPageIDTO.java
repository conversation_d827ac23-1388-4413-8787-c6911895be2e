package com.pinshang.qingyun.order.dto.bigShop;

import com.pinshang.qingyun.order.mapper.entry.stall.StallEntry;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Data
public class DdReceiveDocPageIDTO extends StallEntry {

    private Long shopId;

    @ApiModelProperty("送货日期")
    private String orderTime;

    @ApiModelProperty("配送批次 1=1配 + 补货  2=2配  8=新开店")
    private Integer deliveryBatch;

    @ApiModelProperty("单据状态：0 待收货 1 已经收货")
    private Integer docStatus;

    @ApiModelProperty("订单号")
    private String orderCode;

}
