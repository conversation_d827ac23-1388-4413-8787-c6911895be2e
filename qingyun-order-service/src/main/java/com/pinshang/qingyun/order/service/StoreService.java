package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.StoreMapper;
import com.pinshang.qingyun.order.mapper.entry.store.StoreAccountEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreEntry;
import com.pinshang.qingyun.order.model.distribution.DistributionLine;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.vo.shop.DistributionStoreAccountQueryVo;
import com.pinshang.qingyun.order.vo.shop.DistributionStoreAccountVo;
import com.pinshang.qingyun.order.vo.shop.StoreAccountVo;
import com.pinshang.qingyun.store.dto.storeSettlement.PaymentParentChildStoreSettlementIDTO;
import com.pinshang.qingyun.store.dto.storeSettlement.PaymentParentChildStoreSettlementODTO;
import com.pinshang.qingyun.store.service.StoreSettlementNewClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/8 17:18
 */
@Service
@Slf4j
public class  StoreService {
    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private ShopService shopService;
    @Autowired
    private StoreSettlementNewClient storeSettlementNewClient;

    @Autowired
    private RedissonClient redissonClient;

    public PageInfo<StoreAccountEntry> getStoreAccountList(StoreAccountVo vo){
        PageInfo<StoreAccountEntry> pageInfo = PageHelper.startPage(vo.getPageNo(),vo.getPageSize()).doSelectPageInfo(
                () -> storeMapper.getStoreAccountList(vo.getEmployeeId(),vo.getStoreCode())
        );
        return pageInfo;
    }


    public Store findByStoreCode(String storeCode){
        return storeMapper.getByStoreCode(storeCode);
    }

    public PageInfo<DistributionStoreAccountVo> distributionList(DistributionStoreAccountQueryVo vo){
        if(vo.getStoreIds() == null || vo.getStoreIds().isEmpty()){
            vo.setStoreIds(null);
        }
        return  PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(
                () -> storeMapper.getDistributionStoreAccountList(vo.getStoreTypeId(),vo.getVagueStr(),vo.getStoreIds())
        );
    }

    public List<Long> handleExcelImportInfo(List<String> codes){
        List<Store> stores = storeMapper.findInStoreCodes(codes);
        if(stores == null || stores.isEmpty()){
            QYAssert.isTrue(false, "未找到导入配送排序客户匹配的信息,客户不存在!");
        }
        if(stores.size() < codes.size()){
            Set<String> codesExist = stores.stream().map(Store::getStoreCode).collect(Collectors.toSet());
            String msg = "导入的客户未能在系统中找到匹配信息:"
                    +Arrays.toString(codes.stream().filter(x -> !codesExist.contains(x)).toArray());
            log.error(msg);
            QYAssert.isTrue(false, msg);
        }
        Map<String, Long> toSortMap = stores.stream().collect(Collectors.toMap(Store::getStoreCode, Store::getId));
        List<Long> ids = new ArrayList<>(toSortMap.size());
        codes.forEach(x->ids.add(toSortMap.get(x)));
        return ids;
    }

    /**
     * 根据客户ID查找客户线路
     * @param storeIds
     * @return
     */
    public Map<Long, DistributionLine> queryStoreLineByStoreIds(List<Long> storeIds) {
        Map<Long, DistributionLine> result = new HashMap<>();
        List<DistributionLine> dbResult = storeMapper.queryStoreLineByStoreIds(storeIds);
        if (SpringUtil.isEmpty(dbResult)) {
            return result;
        }
        dbResult.stream().collect(groupingBy(DistributionLine::getStoreId)).forEach((k, v) -> result.put(k, v.get(0)));
        return result;
    }

    /**
     * 根据客户id集合 查询客户详细信息
     * @param storeIdList
     * @return
     */
    public List<Store> findStoreListByStoreIdList(List<Long> storeIdList){
        QYAssert.isTrue(SpringUtil.isNotEmpty(storeIdList),"客户id集合为空");
        Example example = new Example(Store.class);
        example.createCriteria().andIn("id",storeIdList);
        return storeMapper.selectByExample(example);
    }

    /**
     * 条件查询客户列表
     * @param storeName
     * @return
     */
    public List<StoreEntry> findStoreListByStoreName(String storeName){
        QYAssert.isTrue(StringUtils.isNotEmpty(storeName),"条件参数异常");
        return storeMapper.findStoreListByStoreName(storeName);
    }

    /**
     * 根据客户 id 查询客户信息
     * @param storeId
     * @return
     */
    public Store findStoreByStoreId(Long storeId){
        QYAssert.isTrue(null!= storeId,"id 不能为空");
        return storeMapper.selectByPrimaryKey(storeId);
    }

    /**
     * 查询所有绑定门店的客户
     * @return
     */
    public List<StoreEntry> findAllShopStoreList(){
        return storeMapper.findAllShopStoreList();
    }


     public Boolean getIsXd(Long storeId){
         Shop shop = shopService.getShopFromCacheThenDb(storeId);
         return shop.getShopType().equals(ShopTypeEnums.XD.getCode());
     }



    /**
     * 判断是否是通达销售
     */
    public boolean isTdaStore(Long storeId) {
        Store store = findStoreByStoreId(storeId);
        QYAssert.isTrue(!(storeId == null || store == null), "客户不存在！");
        return Objects.equals(store.getBusinessType(), 10);
    }

    public List<StoreEntry> queryStoreByStoreCodeList(List<String> storeCodeList) {
        return storeMapper.queryStoreByStoreCodeList(storeCodeList);
    }

    /**
     * 根据客户id 查询外部客户公司id
     * @param storeId
     * @return
     */
    public Long selectStoreCompanyIdByStoreId(Long storeId){
        List<Long> companyIdList = storeMapper.findStoreCompanyIdByStoreId(storeId);
        if(SpringUtil.isNotEmpty(companyIdList)){
            return companyIdList.get(0);
        }else{
            return null;
        }
    }


    /**
     * 门店、客服下单校验账户余额
     * @param storeId
     * @param paidAmount
     * @return
     */
    public boolean matchBillCondition(Long storeId, BigDecimal paidAmount) {
        boolean result = true;
        PaymentParentChildStoreSettlementODTO storeSettlementODTO = queryStoreCollectPriceByStoreId(storeId, false);

        // 如果是预付
        if(YesOrNoEnums.YES.getCode().equals(storeSettlementODTO.getCollectStatus())) {

            result = storeSettlementODTO.getCollectPrice().compareTo(paidAmount) >= 0;
        }
        return result;
    }

    /**
     * 修改时候判断客户余额
     * @param storeId
     * @param paidAmount 修改后新订单金额
     * @param orderAmount 修改前订单金额
     * @param isXda
     * @return
     */
    public boolean updateMatchBillCondition(Long storeId, BigDecimal paidAmount, BigDecimal orderAmount) {
        boolean result = true;
        PaymentParentChildStoreSettlementODTO storeSettlementODTO = queryStoreCollectPriceByStoreId(storeId, false);

        // 如果是预付
        if(YesOrNoEnums.YES.getCode().equals(storeSettlementODTO.getCollectStatus())) {
            BigDecimal totalAmount = storeSettlementODTO.getCollectPrice().add(orderAmount);
            result = totalAmount.compareTo(paidAmount) >= 0;
        }
        return result;
    }

    /**
     * 查询预付客户余额
     * @param storeId
     * @param isXda
     * @return
     */
    public PaymentParentChildStoreSettlementODTO queryStoreCollectPriceByStoreId(Long storeId, Boolean isXda) {
        PaymentParentChildStoreSettlementIDTO idto = new PaymentParentChildStoreSettlementIDTO();
        idto.setStoreId(storeId);
        idto.setIsXda(isXda);
        PaymentParentChildStoreSettlementODTO odto = storeSettlementNewClient.selectParentChildPaymentStoreInfoByStoreId(idto);
        odto.setCollectPrice(odto.getCollectPrice() == null ? new BigDecimal("0") : odto.getCollectPrice());
        return odto;
    }

    /**
     * 根据 storeId 获取门店信息
     * @param storeId
     * @return
     */
    public Integer getStoreBussinessType(Long storeId){
        QYAssert.notNull(storeId,"客户ID不能为空");
        Store store = storeMapper.selectByPrimaryKey(storeId);
        QYAssert.notNull(store, "客户不存在");

        return getSafeBusinessType(store.getBusinessType());
    }

    public Integer getSafeBusinessType(Integer businessType) {
        // 定义允许直接返回的业务类型列表
        Set<Integer> specialBusinessTypes = new HashSet<>(Arrays.asList(
                BusinessTypeEnums.TD_SALE.getCode(),
                BusinessTypeEnums.BIGSHOP_SALE.getCode(),
                BusinessTypeEnums.PLAN_SALE.getCode(),
                BusinessTypeEnums.B_COUNTRY.getCode()
        ));

        if (businessType != null && specialBusinessTypes.contains(businessType)) {
            return businessType;
        }

        return BusinessTypeEnums.SALE.getCode();
    }

}
