package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.model.shop.Shop;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2021/5/13
 */
@Service
public class ShopService {

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private RedissonClient redissonClient;

    private static final String ORDER_SHOP_CACHE_KEY = "ORDER:SHOP:CACHE:";



    public Shop getShopFromCacheThenDb(Long storeId) {
        RBucket<Shop> bucket = redissonClient.getBucket(ORDER_SHOP_CACHE_KEY + storeId);
        Shop shop = bucket.get();
        if(Objects.isNull(shop)){
            Example example = new Example(Shop.class);
            example.createCriteria().andEqualTo("storeId",storeId);
            List<Shop> shopList = shopMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(shopList)){
                shop = shopList.get(0);
                bucket.set(shop,1, TimeUnit.MINUTES);
            }
        }
        return shop;
    }

    /**
     * 根据 storeId 获取门店信息
     * @param storeId
     * @return
     */
    public Integer getShopType(Long storeId){
        QYAssert.notNull(storeId,"客户ID不能为空");
        Shop shop = getShopFromCacheThenDb(storeId);
        return shop == null ? null:shop.getShopType();
    }

    /**
     * 根据 storeId 获取门店信息
     * @param storeId
     * @return
     */
    public Shop getShopByStoreId(Long storeId){
        QYAssert.notNull(storeId,"客户ID不能为空");
        Shop shop = getShopFromCacheThenDb(storeId);
        return shop;
    }

    /**
     * 根据 storeId 获取门店信息
     * @return
     */
    public List<Shop> getShopByStoreIdList(List<Long> storeIdList){
        QYAssert.notNull(storeIdList,"客户ID不能为空");

        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andIn("storeId", storeIdList);
        return shopMapper.selectByExample(shopEx);
    }

    /**
     * 根据 shopId 获取门店信息
     * @return
     */
    public List<Shop> getShopByIdList(List<Long> shopIdList){
        QYAssert.notNull(shopIdList,"ID不能为空");

        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andIn("id", shopIdList);
        return shopMapper.selectByExample(shopEx);
    }

    /**
     * 根据 shopTypeList 获取storeIdList
     * @return
     */
    public List<Long> getStoreIdListByShopTypeList(List<Integer> shopTypeList){
        QYAssert.isTrue(CollectionUtils.isNotEmpty(shopTypeList),"shopTypeList不能为空");

        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andIn("shopType", shopTypeList);
        List<Shop> shopList = shopMapper.selectByExample(shopEx);
        return shopList.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
    }

    /**
     * 根据 shopType 获取storeIdList
     * @return
     */
    public List<Long> getStoreIdListByShopType(Integer shopType){
        QYAssert.notNull(shopType,"shopType不能为空");

        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andEqualTo("shopType", shopType);
        List<Shop> shopList = shopMapper.selectByExample(shopEx);
        return shopList.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
    }


    /**
     * 根据 shopType 获取shopIdList
     * @return
     */
    public List<Long> getShopIdListByShopType(Integer shopType){
        QYAssert.notNull(shopType,"shopType不能为空");

        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andEqualTo("shopType", shopType);
        List<Shop> shopList = shopMapper.selectByExample(shopEx);
        return shopList.stream().map(item -> item.getId()).collect(Collectors.toList());
    }

    /**
     * 根据 storeId 获取storeCode ,门店信息
     * @return
     */
    public List<StoreEntry> getStoreByStoreIdList(List<Long> storeIdList){
        QYAssert.notNull(storeIdList,"客户ID不能为空");
        return  shopMapper.selectStoreList(storeIdList);
    }


    /**
     * 根据 storeId判断是否加盟商门店类型
     * @param storeId
     * @return
     */
    public Boolean isJmShop(Long storeId){
        QYAssert.notNull(storeId,"客户ID不能为空");
        return ShopTypeEnums.XSJM.getCode().equals(getShopType(storeId));
    }

    /**
     * 根据 storeIds判断是否加盟商门店类型
     * @param storeIds
     * @return
     */
    public Map<Long,Boolean> listJmShop(List<Long> storeIds){
        QYAssert.isTrue(CollectionUtils.isNotEmpty(storeIds),"客户ID列表不能为空");
        List<StoreEntry> storeEntries = shopMapper.listShopType(storeIds);
        if(CollectionUtils.isEmpty(storeEntries)){
            return new HashMap<>();
        }
        Map<Long, Integer> storeEntriesMap = storeEntries.stream().collect(Collectors.toMap(StoreEntry::getStoreId, StoreEntry::getShopType));
        return storeEntriesMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> ShopTypeEnums.XSJM.getCode().equals(entry.getValue())
                ));

    }

    public Shop getByShopId(Long shopId) {
        return shopMapper.selectByPrimaryKey(shopId);
    }
}
