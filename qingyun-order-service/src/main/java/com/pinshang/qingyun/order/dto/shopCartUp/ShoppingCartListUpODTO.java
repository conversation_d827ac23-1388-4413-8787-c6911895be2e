package com.pinshang.qingyun.order.dto.shopCartUp;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2025/4/8
 */
public class ShoppingCartListUpODTO {

    TableData[] sites;

    public TableData[] getSites() {
        return sites;
    }
    public void setSites(TableData[] sites) {
        this.sites = sites;
    }

    public static class TableData implements Serializable{

        @ApiModelProperty("购物车信息")
        private TitleRow title;

        @ApiModelProperty("送货日期")
        private List<DeliveryBatchDay> deliveryBatchDays;

        @ApiModelProperty("配送批次")
        private List<List<DeliveryBatchEntry>> entrys;

        public List<List<DeliveryBatchEntry>> getEntrys() {
            return entrys;
        }

        public void setEntrys(List<List<DeliveryBatchEntry>> entrys) {
            this.entrys = entrys;
        }

        public TitleRow getTitle() {
            return title;
        }
        public void setTitle(TitleRow title) {
            this.title = title;
        }
        public List<DeliveryBatchDay> getDeliveryBatchDays() {
            return deliveryBatchDays;
        }
        public void setDeliveryBatchDays(List<DeliveryBatchDay> deliveryBatchDays) {
            this.deliveryBatchDays = deliveryBatchDays;
        }
    }

    //标题, 如清美-配送-仓库等等
    public static class TitleRow implements Serializable{
        private static final long serialVersionUID = -6088739460323149234L;
        //公司
        @ApiModelProperty("公司")
        private String companyName;

        //配送模式
        @ApiModelProperty("配送模式编码")
        private Integer logisticsModel;

        //配送模式
        @ApiModelProperty("配送模式")
        private String logisticsModelName;

        //仓库
        @ApiModelProperty("仓库")
        private String warehouseName;

        //供货时间
        @ApiModelProperty("订货时间")
        private String supplyTime;

        //商品金额
        @ApiModelProperty("金额合计")
        private BigDecimal totalPrice;

        //品类总数
        @ApiModelProperty("品类总数")
        private BigDecimal varietyTotal;

        //商品数量总数
        @ApiModelProperty("数量合计")
        private BigDecimal quantityTotal;

        //shoppingCartId
        @ApiModelProperty("购物车主ID")
        private String shoppingCartId;

        //是否在供应商时间+客户时间内
        @ApiModelProperty("是否可以提交订单：true不能 false:可以")
        private Boolean disable;

        @ApiModelProperty("默认订货日期")
        private String orderTime;

        @ApiModelProperty("开业日期")
        private Date openDate;

        private String deliveryBatch;

        @ApiModelProperty("鲜道订货固定一配")
        private String xdDeliveryBatch;

        /** 状态：2-开业前、0-暂停营业、1-营业中、3-永久停业，（原0-停用,1-启用） */
        @ApiModelProperty("状态：2-开业前、0-暂停营业、1-营业中、3-永久停业，（原0-停用,1-启用）")
        private Integer shopStatus;

        @ApiModelProperty("鲜道订货固定送货日期")
        private String xdOrderTime;
        @ApiModelProperty("店铺名字")
        private String shopName;

        private String adminDeliveryBatch;

        @ApiModelProperty("是否自动订货")
        private Boolean autoCommodity; // 是否自动订货
        private String stallName;// 档口名称

        public String getStallName() {
            return stallName;
        }

        public void setStallName(String stallName) {
            this.stallName = stallName;
        }

        public Boolean getAutoCommodity() {
            return autoCommodity;
        }

        public void setAutoCommodity(Boolean autoCommodity) {
            this.autoCommodity = autoCommodity;
        }

        public String getAdminDeliveryBatch() {
            return adminDeliveryBatch;
        }

        public void setAdminDeliveryBatch(String adminDeliveryBatch) {
            this.adminDeliveryBatch = adminDeliveryBatch;
        }
        public String getShopName() {
            return shopName;
        }

        public void setShopName(String shopName) {
            this.shopName = shopName;
        }

        public String getXdDeliveryBatch() {
            return xdDeliveryBatch;
        }

        public void setXdDeliveryBatch(String xdDeliveryBatch) {
            this.xdDeliveryBatch = xdDeliveryBatch;
        }

        public String getDeliveryBatch() {
            return deliveryBatch;
        }

        public void setDeliveryBatch(String deliveryBatch) {
            this.deliveryBatch = deliveryBatch;
        }

        public Integer getShopStatus() {
            return shopStatus;
        }

        public void setShopStatus(Integer shopStatus) {
            this.shopStatus = shopStatus;
        }

        public String getXdOrderTime() {
            return xdOrderTime;
        }

        public void setXdOrderTime(String xdOrderTime) {
            this.xdOrderTime = xdOrderTime;
        }

        public Date getOpenDate() {
            return openDate;
        }
        public void setOpenDate(Date openDate) {
            this.openDate = openDate;
        }
        public BigDecimal getVarietyTotal() {
            return varietyTotal;
        }

        public void setVarietyTotal(BigDecimal varietyTotal) {
            this.varietyTotal = varietyTotal;
        }

        public Integer getLogisticsModel() {
            return logisticsModel;
        }

        public void setLogisticsModel(Integer logisticsModel) {
            this.logisticsModel = logisticsModel;
        }

        public String getCompanyName() {
            return companyName;
        }
        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }
        public String getWarehouseName() {
            return warehouseName;
        }
        public void setWarehouseName(String warehouseName) {
            this.warehouseName = warehouseName;
        }
        public String getSupplyTime() {
            return supplyTime;
        }
        public void setSupplyTime(String supplyTime) {
            this.supplyTime = supplyTime;
        }
        public BigDecimal getTotalPrice() {
            if(null !=totalPrice){
                return  totalPrice.setScale(2,BigDecimal.ROUND_HALF_UP);
            }
            return totalPrice;
        }
        public void setTotalPrice(BigDecimal totalPrice) {
            this.totalPrice = totalPrice;
        }
        public BigDecimal getQuantityTotal() {
            if(null !=quantityTotal){
                return quantityTotal.setScale(2,BigDecimal.ROUND_HALF_UP);
            }
            return quantityTotal;
        }
        public void setQuantityTotal(BigDecimal quantityTotal) {
            this.quantityTotal = quantityTotal;
        }
        public String getLogisticsModelName() {
            return logisticsModelName;
        }
        public void setLogisticsModelName(String logisticsModelName) {
            this.logisticsModelName = logisticsModelName;
        }
        public String getShoppingCartId() {
            return shoppingCartId;
        }
        public void setShoppingCartId(String shoppingCartId) {
            this.shoppingCartId = shoppingCartId;
        }
        public Boolean getDisable() {
            return disable;
        }
        public void setDisable(Boolean disable) {
            this.disable = disable;
        }
        public String getOrderTime() {
            return orderTime;
        }
        public void setOrderTime(String orderTime) {
            this.orderTime = orderTime;
        }
    }
    public static class DeliveryBatchEntry  implements Serializable{
        private static final long serialVersionUID = 3864327178292821808L;
        private String optionName;
        private String optionCode;
        private String optionValue;
        private String memo;
        public String getOptionCode() {
            return optionCode;
        }
        public void setOptionCode(String optionCode) {
            this.optionCode = optionCode;
        }
        public String getOptionValue() {
            return optionValue;
        }
        public void setOptionValue(String optionValue) {
            this.optionValue = optionValue;
        }
        public String getMemo() {
            return memo;
        }
        public void setMemo(String memo) {
            this.memo = memo;
        }
        public String getOptionName() {
            return optionName;
        }
        public void setOptionName(String optionName) {
            this.optionName = optionName;
        }
    }
    public static class DeliveryBatchDay implements Serializable{
        private static final long serialVersionUID = 980745494135229662L;
        private String value;
        private String day;
        public String getValue() {
            return value;
        }
        public void setValue(String value) {
            this.value = value;
        }
        public String getDay() {
            return day;
        }
        public void setDay(String day) {
            this.day = day;
        }
    }
}
