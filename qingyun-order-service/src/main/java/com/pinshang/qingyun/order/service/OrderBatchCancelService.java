package com.pinshang.qingyun.order.service;


import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.kafka.KafkaConstant;
import com.pinshang.qingyun.kafka.MessageType;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.dto.xda.TdaOrderSyncTmsIDTO;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.mapper.OrderBillMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.SubOrderMapper;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.service.cup.ConsumableLimitService;
import com.pinshang.qingyun.order.service.giftLimit.GiftLimitService;
import com.pinshang.qingyun.order.service.xda.CouponDayStatisticsService;
import com.pinshang.qingyun.order.service.xda.OrderLimitQuantityService;
import com.pinshang.qingyun.order.service.xda.v4.OrderMtCouponService;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import com.pinshang.qingyun.order.service.xda.v4.ToBService;
import com.pinshang.qingyun.order.service.xda.v4.XdaOrderV4Service;
import com.pinshang.qingyun.order.vo.order.CancelVo;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import com.pinshang.qingyun.storage.service.DeliveryOrderClient;
import com.pinshang.qingyun.tms.service.WaybillClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderBatchCancelService {

    @Autowired
    OrderMapper orderMapper;

    @Autowired
    SubOrderMapper subOrderMapper;

    @Autowired
    TdaOrderService tdaOrderService;

    @Autowired
    private WaybillClient waybillClient;

    @Autowired
    private ToBService toBService;

    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;

    @Autowired
    private OrderHistoryService orderHistoryService;

    @Autowired
    StoreSettlementService storeSettlementService;

    @Autowired
    OrderBillMapper orderBillMapper;

    @Autowired
    DeliveryOrderClient deliveryOrderClient;

    @Autowired
    OrderService orderService;

    @Autowired
    ConsumableLimitService consumableLimitService;

    @Autowired
    OrderListService orderListService;

    @Autowired
    SplitOrderService splitOrderService;

    @Autowired
    WeChatSendMessageService weChatSendMessageService;

    @Autowired
    CommonService commonService;

    @Autowired
    OrderLimitQuantityService orderLimitQuantityService;

    @Autowired
    private StoreRechargeService storeRechargeService;

    @Autowired
    private OrderMtCouponService mtCouponService;

    @Autowired
    private CouponDayStatisticsService couponDayStatisticsService;
    @Autowired
    private SplitOrderSendKfkService splitOrderSendKfkService;

    @Autowired
    private GiftLimitService  giftLimitService;

    @Autowired
    private XdaOrderV4Service xdaOrderV4Service;

    @Transactional(rollbackFor = Exception.class)
    public Integer cancelOrder(Order order, User user) throws Exception {

        Long orderId = order.getId();
        QYAssert.isTrue(null != order , "订单不存在!");

        // 订单校验
        cancelOrderCheck(order);

        Integer rowNumber = doCancelOrder(order);

        //耗材商品取消
        orderCancelConsumableCommodity(user.getId(),order);

        // 记录订单日志
        orderHistoryService.insertOrderHistoryOnCancelOrderV2(order,user);

        //商品库存解冻
        if(!toBService.warehouseUnfreezeInventory(order.getId(), order.getStoreId(),order.getBusinessType())){
            QYAssert.isFalse("订单取消失败!");
        }

        // 返还优惠券
        mtCouponService.refundCoupon(order.getId(), order.getStoreId());

        // 归还买赠限总量
        giftLimitService.deleteGiftLimitQuantity(Arrays.asList(order.getId()));

        // 发送取消订单消息给统计查询
        log.info("start send kafka msg: orderId={} ", orderId);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                //取消的拆单消息
                SplitOrderKafkaVo splitOrderKafkaVo = new SplitOrderKafkaVo();
                splitOrderKafkaVo.setOrderId(orderId);
                splitOrderKafkaVo.setType(KafkaMessageOperationTypeEnum.CANCEL);
                splitOrderKafkaVo.setCreateId(order.getCreateId());
                splitOrderKafkaVo.setEnterpriseId(order.getEnterpriseId());
                splitOrderSendKfkService.sendSplitOrderKfkMsg(splitOrderKafkaVo);
                //splitOrderService.cancelSplitOrder(order.getId(),KafkaMessageOperationTypeEnum.CANCEL,
                 //       order.getCreateId(),order.getEnterpriseId(),splitOrderService);

                //给统计查询发送消息
                orderAsyncKafkaService.sendKafkaCancelOrder(order);

                //发送结算信息kafka
                List<CancelVo> cancelVos = new ArrayList<>();
                CancelVo cancelVo = new CancelVo();
                cancelVo.setSourceCode(order.getOrderCode());
                cancelVo.setSourceType("ORDER");
                cancelVos.add(cancelVo);
                commonService.sendKafkaMessage(cancelVos, MessageType.SETTLE_ORDER_CANCEL_SYNC, KafkaConstant.SETTLE_CANCEL_DATA_ALL_TOPIC, KafkaMessageOperationTypeEnum.CANCEL.getCode());

                // 通达订单或者大店订单取消给物流发消息
                if(orderService.orderBusinessTypeIsTda(order) || BusinessTypeEnums.BIGSHOP_SALE.getCode().equals(order.getBusinessType())){
                    // 发送消息，通知物流
                    TdaOrderSyncTmsIDTO tdaOrderSyncTmsIDTO = TdaOrderSyncTmsIDTO.forOrder(order.getId(), XdaOrderProcessStatusEunm.CANCEL.getCode());
                    tdaOrderService.tdaOrderSyncToTms(tdaOrderSyncTmsIDTO);
                }
                // 维护B端特价限购记录表
                orderLimitQuantityService.saveOrderLimitQuantity(order.getId(), OperateTypeEnums.删除.getCode(),  false);

                //订单退款
                orderRefund(user.getId(), order);

                couponDayStatisticsService.saveOrUpdateCouponDayStatistics(order.getId(), OperateTypeEnums.删除.getCode());


            }
        });

        
        
        return rowNumber;

    }

    /**
     * 订单退款
     *
     * @param userId 用户id
     * @param order  订单信息
     */
    public void orderRefund(Long userId, Order order) {
        // 更新预付款余额，订单回款记录对账明细
        Boolean preStore = storeRechargeService.isPreStore(order.getStoreId());
        //非预付费客户 不处理
        if (!Objects.equals(preStore, Boolean.TRUE)) {
            return;
        }
        String remark = "<--PC回款:" + order.getOrderCode() + " -->";
        int billType = StoreBillTypeEnums.PC_REFUNDMENT.getCode();

        //回款
        StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                .orderCode(order.getOrderCode())
                .tradeCode(order.getOrderCode())
                .money(order.getOrderAmount().doubleValue())
                .storeId(order.getStoreId())
                .tradeTime(new Date())
                .receiptDate(new Date())
                .billType(billType)
                .remark(remark)
                .userId(userId)
                .build();
        storeRechargeService.storeRecharge(rechargeBO);

        // 退还运费
        BigDecimal freightAmount = order.getFreightAmount();
        if (freightAmount != null && freightAmount.compareTo(BigDecimal.ZERO) > 0) {
            billType = StoreBillTypeEnums.PC_FREIGHT_DEDUCTION.getCode();
            remark = "<--PC回款（运费）:" + order.getOrderCode() + " -->";
            StoreRechargeBO rechargeFreightBO = StoreRechargeBO.builder()
                    .orderCode(order.getOrderCode())
                    // 运费的tradeCode加前缀，防止同一个tradeCode充值失败
                    .tradeCode("YF" + order.getOrderCode())
                    .money(freightAmount.doubleValue())
                    .storeId(order.getStoreId())
                    .tradeTime(new Date())
                    .receiptDate(new Date())
                    .billType(billType)
                    .remark(remark)
                    .userId(userId)
                    .build();
            storeRechargeService.storeRecharge(rechargeFreightBO);
        }
    }

    private Integer doCancelOrder(Order order) {

        Map<String,Object> paramMap = new HashMap<String, Object>(2);
        paramMap.put("orderId", order.getId());
        paramMap.put("orderStatus", 2);

        if(DeliveryOrderTypeEnums.TD_SALE.getCode().equals(order.getBusinessType()) ||
                DeliveryOrderTypeEnums.B_COUNTRY.getCode().equals(order.getBusinessType())){
            paramMap.put("processStatus", XdaOrderProcessStatusEunm.CANCEL.getCode());
        }
        Integer rowNumber = orderMapper.updateOrderStatusByParameter(paramMap);
        QYAssert.isTrue(rowNumber > 0 , "订单取消失败!");

        // 取消子订单
        Integer subOrderRows = subOrderMapper.cancelSubOrder(order.getId());
        QYAssert.isTrue(subOrderRows > 0 , "子订单取消失败!");

        return rowNumber;
    }

    /**
     * 耗材商品取消
     * @param userId
     * @param order
     */
    private void orderCancelConsumableCommodity(Long userId, Order order) {
        List<OrderList> orderLists = orderListService.findByOrderIdAndProductType(order.getId(), ProductTypeEnums.PRODUCT.getCode());
        orderLists = orderLists.stream().filter(orderList -> CombTypeEnum.COMB_CHILD.getCode().intValue() != orderList.getCombType().intValue()).collect(Collectors.toList());
        Map<Long,BigDecimal> productIdMap = orderLists
                .stream()
                .collect(Collectors.groupingBy(e->e.getCommodityId(),
                Collectors.reducing(BigDecimal.ZERO,OrderList::getCommodityNum,BigDecimal::subtract)));
        consumableLimitService.checkConsumableCommodityCancel(userId,order.getStoreId(),productIdMap);
    }

    private boolean addFreightAmount(Long storeId, BigDecimal freightAmount) {
        return addPaymentForOrder(storeId,freightAmount);
    }

    private boolean saveOrderReceivable(Order order, long userId,BigDecimal amount,String billRemark) {
        if(order != null && order.getStoreId() != null){
            StoreSettlement ss = storeSettlementService.findByStoreId(order.getStoreId());
            if(null != ss && ss.getCollectStatus() && ss.getCollectPrice() !=null){
                OrderBill ob = new OrderBill();
                ob.setStoreId(order.getStoreId());
                ob.setOrderId(order.getId());
                ob.setArAmount(BigDecimal.ZERO);
                ob.setPaAmount(amount);
                ob.setOrderTime(order.getOrderTime());
                ob.setBillRemark(billRemark);
                ob.setStoreBalance(BigDecimal.valueOf(ss.getCollectPrice()));
                ob.setCreateTime(new Date());
                ob.setCrateId(userId);
                int rowNum = orderBillMapper.insertSelective(ob);
                return rowNum > 0 ? true : false;

            }else{
                return true;
            }

        }else{
            return true;
        }
    }

    private boolean addPaymentForOrder(Long storeId, BigDecimal orderAmount) {
        return storeSettlementService.addCollectPrice(storeId,orderAmount);
    }


    private void cancelOrderCheck(Order order) throws Exception {
        // 通达销售
        // 通达订单取消订单，判断截单时间
        //tdaOrderService.cancelTdaOrderCheckDeliveryTimeRange(order.getStoreId(), order.getDeliveryTimeRange(), order.getDeliveryBatch());
        xdaOrderV4Service.cancelOrderProcessStatusCheck(order);

        Boolean isBigShop = BusinessTypeEnums.BIGSHOP_SALE.getCode().equals(order.getBusinessType());
        Boolean isTdaStore = DeliveryOrderTypeEnums.TD_SALE.getCode().equals(order.getBusinessType());
        boolean isBCountry = DeliveryOrderTypeEnums.B_COUNTRY.getCode().equals(order.getBusinessType());
        if(!(isTdaStore || isBCountry || isBigShop)) {

            if(null != order.getOrderDurationTime()){
                if(DateUtil.isBefore(order.getOrderDurationTime(),new Date())){
                    Map<Long, Boolean> longBooleanMap = deliveryOrderClient.checkOrderGeneratePickOrder(Collections.singletonList(order.getId()));

                    boolean flag = longBooleanMap.values().stream().anyMatch(item -> Boolean.TRUE.equals(item));
                    if(flag){
                        QYAssert.isFalse("大仓已执行发货，不能取消订单!");
                    }
                }
            }
        }

    }


}
