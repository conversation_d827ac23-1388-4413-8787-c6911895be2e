package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.GlobalSupplierNameConstant;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.dto.QuickGoodsResponseODTO;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.mapper.entry.order.DeliveryBatchEntry;
import com.pinshang.qingyun.order.mapper.entry.order.QuickGoodsEntry;
import com.pinshang.qingyun.order.mapper.entry.store.StoreDurationEntry;
import com.pinshang.qingyun.order.model.order.QuickGoods;
import com.pinshang.qingyun.order.model.order.QuickGoodsAdmin;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.util.OrderTimeUtil;
import com.pinshang.qingyun.order.util.StallUtils;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.order.FilterTipVo;
import com.pinshang.qingyun.order.vo.order.OrderRequestVo;
import com.pinshang.qingyun.order.vo.order.QuickGoodsVo;
import com.pinshang.qingyun.order.vo.order.QuickGoodsVo.QuickGoodsItemVo;
import com.pinshang.qingyun.order.vo.order.ShoppingCartVo;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.dto.bigShop.StallCommodityQueryIDTO;
import com.pinshang.qingyun.shop.dto.bigShop.StallODTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import com.pinshang.qingyun.xd.wms.dto.ShopCommodityStockDTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryIDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QuickGoodsService {
	private Logger logger = LoggerFactory.getLogger(getClass());
	@Autowired
	private QuickGoodsMapper quickGoodsMapper;

	@Autowired
	private QuickGoodsAdminMapper quickGoodsAdminMapper;
	
	@Autowired
	private ShoppingCartService shoppingCartService;

	@Autowired
	private RedissonClient redissonClient;
	@Autowired
	private ShopMapper shopMapper;

	@Autowired
	private ShoppingCartMapper shoppingCartMapper; 
	
	@Autowired
	private OrderMapper orderMapper;

	@Autowired
	private MdShopOrderSettingService mdShopOrderSettingService;


	@Autowired
	private ProductService productService;

	@Autowired
	private CommodityMapper commodityMapper;

	@Autowired
	private StoreMapper storeMapper;

	@Autowired
	private XdStockClient xdStockClient;
	@Autowired
	private ProductPriceModelClient productPriceModelClient;
	@Lazy
	@Autowired
	private QuickGoodsAdminService quickGoodsAdminService;
	@Autowired
	private ConsignmentSupplierService consignmentSupplierService;
	@Autowired
	private WeChatSendMessageService weChatSendMessageService;

	public List<QuickGoodsEntry> findQuickGoodsList(QuickGoodsVo vo){

		vo.setIfAdmin(vo.getIfAdmin() == null ? false : vo.getIfAdmin());
		vo.setBigShop(vo.getBigShop() == null ? false : vo.getBigShop());

		Boolean checkStoreId = !vo.getIfAdmin() && vo.getStoreId() == null;
		QYAssert.isTrue(!checkStoreId, "storeId不能为空");

		List<QuickGoodsEntry> list = quickGoodsMapper.findQuickGoodsNewList(vo);
		if(!CollectionUtils.isEmpty(list)){
			Set<Long> commodityIdList = list.stream().map(item -> item.getCommodityId()).collect(Collectors.toSet());
			Set<String> storeIdList = list.stream().map(item -> item.getStoreId() + "").collect(Collectors.toSet());

			List<Long> stallIdList = list.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
					.stream().map(item -> item.getStallId()).collect(Collectors.toList());
			Map<Long, StallODTO> stallIdAndNameMap = consignmentSupplierService.queryStallWholeMapByIds(stallIdList);

			// 查询大店库存
			Map<String, ShopCommodityInfoODTO> bigShopStockMap = new HashMap<>();
			if(!CollectionUtils.isEmpty(stallIdList)) {
				bigShopStockMap = consignmentSupplierService.queryBigShopCommodityStock(list.get(0).getShopId(), stallIdList, new ArrayList<>(commodityIdList));
			}

			Map<Long, ShopCommodityStockDTO> stockMap = new HashMap<>(list.size());
			if(CollectionUtils.isEmpty(stallIdList) || (!CollectionUtils.isEmpty(stallIdList) && stallIdList.size() < list.size())) {
				stockMap  = getXdStockMap(list.get(0).getShopId(),new ArrayList<>(commodityIdList));
			}

			// 获取进货价
			CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
			idto.setStoreIdList(new ArrayList<>(storeIdList));
			idto.setCommodityIdListAll(new ArrayList<>(commodityIdList));
			List<CommodityResultODTO> commodityResultList = productPriceModelClient.findStoreCommodityList(idto);
			Map<String,CommodityResultODTO> priceMap = new HashMap<>();
			if(!CollectionUtils.isEmpty(commodityResultList)){
				for(CommodityResultODTO commodityResult : commodityResultList){
					priceMap.put(commodityResult.getStoreId() + commodityResult.getCommodityId() , commodityResult);
				}
			}

			// 获取商品基础信息
			CommodityVO commodityVO = new CommodityVO();
			commodityVO.setCommodityIdList(new ArrayList<>(commodityIdList));
			List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
			Map<String, CommodityBasicEntry> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

			for(QuickGoodsEntry item : list){
				String key = item.getStoreId() + "" + item.getCommodityId();
				CommodityResultODTO commodityResultODTO = priceMap.get(key);
				if(commodityResultODTO != null){
					item.setPrice(commodityResultODTO.getCommodityPrice());
				}

				CommodityBasicEntry basicEntry = commMap.get(item.getCommodityId() + "");
				if(basicEntry != null){
					BeanUtils.copyProperties(basicEntry,item);
					if(item.getShopType().equals(ShopTypeEnums.XD.getCode()) || StallUtils.isStallSubcontractor(item.getManagementMode())){
						item.setSalesBoxCapacity(basicEntry.getXdSalesBoxCapacity());
					}
				}

				item.setShares(item.getQuantity().divide(item.getCommodityPackageSpec(),0,BigDecimal.ROUND_UP).longValue());
				ShopCommodityStockDTO shopCommodityStockDTO = stockMap.get(item.getCommodityId());
				item.setStockQuantity(null != shopCommodityStockDTO ? shopCommodityStockDTO.getStockQuantity() : BigDecimal.ZERO);

				if(item.getStallId() != null) {
					String stallCommKey = item.getStallId() + "_" + item.getCommodityId();
					ShopCommodityInfoODTO shopCommodityInfoODTO = bigShopStockMap.get(stallCommKey);
					if(shopCommodityInfoODTO != null) {
						item.setStockQuantity(shopCommodityInfoODTO.getStockQuantity());
					}
				}
				StallODTO stallODTO = stallIdAndNameMap.get(item.getStallId());
				if(stallODTO != null){
					item.setStallCode(stallODTO.getStallCode());
					item.setStallName(stallODTO.getStallName());
				}
			}

		}

		// 设置商品默认供应商的订货时间段 //配送的设置默认仓库时间
		if(vo.getIfAdmin()){
			setSupplyTime(list);
		}else {
			productService.setSupplyTime(vo.getStoreId(), list);
		}
		return list;
	}

	/**
	 * 获取xd库存
	 * @param shopId
	 * @param commodityIdList
	 * @return
	 */
	public Map<Long, ShopCommodityStockDTO> getXdStockMap(Long shopId,List<Long> commodityIdList){
		StockQueryIDTO queryDTO = new StockQueryIDTO();
		queryDTO.setWarehouseId(shopId);
		queryDTO.setCommodityList(commodityIdList);
		List<ShopCommodityStockDTO> stockList = xdStockClient.queryShopCommodityStock2(queryDTO);
		if(CollectionUtils.isEmpty(stockList)){
			return new HashMap<>(1024);
		}
       return stockList.stream().collect(Collectors.toMap(ShopCommodityStockDTO::getCommodityId, Function.identity()));
	}

	/**
	 * 代理进来设置订货时间段
	 * @param list
	 */
	public void setSupplyTime(List<QuickGoodsEntry> list){
		if (SpringUtil.isEmpty(list)) {
			return;
		}
		Map<Integer, List<MdShopOrderSettingEntry>> settingMap = new HashMap<>();
		Set<String> commodityIdList = list.stream().map(item -> item.getCommodityId() + "").collect(Collectors.toSet());
		Set<Integer> shopTypeList = list.stream().map(item -> item.getShopType()).collect(Collectors.toSet());
		for(Integer shopType : shopTypeList){
			List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByShopType(shopType, new ArrayList<>(commodityIdList));
		    if(!CollectionUtils.isEmpty(settingList)){
				settingMap.put(shopType, settingList);
			}
		}
		for(QuickGoodsEntry entry : list){
			/*List<String> commodityIds = new ArrayList<>();
			commodityIds.add(entry.getCommodityId() + "");
			List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(entry.getStoreId(), commodityIds);*/
			List<MdShopOrderSettingEntry> settingList = settingMap.get(entry.getShopType());
			if(!CollectionUtils.isEmpty(settingList)){
				Map<Long, MdShopOrderSettingEntry> settMap = settingList.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));
				MdShopOrderSettingEntry msSettingEntry = settMap.get(entry.getCommodityId());
				if(null != msSettingEntry){
					if (msSettingEntry.getLogisticsModel().intValue() == IogisticsModelEnums.DISPATCHING.getCode()) {
						entry.setSupplyStartTime(msSettingEntry.getDefaultWarehouseBeginTime());
						entry.setSupplyEndTime(msSettingEntry.getDefaultWarehouseEndTime());
					} else {
						entry.setSupplyStartTime(msSettingEntry.getDefaultSupplierBeginTime());
						entry.setSupplyEndTime(msSettingEntry.getDefaultSupplierEndTime());
					}
					if(StringUtils.isNotBlank(entry.getSupplyStartTime()) && StringUtils.isNotBlank(entry.getSupplyEndTime())){
						entry.setSupplyTime(entry.getSupplyStartTime() + "-" + entry.getSupplyEndTime());
					}
				}
			}

		}
	}
	public int deleteQuickGoodsByStoreId(QuickGoodsVo vo){
		Example ex =new Example(QuickGoods.class);
		ex.createCriteria().andEqualTo("storeId", vo.getStoreId());
		return quickGoodsMapper.deleteByExample(ex);
	}

	public Integer addShoppingCartAdmin(QuickGoodsVo vo){
		vo.setBigShop(vo.getBigShop() == null ? false : vo.getBigShop());
		RLock lock = redissonClient.getLock(LockConstants.batchCreateOrderAdmin);
		QYAssert.isTrue(!lock.isLocked(), "正在执行一键提交订单的操作，请稍后");

		List<QuickGoodsAdmin> list = quickGoodsAdminMapper.listGroup(1,vo.getCreateId(), vo.getBigShop());
		QYAssert.isTrue(null !=list && !list.isEmpty(), "快速添加到购物车符合条件的商品为0个,不进行添加购物车操作");
		List<Long> storeIdList = list.stream().map(item -> item.getStoreId()).collect(Collectors.toList());

		Example shopEx = new Example(Shop.class);
		shopEx.createCriteria().andIn("storeId", storeIdList);
		List<Shop> shopList = shopMapper.selectByExample(shopEx);
		Map<Long, Shop> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getStoreId, Function.identity()));

		Map<Long, List<QuickGoodsAdmin>> quickGoodsAdminMap = list.stream().collect(Collectors.groupingBy(QuickGoodsAdmin::getStoreId));
		// 获取线程池
		ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ADMIN_SAVE_ORDER_THREADPOOL);
		// 初始化和任务数量一致
		CountDownLatch countDownLatch = new CountDownLatch(quickGoodsAdminMap.size());

		List<FilterTipVo> filterTips =  Collections.synchronizedList(new ArrayList<>());
		for(Map.Entry<Long, List<QuickGoodsAdmin>> entry : quickGoodsAdminMap.entrySet()) {
			threadPool.execute(new Runnable() {
				@Override
				public void run() {
					try {
						Long storeId = entry.getKey();
						List<QuickGoodsAdmin> storeAdminList = entry.getValue();

						Shop shop = shopMap.get(storeId);
						// 判断是否前置仓
						Boolean isXd = shop.getShopType().equals(ShopTypeEnums.XD.getCode());
						ThreadLocalUtils.setXd(isXd);
						ThreadLocalUtils.setAdmin(true);
						ThreadLocalUtils.setShoppingCartSource(1);

						// 加购物车异常的商品idList
						List<Long> errorStoreCommIdList = new ArrayList<>();
						//调用订货功能批量加入购物车操作方法
						for(QuickGoodsAdmin q : storeAdminList){
							try {
								ShoppingCartVo cartVo = new ShoppingCartVo();
								cartVo.setCreateId(vo.getCreateId());
								cartVo.setStoreId(q.getStoreId());
								cartVo.setEnterpriseId(vo.getEnterpriseId());
								cartVo.setInternal(true);
								cartVo.setCommodityId(q.getCommodityId());
								cartVo.setQuantity(q.getQuantity());
								cartVo.setOrderTime(q.getOrderTime());
								cartVo.setDeliveryBatch(q.getDeliveryBatch());
								cartVo.setStallId(q.getStallId());
								shoppingCartService.addShoppingCart(cartVo);
							}catch (Exception e){
								log.error("代理快速订货加入购物车出错, storeId:{} commodityId:{}", storeId, q.getCommodityId(), e);

								// 返回异常
								if(StringUtils.isNotBlank(e.getMessage())) {
									FilterTipVo filterTipVo = new FilterTipVo();
									filterTipVo.setMsg(e.getMessage().length() > 100 ? e.getMessage().substring(0, 100) : e.getMessage());
									filterTips.add(filterTipVo);
								}
								errorStoreCommIdList.add(q.getCommodityId());
							}
						}

						ThreadLocalUtils.remove();

						// 删除代理快速订货数据,按客户
						Example ex =new Example(QuickGoodsAdmin.class);
						Example.Criteria c = ex.createCriteria();
						c.andEqualTo("pass", 1 ).andEqualTo("createId",vo.getCreateId()).andEqualTo("storeId", storeId);
						if(vo.getBigShop()){
							c.andGreaterThan("stallId", 0);
						}else {
							c.andEqualTo("stallId", -1);
						}
						if(!CollectionUtils.isEmpty(errorStoreCommIdList)){
							c.andNotIn("commodityId", errorStoreCommIdList);
						}
						quickGoodsAdminMapper.deleteByExample(ex);
					}finally {
						// 进行记时，当一个线程完成时候countDownLatch -1
						countDownLatch.countDown();
					}
				}
			});
		}

		// 阻塞主线程，当countDownLatch = 0时，线程都完成了
		try {
			countDownLatch.await();
		} catch (InterruptedException e) {
			log.warn("countDownLatch.await() error", e);
		}

		if(!CollectionUtils.isEmpty(filterTips)){
			List<String> errorMsg = filterTips.stream().map(item -> item.getMsg()).collect(Collectors.toList());
			QYAssert.isFalse("请刷新列表:  " + String.join("  ", errorMsg));
		}
		return 0;
	}

	@Transactional(rollbackFor = Exception.class)
	public Integer addShoppingCart(QuickGoodsVo vo){
		Example ex =new Example(QuickGoods.class);
		ex.createCriteria()
			.andEqualTo("storeId", vo.getStoreId())
			.andEqualTo("pass", 1 );
		List<QuickGoods> list =quickGoodsMapper.selectByExample(ex);
		QYAssert.isTrue(null !=list && !list.isEmpty(), "快速添加到购物车符合条件的商品为0个,不进行添加购物车操作");

		// 判断是否前置仓
		Example shopEx = new Example(Shop.class);
		shopEx.createCriteria().andEqualTo("storeId", vo.getStoreId());
		Shop shop = shopMapper.selectByExample(shopEx).get(0);
		Boolean isXd = shop.getShopType().equals(ShopTypeEnums.XD.getCode());
		if(isXd){
			ThreadLocalUtils.setShoppingCartSource(2);
		}else {
			ThreadLocalUtils.setShoppingCartSource(3);
		}
		ThreadLocalUtils.setXd(isXd);
		//调用订货功能批量加入购物车操作方法
		for(QuickGoods q : list){
			ShoppingCartVo cartVo = new ShoppingCartVo();
			cartVo.setCreateId(vo.getCreateId());
			cartVo.setStoreId(vo.getStoreId());
			cartVo.setEnterpriseId(vo.getEnterpriseId());
			cartVo.setInternal(true);
			cartVo.setCommodityId(q.getCommodityId());
			cartVo.setQuantity(q.getQuantity());
			cartVo.setStallId(q.getStallId());
			shoppingCartService.addShoppingCart(cartVo);
		}
		quickGoodsMapper.deleteByExample(ex);
		ThreadLocalUtils.remove();
		return list.size();
	}

	/**
	 * 代销香烟逻辑：代销和非代销分开订货；直送+代销商品+代销门店 必须满足， 否则  不允许导入
	 * @param vo
	 * @param commodityList
	 * @param storeList
	 * @param storeMap
	 * @param commodityMap
	 */
	private void saveQuickGoods4adminCheckConsignment(QuickGoodsVo vo, List<XDCommodityODTO> commodityList, List<Store> storeList, Map<String, Store> storeMap, Map<String, XDCommodityODTO> commodityMap) {
		List<Long> commodityIdList = commodityList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
		List<String> commodityIdStrList = commodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
		List<Long> storeIdList = storeList.stream().map(item -> item.getId()).collect(Collectors.toList());
		List<ConsignmentSupplierInfoODTO> consignmentSupplierList = consignmentSupplierService.queryConsignmentSupplierList(storeIdList, commodityIdList);

		if(!CollectionUtils.isEmpty(consignmentSupplierList)){
			List<Long> consignmentStoreId = consignmentSupplierList.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
			List<Long> consignmentCommodityId = consignmentSupplierList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

			// 直送+代销商品+代销门店 必须满足， 否则  不允许导入
			Map<Integer, List<MdShopOrderSettingEntry>> settingMap = new HashMap<>();
			Set<Integer> shopTypeList = storeList.stream().map(item -> item.getShopType()).collect(Collectors.toSet());
			for(Integer shopType : shopTypeList){
				List<MdShopOrderSettingEntry> settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByShopType(shopType, new ArrayList<>(commodityIdStrList));
				if(!CollectionUtils.isEmpty(settingList)){
					settingMap.put(shopType, settingList);
				}
			}

			// 判断是否都是直送商品
			Boolean isDirectSend = true;
			for(QuickGoodsItemVo itemVo : vo.getList()){
				Store store = storeMap.get(itemVo.getStoreCode());
				XDCommodityODTO commodityODTO = commodityMap.get(itemVo.getCommodityCode());
				List<MdShopOrderSettingEntry> settingList = settingMap.get(store.getShopType());
				if(!CollectionUtils.isEmpty(settingList)){
					Map<Long, MdShopOrderSettingEntry> settMap = settingList.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));
					MdShopOrderSettingEntry msSettingEntry = settMap.get(Long.valueOf(commodityODTO.getCommodityId()));
					if(null != msSettingEntry){
						if (msSettingEntry.getLogisticsModel().intValue() != IogisticsModelEnums.DIRECT_SENDING.getCode()) {
							isDirectSend = false;
							break;
						}
					}
				}
			}

			// 判断门店是否代销门店
			Boolean isConsignmentStore = true;
			for(Long storeId : storeIdList){
				if(!consignmentStoreId.contains(storeId)){
					isConsignmentStore = false;
					break;
				}
			}

			// 判断商品是否代销商品
			Boolean isConsignmentCommodity = true;
			for(Long commodityId : commodityIdList){
				if(!consignmentCommodityId.contains(commodityId)){
					isConsignmentCommodity = false;
					break;
				}
			}

			Boolean check = isDirectSend && isConsignmentStore && isConsignmentCommodity;
			QYAssert.isTrue( check ,"存在直送代销商品和非代销商品，请分别导入");

			// 如果是代销门店、并且是直送商品。则打个标记，可以导入直送的商品。
			String splitCode = "_";
			Map<String, List<ConsignmentSupplierInfoODTO>> consignmentCommodityMap = consignmentSupplierList.stream().collect(Collectors.groupingBy(item -> {
				return item.getStoreId() + splitCode + item.getCommodityId();
			}));
			for(QuickGoodsItemVo itemVo : vo.getList()){
				Store store = storeMap.get(itemVo.getStoreCode());
				XDCommodityODTO commodityODTO = commodityMap.get(itemVo.getCommodityCode());
				String key = store.getId() + splitCode + commodityODTO.getCommodityId();
				if(consignmentCommodityMap.containsKey(key)){
					itemVo.setConsignmentCommodity(true);
				}
			}
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean saveQuickGoods4admin(QuickGoodsVo vo){
		RLock lock = redissonClient.getLock(LockConstants.batchCreateOrderAdmin);
		QYAssert.isTrue(!lock.isLocked(), "正在执行一键提交订单的操作，请稍后");

		QYAssert.isTrue(vo.getList().size() < 1000 , "导入条数不能超过1000条");
		Set<String> storeCodeList = vo.getList().stream().map(item -> item.getStoreCode()).collect(Collectors.toSet());
		Set<String> commodityCodeList = vo.getList().stream().map(item -> item.getCommodityCode()).collect(Collectors.toSet());
		QYAssert.isTrue((storeCodeList.size() * commodityCodeList.size()) < 100000, "请联系管理员!");

		// 判断商品是否存在
		List<XDCommodityODTO> commodityList = commodityMapper.findCommodityInfoByCodes(new ArrayList<>(commodityCodeList));
		QYAssert.isTrue(org.apache.commons.collections.CollectionUtils.isNotEmpty(commodityList), "商品编码都不存在");
		List<String> productCodes = commodityList.stream().map(item -> item.getCommodityCode()).collect(Collectors.toList());
		commodityCodeList.forEach(item -> {
			QYAssert.isTrue(productCodes.contains(item), "导入失败，商品编码" + (StringUtils.isNotBlank(item) ? item : "") + "不存在");
		});
		Map<String, XDCommodityODTO> commodityMap = commodityList.stream().collect(Collectors.toMap(XDCommodityODTO::getCommodityCode, Function.identity()));

		List<Store> storeList = storeMapper.getStoreByCodeList(new ArrayList<>(storeCodeList));
		Map<String, Store> storeMap = storeList.stream().collect(Collectors.toMap(Store::getStoreCode, Function.identity()));
		List<DeliveryBatchEntry> deliveryBatchList = shoppingCartMapper.findDeliveryBatchByCode(null);
		Map<String, String> deliveryBatchMap = deliveryBatchList.stream().collect(Collectors.toMap(DeliveryBatchEntry::getOptionName,DeliveryBatchEntry::getOptionCode,(key1 , key2)-> key2));
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		Map<String, List<QuickGoodsVo.QuickGoodsItemVo>> quickGoodMap = vo.getList().stream().collect(Collectors.groupingBy(QuickGoodsVo.QuickGoodsItemVo::getStoreCode));
		for (String storeCode : quickGoodMap.keySet()) {
			Store store = storeMap.get(storeCode);
			QYAssert.isTrue(store != null, "客户编码"+storeCode+"不存在!");
			QYAssert.isTrue(store.getShopId() != null, "客户编码"+storeCode+"没有关联门店!");
		}

		// 如果是大店导入,需要判断档口编码
		Boolean isBigShop = (vo.getBigShop() == null ? false : vo.getBigShop());
		Map<String, StallODTO> stallCodeMap = new HashMap<>();
		if(isBigShop){
			Set<String> stallCodeList = vo.getList().stream().map(item -> item.getStallCode()).collect(Collectors.toSet());
			stallCodeMap = consignmentSupplierService.queryStallMapByCodes(new ArrayList<>(stallCodeList));
			QYAssert.isTrue(stallCodeMap != null && stallCodeMap.size() > 0, "档口编码都不存在");
		}

		int i = 2;
		for(QuickGoodsItemVo itemVo : vo.getList()){
			Date date = null;
			try {
				date = sdf.parse(itemVo.getOrderTime());
			} catch (Exception e) {
				QYAssert.isTrue(false, "送货日期格式不正确 yyyy-MM-dd");
			}
			QYAssert.isTrue(OrderTimeUtil.isValidDate(itemVo.getOrderTime()), "送货日期无效");
			QYAssert.isTrue(date.getTime() >= DateUtil.getNowDate().getTime(), "送货日期不能小于今日");
			QYAssert.isTrue(StringUtils.isNotBlank(deliveryBatchMap.get(itemVo.getDeliveryBatch())), "配送批次不正确");

			// 大店导入判断门店是否档口分包商
			if(isBigShop){
				if(!stallCodeMap.containsKey(itemVo.getStallCode())){
					QYAssert.isFalse("第" + i + "行档口编码不存在");
				}
				checkShopStall(itemVo, storeMap, stallCodeMap);
			}else {
				// 普通代理订货门店不能是大店
				Store store = storeMap.get(itemVo.getStoreCode());
				if(store != null && StallUtils.isStallSubcontractor(store.getManagementMode())) {
					QYAssert.isFalse("第" + i + "行大店客户编码需使用大店代理订货");
				}
			}
			i ++;
		}

		// 代销香烟逻辑：代销和非代销分开订货；直送+代销商品+代销门店 必须满足， 否则  不允许导入
		saveQuickGoods4adminCheckConsignment(vo, commodityList, storeList, storeMap, commodityMap);

		//  先删除，覆盖的方式
		quickGoodsMapper.deleteQuickGoodsAdmin(vo.getCreateId(), isBigShop);

		// 批量查询大店档口下面的商品
		Map<String, String> stallCommodityMap = new HashMap<>(vo.getList().size());
		if(isBigShop){
			List<Long> shopIdList = storeList.stream().map(item -> item.getShopId()).collect(Collectors.toList());
			Set<String> stallCodeList = vo.getList().stream().map(item -> item.getStallCode()).collect(Collectors.toSet());
			StallCommodityQueryIDTO queryIDTO = new StallCommodityQueryIDTO();
			queryIDTO.setShopIdList(shopIdList);
			queryIDTO.setStallCodeList(new ArrayList<>(stallCodeList));
			queryIDTO.setCommodityCodeList(new ArrayList<>(commodityCodeList));
			stallCommodityMap = consignmentSupplierService.queryStallCommodityMap(queryIDTO);
		}

		// 获取线程池
		ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ADMIN_SAVE_ORDER_THREADPOOL);

		long currentTimeMillis = System.currentTimeMillis();

		List<QuickGoodsResponseODTO> responseODTOList = Collections.synchronizedList(new ArrayList<>());
		// 初始化和任务数量一致
		CountDownLatch countDownLatch = new CountDownLatch(quickGoodMap.size());
		Map<String, StallODTO> finalStallCodeMap = stallCodeMap;
		Map<String, String> finalStallCommodityMap = stallCommodityMap;
		// 创建任务并提交到线程池中
		for (String storeCode : quickGoodMap.keySet()) {
			threadPool.execute(new Runnable() {
				@Override
				public void run() {
					try {
						Store store = storeMap.get(storeCode);
						QuickGoodsVo good = new QuickGoodsVo();
						good.setStoreId(store.getId());
						good.setShopId(store.getShopId());
						good.setIsInternal(true);
						good.setList(quickGoodMap.get(storeCode));
						good.setIfAdmin(true);
						good.setCreateId(vo.getCreateId());
						unifyDoSaveQuickGoods(good,store.getShopType(),commodityMap,deliveryBatchList,finalStallCodeMap,isBigShop,finalStallCommodityMap);
//						quickGoodsAdminService.saveQuickGoodsAdmin(good,store.getShopType(), commodityMap, deliveryBatchList, finalStallCodeMap, isBigShop, finalStallCommodityMap);
					} catch (Throwable e) {
						log.warn("代理导入快速订货异常，storeCode {} 异常信息 {}", storeCode, e.getMessage(), e);
						QuickGoodsResponseODTO responseODTO = new QuickGoodsResponseODTO();
						responseODTO.setStoreCode(storeCode);
						responseODTO.setMsg(e.getMessage());
						responseODTOList.add(responseODTO);
					}finally {
						// 进行记时，当一个线程完成时候countDownLatch -1
						countDownLatch.countDown();
					}
				}
			});
		}

		// 阻塞主线程，当countDownLatch = 0时，线程都完成了
		try {
			countDownLatch.await();
		} catch (InterruptedException e) {
			log.warn("countDownLatch.await() error", e);
		}

		if(!CollectionUtils.isEmpty(responseODTOList)){
			QuickGoodsResponseODTO responseODTO = responseODTOList.get(0);
			QYAssert.isTrue(false, "导入客户 " + responseODTO.getStoreCode() + "异常，错误信息 " + responseODTO.getMsg());
		}

		return true;
	}


	@Transactional(rollbackFor = Exception.class)
	public Boolean unifyDoSaveQuickGoods(QuickGoodsVo vo, Integer shopType, Map<String, XDCommodityODTO> commodityMap, List<DeliveryBatchEntry> deliveryBatchList,
									   Map<String, StallODTO> stallCodeMap , Boolean isBigShop, Map<String, String> stallCommodityMap){
		Boolean isXd = shopType.equals(ShopTypeEnums.XD.getCode());
		Boolean isJm = shopType.equals(ShopTypeEnums.XSJM.getCode());
		List<String> commodityIdStrList = new ArrayList<>();
		List<Long> commodityIdList = new ArrayList<>();
		List<XDCommodityODTO> commodityList = new ArrayList<>();
		for(QuickGoodsVo.QuickGoodsItemVo dto : vo.getList()){
			XDCommodityODTO commodityODTO = BeanCloneUtils.copyTo(commodityMap.get(dto.getCommodityCode()), XDCommodityODTO.class);
			commodityIdList.add(Long.valueOf(commodityODTO.getCommodityId()));
			commodityIdStrList.add(commodityODTO.getCommodityId());
			commodityODTO.setStallCode(dto.getStallCode());
			commodityList.add(commodityODTO);
		}

		// 获取商品价格、状态
		List<CommodityInfoEntry> commodityInfoEntries = queryCommodityInfoByStoreId(commodityList, commodityIdList, vo.getStoreId(), vo.getShopId());
		Map<String, List<CommodityInfoEntry>> entryMap = new HashMap<>(commodityInfoEntries.size());
		if(isBigShop) {
			entryMap = commodityInfoEntries.stream().collect(Collectors.groupingBy(item -> {
				return item.getStallCode() + "_" + item.getCommodityCode();
			}));
		}else {
			entryMap = commodityInfoEntries.stream().collect(Collectors.groupingBy(item -> {
				return item.getCommodityCode();
			}));
		}

		List<QuickGoods> models = new ArrayList<>();
		if(null !=vo && null !=vo.getList() && !vo.getList().isEmpty()){
			List<MdShopOrderSettingEntry>  settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(vo.getStoreId(), commodityIdStrList);
			Map<Long, MdShopOrderSettingEntry> settingMap = settingList.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));
			Map<String, String> deliveryBatchMap = deliveryBatchList.stream().collect(Collectors.toMap(DeliveryBatchEntry::getOptionName,DeliveryBatchEntry::getOptionCode,(key1 , key2)-> key2));
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");


			Date d =new Date();
			for(QuickGoodsVo.QuickGoodsItemVo dto:vo.getList()){
				StringBuilder remark = new StringBuilder();
				QuickGoods g = new QuickGoods();
				g.setPass(true);

				List<CommodityInfoEntry> list = new ArrayList<>();
				if(isBigShop) {
					list = entryMap.get(dto.getStallCode() + "_" + dto.getCommodityCode());
				}else {
					list = entryMap.get(dto.getCommodityCode());
				}
				if(org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
					continue;
				}

				if(isBigShop) {
					String key = vo.getShopId() + "" + dto.getStallCode() + "" + dto.getCommodityCode();
					if(!stallCommodityMap.containsKey(key)){
						remark.append("当前档口无此商品");
						g.setPass(Boolean.FALSE);
					}
				}


				CommodityInfoEntry t = list.get(0);
				MdShopOrderSettingEntry mdShopOrderSettingEntry = settingMap.get(t.getId());
				if(mdShopOrderSettingEntry == null){
					remark.append("门店订货通用设置不存在");
					g.setPass(Boolean.FALSE);
				}else {
					// 采用门店订货通用设置里面的供应商仓库信息
					g.setLogisticsModel(mdShopOrderSettingEntry.getLogisticsModel());
					t.setLogisticsModel(g.getLogisticsModel());
					t.setSupplierId(Long.valueOf(mdShopOrderSettingEntry.getSupplierId()));
					t.setSupplierStartTime(mdShopOrderSettingEntry.getDefaultSupplierBeginTime());
					t.setSupplierEndTime(mdShopOrderSettingEntry.getDefaultSupplierEndTime());
					if(mdShopOrderSettingEntry.getWarehouseId() != null) {
						t.setWarehouseId(Long.valueOf(mdShopOrderSettingEntry.getWarehouseId()));
						t.setWorkBeginTime(mdShopOrderSettingEntry.getDefaultWarehouseBeginTime());
						t.setWorkEndTime(mdShopOrderSettingEntry.getDefaultWarehouseEndTime());
					}else {
						// 仓库为空，更新仓库信息
						mdShopOrderSettingService.updateShopOrderSettingWarehouse(t.getId());
					}

				}

				if(!vo.getIsInternal() && vo.getIfAdmin()){
					g.setOrderTime(dto.getOrderTime());
					g.setDeliveryBatch(dto.getDeliveryBatch());

					Date date = null;
					try {
						date = sdf.parse(dto.getOrderTime());
					} catch (Exception e) {
						QYAssert.isTrue(false, "送货日期格式不正确 yyyy-MM-dd");
					}
					QYAssert.isTrue(OrderTimeUtil.isValidDate(dto.getOrderTime()), "送货日期无效");
					QYAssert.isTrue(date.getTime() >= DateUtil.getNowDate().getTime(), "送货日期不能小于今日");
					QYAssert.isTrue(StringUtils.isNotBlank(deliveryBatchMap.get(dto.getDeliveryBatch())), "配送批次不正确");

					if(mdShopOrderSettingEntry != null && mdShopOrderSettingEntry.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())){
						remark.append("直送商品");
						g.setPass(Boolean.FALSE);
					}
				}

				if(vo.getIsInternal() && vo.getIfAdmin()){
					g.setOrderTime(dto.getOrderTime());
					g.setDeliveryBatch(dto.getDeliveryBatch());
					if(dto.getConsignmentCommodity() != null && dto.getConsignmentCommodity()){
						// 总部代理订货可以导入直送商品(代销香烟)
					}else {
						if(mdShopOrderSettingEntry != null && mdShopOrderSettingEntry.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())){
							remark.append("直送商品");
							g.setPass(Boolean.FALSE);
						}
					}
				}

//				if(vo.getIfAdmin()){
//					g.setOrderTime(dto.getOrderTime());
//					g.setDeliveryBatch(dto.getDeliveryBatch());
//
//					Date date = null;
//					try {
//						date = sdf.parse(dto.getOrderTime());
//					} catch (Exception e) {
//						QYAssert.isTrue(false, "送货日期格式不正确 yyyy-MM-dd");
//					}
//					QYAssert.isTrue(OrderTimeUtil.isValidDate(dto.getOrderTime()), "送货日期无效");
//					QYAssert.isTrue(date.getTime() >= DateUtil.getNowDate().getTime(), "送货日期不能小于今日");
//					QYAssert.isTrue(StringUtils.isNotBlank(deliveryBatchMap.get(dto.getDeliveryBatch())), "配送批次不正确");
//
//
//					if(dto.getConsignmentCommodity() != null && dto.getConsignmentCommodity()){
//						// 总部代理订货可以导入直送商品(代销香烟)
//					}else {
//						if(mdShopOrderSettingEntry != null && mdShopOrderSettingEntry.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())){
//							remark.append("直送商品");
//							g.setPass(Boolean.FALSE);
//						}
//					}
//
//				}

				// 代理订货，不判断商品的可售状态（即停售的商品，也能下单）
				// 非代理订货需要判断
				if(!vo.getIfAdmin()){
					if(null != t.getCommodityStatus() && t.getCommodityStatus().intValue() == 0){
						remark.append("该商品已被禁用");
						g.setPass(Boolean.FALSE);
					}
				}

				// 鲜食加盟的不允许导入直送商品
				if(isJm){
					if(mdShopOrderSettingEntry != null && mdShopOrderSettingEntry.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())){
						remark.append("直送商品");
						g.setPass(Boolean.FALSE);
					}
				}

				g.setCommodityId(t.getId());
				g.setStoreId(vo.getStoreId());
				// 数量先保留两位小数
				dto.setQuantity(dto.getQuantity().setScale(2, BigDecimal.ROUND_HALF_UP));
				if(isXd){
					int num = dto.getQuantity().intValue();

					// 代理订货不校验小数
					if(!vo.getIfAdmin()){
						if(BigDecimal.valueOf(num).compareTo(dto.getQuantity()) < 0){
							remark.append("不是整数");
							g.setPass(Boolean.FALSE);
						}
					}

					BigDecimal commodityPackageSpec = BigDecimal.ONE;
					XDCommodityODTO xdCommodityPackageSpec = commodityMap.get(dto.getCommodityCode());
					if(xdCommodityPackageSpec != null && xdCommodityPackageSpec.getCommodityPackageSpec() != null){
						commodityPackageSpec = commodityMap.get(dto.getCommodityCode()).getCommodityPackageSpec();
					}
					// 代理快速订货，按照数量导入
					if(vo.getIfAdmin()){
						g.setQuantity(dto.getQuantity());
					}else {
						g.setQuantity(dto.getQuantity().multiply(commodityPackageSpec));
					}

				}else {
					g.setQuantity(dto.getQuantity());
				}

				g.setSupplierId(t.getSupplierId());
				g.setWarehouseId(t.getWarehouseId());
				g.setCreateTime(d);
				//检查客户价格方案->检查物流模式->检查供应商供货时间->检查仓库或供应商;

				XDCommodityODTO fpe = commodityMap.get(dto.getCommodityCode());
				// FrozenProductEntry fpe = this.quickGoodsMapper.findFrozenProductListByProductCode(dto.getCommodityCode());
				if(null != fpe){
					if(null != fpe.getSalesBoxCapacity() && fpe.getSalesBoxCapacity().compareTo(BigDecimal.ZERO) > 0){
						log.info(dto.getCommodityCode()+"-"+g.getQuantity());
						BigDecimal salesBoxCapacity = fpe.getSalesBoxCapacity();
						if(isXd || isBigShop){
							salesBoxCapacity = fpe.getXdSalesBoxCapacity();
						}
						if(null == salesBoxCapacity || salesBoxCapacity.compareTo(BigDecimal.ZERO) == 0){
							remark.append("箱规不存在或者为0");
							g.setPass(Boolean.FALSE);
						}else {
							if(g.getQuantity().divideAndRemainder(salesBoxCapacity)[1].compareTo(BigDecimal.ZERO) != 0){
								remark.append("不是箱规的倍数");
								g.setPass(Boolean.FALSE);
							}
						}
					}
				}
				// 代理订货，不判断商品的可售状态（即停售的商品，也能下单）
				if(!vo.getIfAdmin()){
					if(null != t.getCommodityStatus() && t.getCommodityStatus().intValue() == 0){
						remark.append("该商品已被禁用");
						g.setPass(Boolean.FALSE);
					}
				}

				if(null ==t.getCommodityId()){
					remark.append("不在价格方案中,");
					g.setPass(Boolean.FALSE);
				}
				//检查物流模式
				if(null ==t.getLogisticsModel()){
					remark.append("没有设置商品物流模式,");
					g.setPass(Boolean.FALSE);
				}
				if (null == t.getCommodityPurchaseStatus() || t.getCommodityPurchaseStatus() == 0) {
					remark.append("该商品不可采,");
					g.setPass(Boolean.FALSE);
				}
				if (null == t.getPrice() || BigDecimal.ZERO.equals(t.getPrice())) {
					remark.append("不在价格方案中,");
					g.setPass(Boolean.FALSE);
				}

				//检查仓库或供应商;
				if(null !=t.getLogisticsModel()){
					if(t.getLogisticsModel().intValue()== IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
						if(null ==t.getSupplierId()){
							g.setPass(Boolean.FALSE);
							remark.append("默认供应商未设置");
						}
						if(null ==t.getWarehouseId()){
							g.setPass(Boolean.FALSE);
							remark.append("默认仓库未设置");
						}
					}else if(t.getLogisticsModel().intValue()== IogisticsModelEnums.DIRECT_SENDING.getCode()){
						if(null ==t.getSupplierId()){
							g.setPass(Boolean.FALSE);
							remark.append("默认供应商未设置");
						}
					}else if(t.getLogisticsModel().intValue()== IogisticsModelEnums.DISPATCHING.getCode()){
						if(null ==t.getSupplierId()){
							g.setPass(Boolean.FALSE);
							remark.append("默认供应商未设置");
						}
						if(null ==t.getWarehouseId()){
							g.setPass(Boolean.FALSE);
							remark.append("默认仓库未设置");
						}
					}
				}

				//非代理订货需要判断时间
				if(!vo.getIsInternal() && mdShopOrderSettingEntry != null){

					if(mdShopOrderSettingEntry.getLogisticsModel().intValue()== IogisticsModelEnums.DIRECT_CONNECTION.getCode() || mdShopOrderSettingEntry.getLogisticsModel().intValue()== IogisticsModelEnums.DIRECT_SENDING.getCode()){
						Boolean b1 = null !=t.getSupplierStartTime() && null !=t.getSupplierEndTime();
						if(!b1){
							g.setPass(Boolean.FALSE);
							remark.append("该供应商供应时间段信息不全,请补充");
						}
						if(StringUtils.isNotBlank(t.getSupplierStartTime()) && StringUtils.isNotBlank(t.getSupplierEndTime())){
							if(!DateTimeUtil.compareNewDate(t.getSupplierStartTime(), t.getSupplierEndTime())){
								remark.append("超出供应商供货时间,无法操作!");
								g.setPass(Boolean.FALSE);
							}
						}
					}else if(mdShopOrderSettingEntry.getLogisticsModel().intValue() ==IogisticsModelEnums.DISPATCHING.getCode()){
						Boolean b1 =  null != t.getWorkBeginTime() && null != t.getWorkEndTime();
						if(!b1){
							g.setPass(Boolean.FALSE);
							remark.append(GlobalSupplierNameConstant.SUPPLIER_NAME+"该仓库供应时间段信息不全,请补充");
						}

						if(!DateTimeUtil.compareNewDate(t.getWorkBeginTime(), t.getWorkEndTime())){
							g.setPass(Boolean.FALSE);
							remark.append("超出仓库供货时间,无法操作!");
						}
					}
				}


				g.setRemark(remark.toString());
				g.setCreateId(vo.getCreateId());
				g.setConsignmentId(-1L);
				StallODTO stallODTO = stallCodeMap.get(dto.getStallCode());
				g.setStallId(stallODTO != null ? stallODTO.getId() : -1L);
				models.add(g);
			}
			if (vo.getIfAdmin()){
				quickGoodsMapper.insertListAdmin(models);
			} else {
				quickGoodsMapper.insertList(models);
			}
		}
		return true;
	}

	/**
	 * 大店导入判断门店是否档口分包商
	 * @param itemVo
	 * @param storeMap
	 * @param stallCodeMap
	 */
	private void checkShopStall(QuickGoodsItemVo itemVo, Map<String, Store> storeMap, Map<String, StallODTO> stallCodeMap) {
		Store store = storeMap.get(itemVo.getStoreCode());
		if(store != null){
			if(!StallUtils.isStallSubcontractor(store.getManagementMode())){
				QYAssert.isFalse("客户编码" + itemVo.getStoreCode() + "需使用普通代理订货");
			}

			if(stallCodeMap.containsKey(itemVo.getStallCode())){
				StallODTO stallODTO = stallCodeMap.get(itemVo.getStallCode());
				if(store.getShopId().compareTo(stallODTO.getShopId()) != 0){
					QYAssert.isFalse("档口编码" + itemVo.getStallCode() + ",不是客户编码" + itemVo.getStoreCode() + "所属档口");
				}
			}
		}
	}



	public void threadOver(ThreadPoolExecutor threadPool, long currentTimeMillis, String remark){
		try {
			boolean loop = true;
			do {
				//等待所有线程执行完毕当前任务结束
				loop = !threadPool.awaitTermination(2, TimeUnit.SECONDS);//等待2秒
			} while (loop);

			if (loop != true) {
				log.info(remark+ " 所有线程执行完毕");
			}

		} catch (InterruptedException e) {
			log.error(remark+" 线程执行时间异常",e);
		} finally {
			log.info(remark+" 耗时：" + (System.currentTimeMillis() - currentTimeMillis) + " 毫秒");
		}
	}
	//导入快速订货数据;
	@Transactional(rollbackFor = Exception.class)
	public Boolean saveQuickGoods(QuickGoodsVo vo){
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
		// 是否大店
		Boolean isBigShop = StallUtils.isStallSubcontractor(tokenInfo.getManagementMode());
		List<Long> stallCommodityIds = new ArrayList<>();
		if(isBigShop){
			stallCommodityIds = consignmentSupplierService.getCommodityIdListByStallIds(tokenInfo.getShopId(), vo.getStallId());
		}
		// 判断是否前置仓
		Example shopEx = new Example(Shop.class);
		shopEx.createCriteria().andEqualTo("storeId", vo.getStoreId());
		Shop shop = shopMapper.selectByExample(shopEx).get(0);
		vo.setShopId(shop.getId());

		//检查客户下单时间
		OrderRequestVo orderRequestVo =new OrderRequestVo();
		orderRequestVo.setStoreId(String.valueOf(vo.getStoreId()));
		orderRequestVo.setShopStoreId(vo.getStoreId());
		Boolean isInternal =vo.getIsInternal();
		if(null !=isInternal && !isInternal){
			//orderService.checkStoreOrderTime(orderRequestVo);
			StoreDurationEntry sd = this.orderMapper.findStoreDurationByStoreId(orderRequestVo.getStoreId());
			if(null != sd){
				if (StringUtils.isNotBlank(sd.getBeginTime()) && StringUtils.isNotBlank(sd.getEndTime())){
					if(!DateTimeUtil.compareNewDate(sd.getBeginTime(), sd.getEndTime())){
						QYAssert.isTrue(false, "超出订货时间,无法操作!");
					}
				}
			}
		}
		List<String> commodityCodes =new ArrayList<String>();
		if(null !=vo && null !=vo.getList() && !vo.getList().isEmpty()){
			for(QuickGoodsItemVo dto:vo.getList()){
				commodityCodes.add(dto.getCommodityCode());
			}
		}

		List<XDCommodityODTO> commodityList = commodityMapper.findCommodityInfoByCodes(commodityCodes);
		QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "商品编码都不存在");
		List<String> prodcutCodes = commodityList.stream().map(item -> item.getCommodityCode()).collect(Collectors.toList());
		commodityCodes.forEach(item -> {
			QYAssert.isTrue(prodcutCodes.contains(item), "导入失败，商品编码" + (StringUtils.isNotBlank(item) ? item : "") + "不存在");
		});

		Map<String, XDCommodityODTO> commodityMap = commodityList.stream().collect(Collectors.toMap(XDCommodityODTO::getCommodityCode, Function.identity()));

		List<DeliveryBatchEntry> deliveryBatchList = shoppingCartMapper.findDeliveryBatchByCode(null);
		Map<String, String> deliveryBatchMap = deliveryBatchList.stream().collect(Collectors.toMap(DeliveryBatchEntry::getOptionName,DeliveryBatchEntry::getOptionCode,(key1 , key2)-> key2));

		Map<String, StallODTO> stallCodeMap = new HashMap<>();
		if(isBigShop){
			stallCodeMap = consignmentSupplierService.selectStallMapByIds(Arrays.asList(vo.getStallId()));
			QYAssert.isTrue(stallCodeMap != null && stallCodeMap.size() > 0, "档口编码不存在");
		}

		Map<String, String> stallCommodityMap = new HashMap<>(vo.getList().size());
		if(isBigShop){
			List<Long> shopIdList = Arrays.asList(vo.getShopId());
			Set<String> stallCodeList = stallCodeMap.keySet();
			StallCommodityQueryIDTO queryIDTO = new StallCommodityQueryIDTO();
			queryIDTO.setShopIdList(shopIdList);
			queryIDTO.setStallCodeList(new ArrayList<>(stallCodeList));
			queryIDTO.setCommodityCodeList(new ArrayList<>(commodityCodes));
			stallCommodityMap = consignmentSupplierService.queryStallCommodityMap(queryIDTO);
		}

		unifyDoSaveQuickGoods(vo,shop.getShopType(),commodityMap,deliveryBatchList,stallCodeMap,isBigShop,stallCommodityMap);

		return true;
	}


	public List<CommodityInfoEntry> queryCommodityInfoByStoreId(List<XDCommodityODTO> list, List<Long> commodityIdList, Long storeId, Long shopId){
		//List<CommodityInfoEntry> commodityInfoEntries = quickGoodsMapper.queryCommodityInfoByStoreId(commodityIds, storeId);
		//List<CommodityInfoEntry> commodityInfoEntries = commodityMapper.queryCommodityInfoByCodeList(commodityIds);
		List<CommodityInfoEntry> commodityInfoEntries = new ArrayList<>();
		CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
		idto.setStoreId(storeId + "");
		idto.setCommodityIdListAll(commodityIdList);
		List<CommodityResultODTO> commodityResultList = productPriceModelClient.findStoreCommodityList(idto);

		Map<String, CommodityResultODTO> commodityResultMap = new HashMap<>();
		if(!CollectionUtils.isEmpty(commodityResultList)){
			commodityResultMap = commodityResultList.stream().collect(Collectors.toMap(CommodityResultODTO::getCommodityId, Function.identity()));
		}
		Map<Long, Integer> purchaseStatusMap = new HashMap<>();
		List<CommodityInfoEntry> commodityPurchaseStatusList = quickGoodsMapper.queryCommodityPurchaseStatus(commodityIdList,shopId);
		if(!CollectionUtils.isEmpty(commodityPurchaseStatusList)){
			purchaseStatusMap = commodityPurchaseStatusList.stream().collect(Collectors.toMap(CommodityInfoEntry::getCommodityId,CommodityInfoEntry::getCommodityPurchaseStatus,(key1 , key2)-> key2));
		}

		for(XDCommodityODTO xdCommodityODTO : list){
			CommodityInfoEntry infoEntry = new CommodityInfoEntry();
			infoEntry.setId(Long.valueOf(xdCommodityODTO.getCommodityId()));
			infoEntry.setCommodityId(Long.valueOf(xdCommodityODTO.getCommodityId()));
			infoEntry.setCommodityCode(xdCommodityODTO.getCommodityCode());
			infoEntry.setCommodityStatus(xdCommodityODTO.getCommodityStatus());
			infoEntry.setLogisticsModel(xdCommodityODTO.getLogisticsModel());

			CommodityResultODTO commodityResultODTO = commodityResultMap.get(infoEntry.getId() + "");
			if(commodityResultODTO != null){
				infoEntry.setPrice(commodityResultODTO.getCommodityPrice());
			}
			infoEntry.setCommodityPurchaseStatus(purchaseStatusMap.get(infoEntry.getId()));
			infoEntry.setStallCode(xdCommodityODTO.getStallCode());
			commodityInfoEntries.add(infoEntry);
		}

		return commodityInfoEntries;
	}

}
