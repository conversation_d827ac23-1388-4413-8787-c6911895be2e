package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.dto.commodity.ShoppingCartDetailHandleODto;
import com.pinshang.qingyun.order.dto.commodity.ShoppingCartHandleDetailODto;
import com.pinshang.qingyun.order.dto.commodity.ShoppingCartHandleDetailsODto;
import com.pinshang.qingyun.order.dto.order.DeliveryBatchODTO;
import com.pinshang.qingyun.order.dto.shopCartUp.ShoppingCartDetailUpODTO;
import com.pinshang.qingyun.order.dto.shopCartUp.ShoppingCartListUpODTO;
import com.pinshang.qingyun.order.dto.shopCartUp.ShoppingCartQueryIDTO;
import com.pinshang.qingyun.order.mapper.ShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.entry.order.DeliveryBatchEntry;
import com.pinshang.qingyun.order.model.order.ShoppingCart;
import com.pinshang.qingyun.order.vo.order.ShoppingCartAdminPageVo;
import com.pinshang.qingyun.order.vo.order.ShoppingCartDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2025/4/8
 */
@Slf4j
@Service
public class ShoppingCartUpService {

    @Autowired
    private ShoppingCartService shoppingCartService;
    @Autowired
    private ShoppingCartMapper shoppingCartMapper;

    /**
     * 总部登录、代理购物车列表
     * @param shoppingCartQueryIDTO
     * @return
     */
    public TablePageInfo<ShoppingCartDetailVo> shoppingCartAdminList(ShoppingCartQueryIDTO shoppingCartQueryIDTO) {
        ShoppingCartAdminPageVo shoppingCartAdminPageVo = BeanCloneUtils.copyTo(shoppingCartQueryIDTO, ShoppingCartAdminPageVo.class);

        ShoppingCartListUpODTO responseCart = new ShoppingCartListUpODTO();
        TablePageInfo<ShoppingCartDetailVo> shoppingCartPageInfo = shoppingCartService.queryShoppingCartDetailAdmin(shoppingCartAdminPageVo);

        ShoppingCartDetailVo shoppingCartDetailVo = (CollectionUtils.isEmpty(shoppingCartPageInfo.getList()) ? null : shoppingCartPageInfo.getList().get(0));
        if(null != shoppingCartDetailVo && null != shoppingCartDetailVo.getSites() && shoppingCartDetailVo.getSites().length > 0){
            //组装购物车返回数据(购物车列表)
            setShoppingCartResponse(shoppingCartDetailVo, responseCart);
        }

        shoppingCartPageInfo.setList(new ArrayList<>());
        shoppingCartPageInfo.setHeader(null == responseCart.getSites() ? new ShoppingCartHandleDetailsODto.TableData [0] : responseCart.getSites());
        return shoppingCartPageInfo;
    }


    /**
     * 门店、手持PDA购物车列表
     * @return
     */
    public Object shoppingCartList() {
        TokenInfo ti = FastThreadLocalUtil.getQY();
        Long storeId = ti.getStoreId();
        QYAssert.isTrue(null != storeId, "storeId不能为null");

        ShoppingCartListUpODTO responseCart = new ShoppingCartListUpODTO();
        ShoppingCartDetailVo shoppingCartDetailVo = shoppingCartService.shoppingCartDetail(storeId, ti.getIsInternal(),null,false, ti.getUserId(), null);

        if(null != shoppingCartDetailVo && null != shoppingCartDetailVo.getSites() && shoppingCartDetailVo.getSites().length > 0){
            //组装购物车返回数据(购物车列表)
            setShoppingCartResponse(shoppingCartDetailVo, responseCart);
        }

        return null == responseCart.getSites() ? new ShoppingCartHandleDetailsODto.TableData[0] : responseCart.getSites();
    }

    /**
     * 购物车列表返回数据组装
     * @param detailVo
     * @param responseCart
     */
    public void setShoppingCartResponse(ShoppingCartDetailVo detailVo, ShoppingCartListUpODTO responseCart){
        ShoppingCartListUpODTO.TableData [] sites=new ShoppingCartListUpODTO.TableData[detailVo.getSites().length];
        for(int i = 0; i < detailVo.getSites().length; i++ ){
            //原始购物车信息
            ShoppingCartDetailVo.TableData from = detailVo.getSites()[i];
            ShoppingCartDetailVo.TitleRow [] fromtitles = from.getTitle();
            List<ShoppingCartDetailVo.DeliveryBatchDay> fromdays = from.getDeliveryBatchDays();

            //构建新购物车信息
            ShoppingCartListUpODTO.TableData to = new ShoppingCartListUpODTO.TableData();
            ShoppingCartListUpODTO.TitleRow toTitle = BeanCloneUtils.copyTo(fromtitles[0], ShoppingCartListUpODTO.TitleRow.class);

            //送货日期
            List<ShoppingCartListUpODTO.DeliveryBatchDay> todays = new ArrayList<>();
            List<List<ShoppingCartListUpODTO.DeliveryBatchEntry>> entryList=new ArrayList();
            for(ShoppingCartDetailVo.DeliveryBatchDay fromday : fromdays){
                ShoppingCartListUpODTO.DeliveryBatchDay today = BeanCloneUtils.copyTo(fromday, ShoppingCartListUpODTO.DeliveryBatchDay.class);
                todays.add(today);

                //批次
                TokenInfo ti = FastThreadLocalUtil.getQY();
                List<DeliveryBatchEntry> bats;

                // 总部登录storeId为空(总部代理订货列表)，则取购物车中的storeId
                if(ti.getStoreId() == null){
                    String shoppingCartId = detailVo.getSites()[0].getTitle()[0].getShoppingCartId();
                    ShoppingCart shoppingCart = shoppingCartService.queryShoppingCartById(Long.parseLong(shoppingCartId));
                    QYAssert.isTrue(shoppingCart != null , "购物车不存在,请刷新!");
                    bats = shoppingCartService.findDeliveryBatch(shoppingCart.getStoreId(), today.getDay());
                }else{
                    bats = shoppingCartService.findDeliveryBatch(ti.getStoreId(), today.getDay());
                }

                List<ShoppingCartListUpODTO.DeliveryBatchEntry> dbeList = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(bats)){
                    for(DeliveryBatchEntry dbatch : bats){
                        ShoppingCartListUpODTO.DeliveryBatchEntry deliveryBatchEntry = BeanCloneUtils.copyTo(dbatch, ShoppingCartListUpODTO.DeliveryBatchEntry.class);
                        dbeList.add(deliveryBatchEntry);
                    }
                }
                entryList.add(dbeList);
            }
            to.setTitle(toTitle);
            to.setDeliveryBatchDays(todays);
            to.setEntrys(entryList);
            sites[i]=to;
        }
        responseCart.setSites(sites);
    }


    /**
     * 购物车详情
     * @param shoppingCartQueryIDTO
     * @return
     */
    public Object shoppingCartDetail(ShoppingCartQueryIDTO queryIDTO) {
        QYAssert.isTrue(null != queryIDTO.getShoppingCartId(), "购物车ID不能为null");

        ShoppingCartDetailUpODTO responseCart = new ShoppingCartDetailUpODTO();
        ShoppingCart shoppingCart = shoppingCartMapper.selectByPrimaryKey(queryIDTO.getShoppingCartId());
        QYAssert.isTrue(shoppingCart != null, "购物车数据异常,请刷新页面重试");

        ShoppingCartDetailVo shoppingCartDetail = shoppingCartService.shoppingCartDetail(shoppingCart.getStoreId(), queryIDTO.getIsInternal(), queryIDTO.getShoppingCartId(), queryIDTO.getHandle(), null, queryIDTO.getOrderTime());

        if(null != shoppingCartDetail && null != shoppingCartDetail.getSites() && shoppingCartDetail.getSites().length > 0 ){
            //组装购物车返回数据(单个购物车)
            setShoppingCartDetailResponse(shoppingCartDetail, responseCart);
        }

        return null == responseCart.getSites() ? "" : responseCart.getSites();
    }

    /**
     * 购物车详情返回数据组装
     * @param dto
     * @param responseCart
     */
    public void setShoppingCartDetailResponse(ShoppingCartDetailVo dto, ShoppingCartDetailUpODTO responseCart){
        ShoppingCartDetailUpODTO.TableData res = new ShoppingCartDetailUpODTO.TableData();
        //原始购物车信息
        ShoppingCartDetailVo.TableData from = dto.getSites()[0];
        ShoppingCartDetailVo.TitleRow [] fromtitle = from.getTitle();
        ShoppingCartDetailVo.CommodityRow [] fromcommoditys = from.getCommoditys();
        List<ShoppingCartDetailVo.DeliveryBatchDay> fromdays = from.getDeliveryBatchDays();

        //构建返回购物车信息
        ShoppingCartDetailUpODTO.TableData toTableData = new ShoppingCartDetailUpODTO.TableData();
        ShoppingCartDetailUpODTO.TitleRow totitle = BeanCloneUtils.copyTo(fromtitle[0], ShoppingCartDetailUpODTO.TitleRow.class);

        ShoppingCartDetailUpODTO.CommodityRow [] tocommodtys = new ShoppingCartDetailUpODTO.CommodityRow[fromcommoditys.length];
        for(int i = 0; i < fromcommoditys.length; i++ ){
            ShoppingCartDetailUpODTO.CommodityRow tocomm = BeanCloneUtils.copyTo(fromcommoditys[i], ShoppingCartDetailUpODTO.CommodityRow.class);
            tocommodtys[i] = tocomm;
        }

        List<List<ShoppingCartDetailUpODTO.DeliveryBatchEntry>> entryList=new ArrayList();
        List<ShoppingCartDetailUpODTO.DeliveryBatchDay> todays = new ArrayList<>();
        for(ShoppingCartDetailVo.DeliveryBatchDay fromday : fromdays){
            ShoppingCartDetailUpODTO.DeliveryBatchDay today = BeanCloneUtils.copyTo(fromday, ShoppingCartDetailUpODTO.DeliveryBatchDay.class);
            todays.add(today);

            //批次
            TokenInfo ti = FastThreadLocalUtil.getQY();
            List<DeliveryBatchEntry> bats;
            if(ti.getStoreId() == null){
                String shoppingCartId = dto.getSites()[0].getTitle()[0].getShoppingCartId();
                ShoppingCart shoppingCart = shoppingCartService.queryShoppingCartById(Long.parseLong(shoppingCartId));
                QYAssert.isTrue(shoppingCart != null , "购物车不存在,请刷新!");
                bats = shoppingCartService.findDeliveryBatch(shoppingCart.getStoreId(), today.getDay());
            }else{
                bats = shoppingCartService.findDeliveryBatch(ti.getStoreId(), today.getDay());
            }

            List<ShoppingCartDetailUpODTO.DeliveryBatchEntry> dbeList = new ArrayList<>();
            if(!org.springframework.util.CollectionUtils.isEmpty(bats)){
                for(DeliveryBatchEntry dbatch : bats){
                    ShoppingCartDetailUpODTO.DeliveryBatchEntry deliveryBatchEntry = BeanCloneUtils.copyTo(dbatch, ShoppingCartDetailUpODTO.DeliveryBatchEntry.class);
                    dbeList.add(deliveryBatchEntry);
                }
            }
            entryList.add(dbeList);
        }
        toTableData.setTitle(totitle);
        toTableData.setCommoditys(tocommodtys);
        toTableData.setDeliveryBatchDays(todays);
        toTableData.setEntrys(entryList);
        responseCart.setSites(toTableData);
    }
}
