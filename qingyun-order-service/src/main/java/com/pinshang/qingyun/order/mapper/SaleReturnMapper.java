package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.XdSaleReturnIDTO;
import com.pinshang.qingyun.order.dto.XdSaleReturnODTO;
import com.pinshang.qingyun.order.dto.finance.DirectDeliveryOrderIDTO;
import com.pinshang.qingyun.order.dto.finance.DirectDeliveryOrderODTO;
import com.pinshang.qingyun.order.dto.finance.ShopLessGoodsOrderIDTO;
import com.pinshang.qingyun.order.dto.finance.ShopLessGoodsOrderODTO;
import com.pinshang.qingyun.order.mapper.entry.order.SaleReturnEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SaleReturnForAuditEntry;
import com.pinshang.qingyun.order.model.order.SaleReturn;
import com.pinshang.qingyun.order.vo.order.SaleReturnVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface SaleReturnMapper extends MyMapper<SaleReturn> {

    List <SaleReturnEntry> getSaleReturnListByCondition(@Param("saleReturnVo") SaleReturnVo saleReturnVo);

    SaleReturnEntry getSaleReturnByCode(@Param("orderCode") String orderCode);

    @Update("update t_sale_return_order set status = #{status},update_id = #{userId},update_time = #{currentDate} where order_code = #{orderCode} and status = 1")
    int cancelSaleReturn(@Param("orderCode") String orderCode, @Param("status") int status,@Param("userId") Long userId,@Param("currentDate") Date currentDate);

    //    List<SaleReturnItemEntry> findCommodityByCode(SaleReturnCommodityVo vo);

    List <SaleReturnEntry> getSaleReturnSendList(@Param("saleReturnVo") SaleReturnVo saleReturnVo);

    List<XdSaleReturnODTO> xdReturnList(@Param("xdSaleReturnIDTO") XdSaleReturnIDTO xdSaleReturnIDTO);

    SaleReturnForAuditEntry getSaleReturnAuditByCode(@Param("orderCode") String orderCode);

    List<DirectDeliveryOrderODTO> selectDirectDeliveryReturnOrderList(@Param("vo") DirectDeliveryOrderIDTO idto);

    List<ShopLessGoodsOrderODTO> selectShopLessGoodsOrderList(@Param("vo") ShopLessGoodsOrderIDTO idto);
}