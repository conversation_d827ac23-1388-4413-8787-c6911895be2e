package com.pinshang.qingyun.order.service.xda.v4;

import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentManageConfig;
import com.pinshang.qingyun.infrastructure.springcloud.common.event.ComponentListener;
import com.pinshang.qingyun.infrastructure.test.BaseDbUnitTest;
import com.pinshang.qingyun.marketing.dto.app.CartODTO;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartV4ODTO;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.CommodityFreezeGroupEntry;
import com.pinshang.qingyun.order.model.order.XdaShoppingCart;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.model.store.StoreDuration;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.service.giftLimit.GiftLimitService;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.storage.dto.tob.ToBService;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import com.pinshang.qingyun.xda.product.service.XdaShoppingCartController;
import feign.RequestInterceptor;
import feign.auth.BasicAuthRequestInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * BuildXdaShoppingCartV4.shopCartList() 方法单元测试
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
//@Import(value = {BuildXdaShoppingCartV4.class})
public class BuildXdaShoppingCartV4Test extends BaseDbUnitTest {
    private TestDataFactory testDataFactory;

    private BuildXdaShoppingCartV4 buildXdaShoppingCartV4;

    @Autowired
    private XdaShoppingCartMapper xdaShoppingCartMapper;

    @Autowired
    private StoreDurationMapper storeDurationMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;

    @Autowired
    private OrderListMapper orderListMapper;

    @Autowired
    private OrderMapper orderMapper;

//    @Autowired
//    @Qualifier("storeService")
    @MockBean
    private StoreService storeService;

    @MockBean
    private GiftLimitService giftLimitService;

    @MockBean
    private XdaShoppingCartController xdaShoppingCartController;

    @MockBean
    private MtPromotionClient mtPromotionClient;

    @MockBean
    private ToBService toBService;

    @MockBean
    private XdaCommodityFrontClient xdaCommodityFrontClient;

    @MockBean
    IRenderService renderService;

    @MockBean
    RLock lock;

    @MockBean
    RedissonClient redissonClient;

    @MockBean
    private JavaMailSenderImpl javaMailSender;

    @MockBean
    WeChatSendMessageService weChatSendMessageService;

    @MockBean
    ComponentManageConfig componentManageConfig;

    @MockBean
    ComponentListener componentListener;

    @MockBean
    BasicAuthRequestInterceptor basicAuthRequestInterceptor;

    // 测试常量
    private static final Long TEST_STORE_ID = 123456L;
    private static final Long TEST_COUPON_USER_ID = 123L;
    private static final String username = "54293";
    private static final String password = "ykfy2780";

    @Bean
    public RequestInterceptor basicAuthRequestInterceptor() {
        if (StringUtils.isAnyBlank(username, password)) {
            return requestTemplate -> {
            }; // 空拦截器
        }
        return new BasicAuthRequestInterceptor(username, password);
    }

    @Override
    protected List<String> getInitSqlScripts() {
        return Arrays.asList(
                "schema.sql",
                "data.sql"
        );
    }

    /**
     * 创建 BuildXdaShoppingCartV4 实例
     */
    private BuildXdaShoppingCartV4 createBuildXdaShoppingCartV4() {
        Date deliveryDate = new Date();
        String deliveryTimeRange = "08:00-18:00";
        Integer deliveryBatch = 1;

        return new BuildXdaShoppingCartV4(
                TEST_STORE_ID,
                deliveryDate,
                toBService,
                xdaShoppingCartMapper,
                orderMapper,
                orderListMapper,
                xdaCommodityFrontClient,
                xdaShoppingCartController,
                storeService,
                storeDurationMapper,
                commodityFreezeGroupMapper,
                mtPromotionClient,
                deliveryTimeRange,
                deliveryBatch,
                redissonClient,
                TEST_COUPON_USER_ID
        );
    }

    /**
     * 测试空购物车场景
     */
    @Test
    public void testShopCartList_EmptyCart() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getSummation());
        assertEquals(BigDecimal.ZERO, result.getOriginalAmount());
        assertEquals(BigDecimal.ZERO, result.getDiscountTotal());
        assertTrue(result.getCanSettlement());
        assertNotNull(result.getTopTips());
        assertTrue(result.getTopTips().contains("08:00~18:00"));
    }

    /**
     * 测试基础购物车场景
     */
    @Test
    public void testShopCartList_BasicCart() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();
        setupBasicShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNormalGroup());
        assertTrue(result.getCanSettlement());
        assertTrue(result.getSummation().compareTo(BigDecimal.ZERO) >= 0);
        assertTrue(result.getOriginalAmount().compareTo(BigDecimal.ZERO) >= 0);
    }

    /**
     * 设置测试店铺数据
     */
    private void setupTestStore() {
        Store store = new Store();
        store.setId(TEST_STORE_ID);
        store.setMinDeliveryAmount(new BigDecimal("50.00"));
        store.setCreateTime(new Date());
        store.setUpdateTime(new Date());
        store.setStoreCode("TEST_STORE_001");
        store.setStoreName("测试店铺");
        store.setStoreStatus(1); // 正常状态
        store.setSettlementStatus(1L); // 设置合理的结算状态值
        store.setStoreTypeId(1L);
        store.setStoreLevelId(1L);
        store.setStoreCategoryId(1L);
        store.setStoreCompanyId(1L);
        store.setStoreDistrictId(1L);
        store.setStoreChannelId(1L);
        store.setStoreLineId(1L);
        storeMapper.insertSelective(store);
    }

    /**
     * 设置测试店铺时间段数据
     */
    private void setupTestStoreDuration() {
        StoreDuration storeDuration = new StoreDuration();
        storeDuration.setStoreId(TEST_STORE_ID);
        storeDuration.setBeginTime("08:00");
        storeDuration.setEndTime("18:00");
        storeDuration.setCreateTime(new Date());
        storeDuration.setUpdateTime(new Date());
        storeDuration.setPrintStatus(1); // 设置打印状态
        storeDurationMapper.insertSelective(storeDuration);
    }

    /**
     * 设置基础购物车数据
     */
    private void setupBasicShoppingCart() {
        XdaShoppingCart cart1 = new XdaShoppingCart();
        cart1.setStoreId(TEST_STORE_ID);
        cart1.setCommodityId(8107187447387601L);
        cart1.setQuantity(new BigDecimal("10"));
        cart1.setCommodityType(1);
        cart1.setCreateTime(new Date());
        xdaShoppingCartMapper.insertSelective(cart1);

        XdaShoppingCart cart2 = new XdaShoppingCart();
        cart2.setStoreId(TEST_STORE_ID);
        cart2.setCommodityId(2580787591198700L);
        cart2.setQuantity(new BigDecimal("5"));
        cart2.setCommodityType(1);
        cart2.setCreateTime(new Date());
        xdaShoppingCartMapper.insertSelective(cart2);
    }

    /**
     * Mock 外部服务
     */
    private void mockExternalServices() {
        // Mock 商品信息服务
        // Mock 促销服务
        // Mock 库存服务
        // Mock 其他服务
        // 简化实现，避免复杂的外部依赖
    }

    /**
     * 测试包含特惠商品的购物车
     */
    @Test
    public void testShopCartList_WithThCommodities() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();
        setupThShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getThGroups());
        assertTrue(result.getCanSettlement());
    }

    /**
     * 测试商品失效场景
     */
    @Test
    public void testShopCartList_WithInvalidCommodities() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();
        setupInvalidShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getInvalidateGroup());
    }

    /**
     * 测试无店铺时间段场景
     */
    @Test
    public void testShopCartList_NoStoreDuration() {
        // 准备测试数据
        setupTestStore();
        // 不设置店铺时间段
        setupBasicShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertEquals("", result.getTopTips());
    }

    /**
     * 设置特惠商品购物车数据
     */
    private void setupThShoppingCart() {
        XdaShoppingCart cart = new XdaShoppingCart();
        cart.setStoreId(TEST_STORE_ID);
        cart.setCommodityId(3333333333333333L);
        cart.setQuantity(new BigDecimal("8"));
        cart.setCommodityType(2); // 特惠商品
        cart.setCreateTime(new Date());
        xdaShoppingCartMapper.insertSelective(cart);
    }

    /**
     * 设置失效商品购物车数据
     */
    private void setupInvalidShoppingCart() {
        XdaShoppingCart cart = new XdaShoppingCart();
        cart.setStoreId(TEST_STORE_ID);
        cart.setCommodityId(9999999999999999L); // 不存在的商品ID
        cart.setQuantity(new BigDecimal("5"));
        cart.setCommodityType(1);
        cart.setCreateTime(new Date());
        xdaShoppingCartMapper.insertSelective(cart);
    }

    /**
     * 测试凑整商品场景
     */
    @Test
    public void testShopCartList_WithRoundingCommodities() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();
        setupRoundingShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getCanSettlement());
    }

    /**
     * 测试起送金额不足场景
     */
    @Test
    public void testShopCartList_InsufficientMinDeliveryAmount() {
        // 准备测试数据 - 设置较高的起送金额
        Store store = new Store();
        store.setId(TEST_STORE_ID);
        store.setMinDeliveryAmount(new BigDecimal("500.00")); // 设置高起送金额
        store.setCreateTime(new Date());
        store.setUpdateTime(new Date());
        store.setStoreCode("TEST_STORE_002");
        store.setStoreName("测试店铺2");
        store.setStoreStatus(1);
        store.setSettlementStatus(1L);
        store.setStoreTypeId(1L);
        store.setStoreLevelId(1L);
        store.setStoreCategoryId(1L);
        store.setStoreCompanyId(1L);
        store.setStoreDistrictId(1L);
        store.setStoreChannelId(1L);
        store.setStoreLineId(1L);
        storeMapper.insertSelective(store);

        setupTestStoreDuration();
        setupBasicShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getCanSettlement()); // 应该不能结算
        assertNotNull(result.getBottomTips()); // 应该有提示信息
        assertTrue(result.getBottomTips().contains("起送金额"));
    }

    /**
     * 设置凑整商品购物车数据
     */
    private void setupRoundingShoppingCart() {
        XdaShoppingCart cart = new XdaShoppingCart();
        cart.setStoreId(TEST_STORE_ID);
        cart.setCommodityId(4444444444444444L);
        cart.setQuantity(new BigDecimal("30")); // 凑整商品，30的倍数
        cart.setCommodityType(1);
        cart.setCreateTime(new Date());
        xdaShoppingCartMapper.insertSelective(cart);

        // 添加凑整商品配置
        CommodityFreezeGroupEntry freezeGroup = new CommodityFreezeGroupEntry();
        freezeGroup.setCommodityId(4444444444444444L);
        freezeGroup.setCreateTime(new Date());
        freezeGroup.setUpdateTime(new Date());
        commodityFreezeGroupMapper.insertSelective(freezeGroup);
    }

    /**
     * 测试优惠券场景
     */
    @Test
    public void testShopCartList_WithCoupon() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();
        setupBasicShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖 - 包含优惠券
        mockExternalServicesWithCoupon();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getCanSettlement());
        assertNotNull(result.getDiscountDetail());
        assertTrue(result.getCouponAmount().compareTo(BigDecimal.ZERO) > 0);
    }

    /**
     * Mock 包含优惠券的外部服务
     */
    private void mockExternalServicesWithCoupon() {
        // 简化实现，避免复杂的外部依赖
        // 在实际测试中，可以使用 Mockito 来 mock 外部服务
    }


    /**
     * 测试完整购物车场景
     */
    @Test
    public void testShopCartList_Cart() {
        Long storeId = 1L;
        Long commodityId = 1L;
        Integer commodityType = 1;
        // mock 购物车数据
        List<XdaShoppingCart> xdaShoppingCarts = buildXdaShoppingCartV4.queryShoppingCart(storeId, commodityId, commodityType);

        ShoppingCartV4ODTO shoppingCartV4ODTO = randomPojo(ShoppingCartV4ODTO.class, o -> {
            o.setCanSettlement(true);
        });


        Store store = randomPojo(Store.class, o -> {
            o.setId(storeId);
        });
        when(storeService.findStoreByStoreId(any())).thenReturn(store);

        buildXdaShoppingCartV4.buildCommodityInfo(xdaShoppingCarts);
    }

    @BeforeEach
    void setUp() {
        testDataFactory = new TestDataFactory();
        // 默认Mock设置
        setupDefaultMocks();
    }

    private void setupDefaultMocks() {
        // 默认返回空的促销结果
        when(mtPromotionClient.commodityForCart(any())).thenReturn(new CartODTO());
        // 默认返回空的库存结果
        when(toBService.getInventoryOrderCommodityList(any())).thenReturn(Collections.emptyList());
        // Mock凑整商品查询 - 返回商品ID列表
        when(commodityFreezeGroupMapper.selectOldCommodity(anyList()))
            .thenReturn(Arrays.asList(1001L, 1004L));
        // Mock商品规格查询
        when(xdaShoppingCartController.selectCommodityPackageSpecByCommodityIdList(any()))
            .thenReturn(createDefaultPackageSpecMap());
    }

    private Map<Long, BigDecimal> createDefaultPackageSpecMap() {
        Map<Long, BigDecimal> specMap = new HashMap<>();
        specMap.put(1001L, new BigDecimal("1.0"));
        specMap.put(1002L, new BigDecimal("1.0"));
        specMap.put(1003L, new BigDecimal("1.0"));
        specMap.put(1004L, new BigDecimal("1.0"));
        specMap.put(1005L, new BigDecimal("1.0"));
        specMap.put(1006L, new BigDecimal("1.0"));
        return specMap;
    }

    @Test
    void testBuildCommodityInfo_EmptyShoppingCart() {
        // Given: 空购物车
        Long storeId = 10001L;
        Date deliveryDate = new Date();
        buildXdaShoppingCartV4 = createBuildService(storeId, deliveryDate);

        Store store = testDataFactory.createStore(storeId, new BigDecimal("50"));
        when(storeService.findStoreByStoreId(storeId)).thenReturn(store);

        // When: 调用buildCommodityInfo
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.buildCommodityInfo(Collections.emptyList());

        // Then: 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCanSettlement()).isTrue(); // 空购物车默认可结算
        assertThat(result.getSummation()).isEqualTo(BigDecimal.ZERO);
        assertThat(result.getOriginalAmount()).isEqualTo(BigDecimal.ZERO);
        assertThat(result.getNormalGroup()).isNotNull();
        assertThat(result.getThGroups()).isNotNull();
    }

    @Test
    void testBuildCommodityInfo_NoValidCommodity() {
        // Given: 有购物车数据但无有效商品
        Long storeId = 10001L;
        Date deliveryDate = new Date();
        buildXdaShoppingCartV4 = createBuildService(storeId, deliveryDate);

        List<XdaShoppingCart> shoppingCarts = Arrays.asList(
                testDataFactory.createShoppingCart(1L, storeId, 1001L, 1, new BigDecimal("3.000"))
        );

        Store store = testDataFactory.createStore(storeId, new BigDecimal("50"));
        when(storeService.findStoreByStoreId(storeId)).thenReturn(store);

        // Mock: 商品服务返回空列表（无有效商品）
        when(xdaShoppingCartController.getShopCartList(any())).thenReturn(Collections.emptyList());

        // When: 调用buildCommodityInfo
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.buildCommodityInfo(shoppingCarts);

        // Then: 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCanSettlement()).isFalse(); // 无有效商品不可结算
        assertThat(result.getSummation()).isEqualTo(BigDecimal.ZERO);
    }

    @Test
    void testBuildCommodityInfo_BelowMinDeliveryAmount() {
        // Given: 购物车总价低于起送金额
        Long storeId = 10001L;
        Date deliveryDate = new Date();
        buildXdaShoppingCartV4 = createBuildService(storeId, deliveryDate);

        List<XdaShoppingCart> shoppingCarts = Arrays.asList(
                testDataFactory.createShoppingCart(1L, storeId, 1001L, 1, new BigDecimal("2.000"))
        );

        Store store = testDataFactory.createStore(storeId, new BigDecimal("50")); // 起送金额50元
        when(storeService.findStoreByStoreId(storeId)).thenReturn(store);

        // Mock: 返回低价商品信息
        List<XdaShoppingCartV3ODTO> commodityList = Arrays.asList(
                testDataFactory.createCommodityInfo(1001L, "低价商品", new BigDecimal("10.00"), true)
        );
        when(xdaShoppingCartController.getShopCartList(any())).thenReturn(commodityList);
        when(xdaShoppingCartController.selectCommodityPackageSpecByCommodityIdList(any()))
                .thenReturn(Collections.singletonMap(1001L, new BigDecimal("1.0")));

        // When: 调用buildCommodityInfo
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.buildCommodityInfo(shoppingCarts);

        // Then: 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCanSettlement()).isFalse(); // 未达起送金额不可结算
        assertThat(result.getBottomTips()).contains("起送金额为50元");
        assertThat(result.getSummation()).isLessThan(new BigDecimal("50"));
    }

    @Test
    void testBuildCommodityInfo_AboveMinDeliveryAmount() {
        // Given: 购物车总价高于起送金额
        Long storeId = 10001L;
        Date deliveryDate = new Date();
        buildXdaShoppingCartV4 = createBuildService(storeId, deliveryDate);

        List<XdaShoppingCart> shoppingCarts = Arrays.asList(
                testDataFactory.createShoppingCart(1L, storeId, 1001L, 1, new BigDecimal("6.000"))
        );

        Store store = testDataFactory.createStore(storeId, new BigDecimal("50")); // 起送金额50元
        when(storeService.findStoreByStoreId(storeId)).thenReturn(store);

        // Mock: 返回高价商品信息
        List<XdaShoppingCartV3ODTO> commodityList = Arrays.asList(
                testDataFactory.createCommodityInfo(1001L, "高价商品", new BigDecimal("15.00"), true)
        );
        when(xdaShoppingCartController.getShopCartList(any())).thenReturn(commodityList);
        when(xdaShoppingCartController.selectCommodityPackageSpecByCommodityIdList(any()))
                .thenReturn(Collections.singletonMap(1001L, new BigDecimal("1.0")));

        // When: 调用buildCommodityInfo
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.buildCommodityInfo(shoppingCarts);

        // Then: 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCanSettlement()).isTrue(); // 达到起送金额可结算
        assertThat(result.getBottomTips()).isNullOrEmpty();
        assertThat(result.getSummation()).isGreaterThanOrEqualTo(new BigDecimal("50"));
    }

    @Test
    void testBuildCommodityInfo_NoMinDeliveryAmount() {
        // Given: 店铺无起送金额限制
        Long storeId = 10002L;
        Date deliveryDate = new Date();
        buildXdaShoppingCartV4 = createBuildService(storeId, deliveryDate);

        List<XdaShoppingCart> shoppingCarts = Arrays.asList(
                testDataFactory.createShoppingCart(1L, storeId, 1001L, 1, new BigDecimal("1.000"))
        );

        Store store = testDataFactory.createStore(storeId, null); // 无起送金额
        when(storeService.findStoreByStoreId(storeId)).thenReturn(store);

        // Mock: 返回商品信息
        List<XdaShoppingCartV3ODTO> commodityList = Arrays.asList(
                testDataFactory.createCommodityInfo(1001L, "普通商品", new BigDecimal("10.00"), true)
        );
        when(xdaShoppingCartController.getShopCartList(any())).thenReturn(commodityList);
        when(xdaShoppingCartController.selectCommodityPackageSpecByCommodityIdList(any()))
                .thenReturn(Collections.singletonMap(1001L, new BigDecimal("1.0")));

        // When: 调用buildCommodityInfo
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.buildCommodityInfo(shoppingCarts);

        // Then: 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCanSettlement()).isTrue(); // 无起送金额限制，可结算
        assertThat(result.getBottomTips()).isNullOrEmpty();
    }

    @Test
    void testBuildCommodityInfo_WithStoreDuration() {
        // Given: 店铺有订货时间段配置
        Long storeId = 10001L;
        Date deliveryDate = new Date();
        buildXdaShoppingCartV4 = createBuildService(storeId, deliveryDate);

        // 这个测试需要实际的StoreDurationMapper查询，所以需要真实的数据库数据
        // 在data.sql中已经为store_id=10001插入了时间段数据

        List<XdaShoppingCart> shoppingCarts = Arrays.asList(
                testDataFactory.createShoppingCart(1L, storeId, 1001L, 1, new BigDecimal("6.000"))
        );

        Store store = testDataFactory.createStore(storeId, new BigDecimal("30"));
        when(storeService.findStoreByStoreId(storeId)).thenReturn(store);

        // Mock: 返回商品信息
        List<XdaShoppingCartV3ODTO> commodityList = Arrays.asList(
                testDataFactory.createCommodityInfo(1001L, "普通商品", new BigDecimal("15.00"), true)
        );
        when(xdaShoppingCartController.getShopCartList(any())).thenReturn(commodityList);
        when(xdaShoppingCartController.selectCommodityPackageSpecByCommodityIdList(any()))
                .thenReturn(Collections.singletonMap(1001L, new BigDecimal("1.0")));

        // When: 调用buildCommodityInfo
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.buildCommodityInfo(shoppingCarts);

        // Then: 验证结果
        assertThat(result).isNotNull();
        // 注意：topTips的验证需要根据实际的getStoreDuration方法实现来调整
    }

    @Test
    void testBuildCommodityInfo_CompleteFlow() {
        // Given: 完整的业务流程测试
        Long storeId = 10001L;
        Date deliveryDate = new Date();
        buildXdaShoppingCartV4 = createBuildService(storeId, deliveryDate);

        List<XdaShoppingCart> shoppingCarts = Arrays.asList(
                testDataFactory.createShoppingCart(1L, storeId, 1001L, 1, new BigDecimal("3.000")), // 普通商品
                testDataFactory.createShoppingCart(2L, storeId, 1002L, 2, new BigDecimal("2.000"))  // 特惠商品
        );

        Store store = testDataFactory.createStore(storeId, new BigDecimal("50"));
        when(storeService.findStoreByStoreId(storeId)).thenReturn(store);

        // Mock: 返回完整的商品信息
        List<XdaShoppingCartV3ODTO> commodityList = Arrays.asList(
                testDataFactory.createCommodityInfo(1001L, "苹果", new BigDecimal("20.00"), true),
                testDataFactory.createSpecialCommodityInfo(1002L, "特惠香蕉", new BigDecimal("15.00"), new BigDecimal("12.00"), true)
        );
        when(xdaShoppingCartController.getShopCartList(any())).thenReturn(commodityList);
        when(xdaShoppingCartController.selectCommodityPackageSpecByCommodityIdList(any()))
                .thenReturn(new HashMap<Long, BigDecimal>() {{
                    put(1001L, new BigDecimal("1.0"));
                    put(1002L, new BigDecimal("1.0"));
                }});

        // Mock: 特惠门槛
        when(xdaCommodityFrontClient.findOrderTargetByStoreIdAndOrderTime(any()))
                .thenReturn(new BigDecimal("50.00"));

        // Mock: 促销活动
        CartODTO cartODTO = testDataFactory.createPromotionResult();
        when(mtPromotionClient.commodityForCart(any())).thenReturn(cartODTO);

        // When: 调用buildCommodityInfo
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.buildCommodityInfo(shoppingCarts);

        // Then: 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCanSettlement()).isTrue();
        assertThat(result.getSummation()).isGreaterThan(BigDecimal.ZERO);
        assertThat(result.getOriginalAmount()).isGreaterThan(BigDecimal.ZERO);
        assertThat(result.getNormalGroup().getCommodities()).isNotEmpty();
        assertThat(result.getVarietySum()).isGreaterThan(0);
        assertThat(result.getCommodityNum()).isGreaterThan(0);
    }

    private BuildXdaShoppingCartV4 createBuildService(Long storeId, Date deliveryDate) {
        return new BuildXdaShoppingCartV4(
                storeId, deliveryDate, toBService, xdaShoppingCartMapper, orderMapper, orderListMapper,
                xdaCommodityFrontClient, xdaShoppingCartController, storeService, storeDurationMapper,
                commodityFreezeGroupMapper, mtPromotionClient, "09:00-18:00", 1, redissonClient, null
        );
    }

    // 测试数据工厂类
    static class TestDataFactory {

        Store createStore(Long storeId, BigDecimal minDeliveryAmount) {
            Store store = new Store();
            store.setId(storeId);
            store.setStoreCode("STORE" + storeId);
            store.setMinDeliveryAmount(minDeliveryAmount);
            return store;
        }

        XdaShoppingCart createShoppingCart(Long id, Long storeId, Long commodityId, Integer commodityType, BigDecimal quantity) {
            XdaShoppingCart cart = new XdaShoppingCart();
            cart.setId(id);
            cart.setStoreId(storeId);
            cart.setCommodityId(commodityId);
            cart.setCommodityType(commodityType);
            cart.setQuantity(quantity);
            return cart;
        }

        XdaShoppingCartV3ODTO createCommodityInfo(Long commodityId, String name, BigDecimal price, Boolean canOrder) {
            XdaShoppingCartV3ODTO commodity = new XdaShoppingCartV3ODTO();
            commodity.setCommodityId(commodityId);
            commodity.setCommodityName(name);
            commodity.setCommodityPrice(price);
            commodity.setIsCanOrder(canOrder);
            commodity.setProductType(1); // 单品
            commodity.setCommodityUnitName("个");
            commodity.setIsSpecialPrice(0); // 默认无特价
            commodity.setSpecialPrice(null);
            commodity.setIsThPrice(0); // 默认无特惠价
            commodity.setThPrice(null);
            return commodity;
        }

        XdaShoppingCartV3ODTO createSpecialCommodityInfo(Long commodityId, String name, BigDecimal price, BigDecimal thPrice, Boolean canOrder) {
            XdaShoppingCartV3ODTO commodity = createCommodityInfo(commodityId, name, price, canOrder);
            commodity.setThPrice(thPrice);
            commodity.setIsThPrice(1);
            return commodity;
        }

        CartODTO createPromotionResult() {
            CartODTO cartODTO = new CartODTO();
            cartODTO.setAvailableNumber(1);
            cartODTO.setCouponDiscount(new BigDecimal("5.00"));
            cartODTO.setValidateGroup(Collections.emptyList());
            cartODTO.setPromotionGroups(Collections.emptyList());
            return cartODTO;
        }
    }
}
