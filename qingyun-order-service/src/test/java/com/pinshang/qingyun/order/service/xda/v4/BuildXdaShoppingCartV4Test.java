package com.pinshang.qingyun.order.service.xda.v4;

import com.pinshang.qingyun.ApplicationOrderService;
import com.pinshang.qingyun.infrastructure.springcloud.common.ComponentManageConfig;
import com.pinshang.qingyun.infrastructure.springcloud.common.event.ComponentListener;
import com.pinshang.qingyun.infrastructure.test.BaseDbUnitTest;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartV4ODTO;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.CommodityFreezeGroupEntry;
import com.pinshang.qingyun.order.model.order.XdaShoppingCart;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.model.store.StoreDuration;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import com.pinshang.qingyun.xda.product.service.XdaShoppingCartController;
import feign.RequestInterceptor;
import feign.auth.BasicAuthRequestInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.test.context.TestPropertySource;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BuildXdaShoppingCartV4.shopCartList() 方法单元测试
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,classes = {ApplicationOrderService.class,BaseDbUnitTest.Application.class},
        properties = {"application.name.switch=dev-three-","pinshang.img-server-url=xxx",
                "pinshang.img-xd-server-url=xxx","print.path.rootPath=xxx","print.path.upPath=xxx",
                "print.path.url=xxx","pinshang.xda.isThTips=xxx"})
//@Import(value = {BuildXdaShoppingCartV4.class})
@TestPropertySource(locations = "classpath:application-test.properties")
public class BuildXdaShoppingCartV4Test extends BaseDbUnitTest {

    private BuildXdaShoppingCartV4 buildXdaShoppingCartV4;

    @Autowired
    private XdaShoppingCartMapper xdaShoppingCartMapper;

    @Autowired
    private StoreDurationMapper storeDurationMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;

    @Autowired
    private OrderListMapper orderListMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private StoreService storeService;

    @MockBean
    private XdaShoppingCartController xdaShoppingCartController;

    @MockBean
    private MtPromotionClient mtPromotionClient;

    @MockBean
    private ToBService toBService;

    @MockBean
    private XdaCommodityFrontClient xdaCommodityFrontClient;

    @MockBean
    IRenderService renderService;

    @MockBean
    RLock lock;

    @MockBean
    RedissonClient redissonClient;

    @MockBean
    private JavaMailSenderImpl javaMailSender;

    @MockBean
    WeChatSendMessageService weChatSendMessageService;

    @MockBean
    ComponentManageConfig componentManageConfig;

    @MockBean
    ComponentListener componentListener;

    @MockBean
    BasicAuthRequestInterceptor basicAuthRequestInterceptor;

    // 测试常量
    private static final Long TEST_STORE_ID = 123456L;
    private static final Long TEST_COUPON_USER_ID = 123L;
    private static final String username = "54293";
    private static final String password = "ykfy2780";

    @Bean
    public RequestInterceptor basicAuthRequestInterceptor() {
        if (StringUtils.isAnyBlank(username, password)) {
            return requestTemplate -> {}; // 空拦截器
        }
        return new BasicAuthRequestInterceptor(username, password);
    }

    @Override
    protected List<String> getInitSqlScripts() {
        return Arrays.asList(
                "db/xda_order.sql"
        );
    }

    /**
     * 创建 BuildXdaShoppingCartV4 实例
     */
    private BuildXdaShoppingCartV4 createBuildXdaShoppingCartV4() {
        Date deliveryDate = new Date();
        String deliveryTimeRange = "08:00-18:00";
        Integer deliveryBatch = 1;

        return new BuildXdaShoppingCartV4(
                TEST_STORE_ID,
                deliveryDate,
                toBService,
                xdaShoppingCartMapper,
                orderMapper,
                orderListMapper,
                xdaCommodityFrontClient,
                xdaShoppingCartController,
                storeService,
                storeDurationMapper,
                commodityFreezeGroupMapper,
                mtPromotionClient,
                deliveryTimeRange,
                deliveryBatch,
                redissonClient,
                TEST_COUPON_USER_ID
        );
    }

    /**
     * 测试空购物车场景
     */
    @Test
    public void testShopCartList_EmptyCart() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getSummation());
        assertEquals(BigDecimal.ZERO, result.getOriginalAmount());
        assertEquals(BigDecimal.ZERO, result.getDiscountTotal());
        assertTrue(result.getCanSettlement());
        assertNotNull(result.getTopTips());
        assertTrue(result.getTopTips().contains("08:00~18:00"));
    }

    /**
     * 测试基础购物车场景
     */
    @Test
    public void testShopCartList_BasicCart() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();
        setupBasicShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNormalGroup());
        assertTrue(result.getCanSettlement());
        assertTrue(result.getSummation().compareTo(BigDecimal.ZERO) >= 0);
        assertTrue(result.getOriginalAmount().compareTo(BigDecimal.ZERO) >= 0);
    }

    /**
     * 设置测试店铺数据
     */
    private void setupTestStore() {
        Store store = new Store();
        store.setId(TEST_STORE_ID);
        store.setMinDeliveryAmount(new BigDecimal("50.00"));
        store.setCreateTime(new Date());
        store.setUpdateTime(new Date());
        store.setStoreCode("TEST_STORE_001");
        store.setStoreName("测试店铺");
        store.setStoreStatus(1); // 正常状态
        store.setSettlementStatus(1L); // 设置合理的结算状态值
        store.setStoreTypeId(1L);
        store.setStoreLevelId(1L);
        store.setStoreCategoryId(1L);
        store.setStoreCompanyId(1L);
        store.setStoreDistrictId(1L);
        store.setStoreChannelId(1L);
        store.setStoreLineId(1L);
        storeMapper.insertSelective(store);
    }

    /**
     * 设置测试店铺时间段数据
     */
    private void setupTestStoreDuration() {
        StoreDuration storeDuration = new StoreDuration();
        storeDuration.setStoreId(TEST_STORE_ID);
        storeDuration.setBeginTime("08:00");
        storeDuration.setEndTime("18:00");
        storeDuration.setCreateTime(new Date());
        storeDuration.setUpdateTime(new Date());
        storeDuration.setPrintStatus(1); // 设置打印状态
        storeDurationMapper.insertSelective(storeDuration);
    }

    /**
     * 设置基础购物车数据
     */
    private void setupBasicShoppingCart() {
        XdaShoppingCart cart1 = new XdaShoppingCart();
        cart1.setStoreId(TEST_STORE_ID);
        cart1.setCommodityId(8107187447387601L);
        cart1.setQuantity(new BigDecimal("10"));
        cart1.setCommodityType(1);
        cart1.setCreateTime(new Date());
        xdaShoppingCartMapper.insertSelective(cart1);

        XdaShoppingCart cart2 = new XdaShoppingCart();
        cart2.setStoreId(TEST_STORE_ID);
        cart2.setCommodityId(2580787591198700L);
        cart2.setQuantity(new BigDecimal("5"));
        cart2.setCommodityType(1);
        cart2.setCreateTime(new Date());
        xdaShoppingCartMapper.insertSelective(cart2);
    }

    /**
     * Mock 外部服务
     */
    private void mockExternalServices() {
        // Mock 商品信息服务
        // Mock 促销服务
        // Mock 库存服务
        // Mock 其他服务
        // 简化实现，避免复杂的外部依赖
    }

    /**
     * 测试包含特惠商品的购物车
     */
    @Test
    public void testShopCartList_WithThCommodities() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();
        setupThShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getThGroups());
        assertTrue(result.getCanSettlement());
    }

    /**
     * 测试商品失效场景
     */
    @Test
    public void testShopCartList_WithInvalidCommodities() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();
        setupInvalidShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getInvalidateGroup());
    }

    /**
     * 测试无店铺时间段场景
     */
    @Test
    public void testShopCartList_NoStoreDuration() {
        // 准备测试数据
        setupTestStore();
        // 不设置店铺时间段
        setupBasicShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertEquals("", result.getTopTips());
    }

    /**
     * 设置特惠商品购物车数据
     */
    private void setupThShoppingCart() {
        XdaShoppingCart cart = new XdaShoppingCart();
        cart.setStoreId(TEST_STORE_ID);
        cart.setCommodityId(3333333333333333L);
        cart.setQuantity(new BigDecimal("8"));
        cart.setCommodityType(2); // 特惠商品
        cart.setCreateTime(new Date());
        xdaShoppingCartMapper.insertSelective(cart);
    }

    /**
     * 设置失效商品购物车数据
     */
    private void setupInvalidShoppingCart() {
        XdaShoppingCart cart = new XdaShoppingCart();
        cart.setStoreId(TEST_STORE_ID);
        cart.setCommodityId(9999999999999999L); // 不存在的商品ID
        cart.setQuantity(new BigDecimal("5"));
        cart.setCommodityType(1);
        cart.setCreateTime(new Date());
        xdaShoppingCartMapper.insertSelective(cart);
    }

    /**
     * 测试凑整商品场景
     */
    @Test
    public void testShopCartList_WithRoundingCommodities() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();
        setupRoundingShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getCanSettlement());
    }

    /**
     * 测试起送金额不足场景
     */
    @Test
    public void testShopCartList_InsufficientMinDeliveryAmount() {
        // 准备测试数据 - 设置较高的起送金额
        Store store = new Store();
        store.setId(TEST_STORE_ID);
        store.setMinDeliveryAmount(new BigDecimal("500.00")); // 设置高起送金额
        store.setCreateTime(new Date());
        store.setUpdateTime(new Date());
        store.setStoreCode("TEST_STORE_002");
        store.setStoreName("测试店铺2");
        store.setStoreStatus(1);
        store.setSettlementStatus(1L);
        store.setStoreTypeId(1L);
        store.setStoreLevelId(1L);
        store.setStoreCategoryId(1L);
        store.setStoreCompanyId(1L);
        store.setStoreDistrictId(1L);
        store.setStoreChannelId(1L);
        store.setStoreLineId(1L);
        storeMapper.insertSelective(store);

        setupTestStoreDuration();
        setupBasicShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖
        mockExternalServices();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getCanSettlement()); // 应该不能结算
        assertNotNull(result.getBottomTips()); // 应该有提示信息
        assertTrue(result.getBottomTips().contains("起送金额"));
    }

    /**
     * 设置凑整商品购物车数据
     */
    private void setupRoundingShoppingCart() {
        XdaShoppingCart cart = new XdaShoppingCart();
        cart.setStoreId(TEST_STORE_ID);
        cart.setCommodityId(4444444444444444L);
        cart.setQuantity(new BigDecimal("30")); // 凑整商品，30的倍数
        cart.setCommodityType(1);
        cart.setCreateTime(new Date());
        xdaShoppingCartMapper.insertSelective(cart);

        // 添加凑整商品配置
        CommodityFreezeGroupEntry freezeGroup = new CommodityFreezeGroupEntry();
        freezeGroup.setCommodityId(4444444444444444L);
        freezeGroup.setCreateTime(new Date());
        freezeGroup.setUpdateTime(new Date());
        commodityFreezeGroupMapper.insertSelective(freezeGroup);
    }

    /**
     * 测试优惠券场景
     */
    @Test
    public void testShopCartList_WithCoupon() {
        // 准备测试数据
        setupTestStore();
        setupTestStoreDuration();
        setupBasicShoppingCart();

        // 创建测试实例
        buildXdaShoppingCartV4 = createBuildXdaShoppingCartV4();

        // Mock 外部依赖 - 包含优惠券
        mockExternalServicesWithCoupon();

        // 执行测试
        ShoppingCartV4ODTO result = buildXdaShoppingCartV4.shopCartList();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getCanSettlement());
        assertNotNull(result.getDiscountDetail());
        assertTrue(result.getCouponAmount().compareTo(BigDecimal.ZERO) > 0);
    }

    /**
     * Mock 包含优惠券的外部服务
     */
    private void mockExternalServicesWithCoupon() {
        // 简化实现，避免复杂的外部依赖
        // 在实际测试中，可以使用 Mockito 来 mock 外部服务
    }
}
