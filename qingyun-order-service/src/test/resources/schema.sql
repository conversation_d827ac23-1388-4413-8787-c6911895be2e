-- schema.sql (H2数据库表结构)
DROP TABLE IF EXISTS t_xda_shopping_cart;
DROP TABLE IF EXISTS t_store;
DROP TABLE IF EXISTS t_store_duration;
DROP TABLE IF EXISTS t_commodity_freeze_group;
DROP TABLE IF EXISTS t_order_list;
DROP TABLE IF EXISTS t_commodity;

-- 购物车表
CREATE TABLE t_xda_shopping_cart (
  id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  store_id BIGINT NOT NULL,
  commodity_id BIGINT NOT NULL,
  commodity_type INT NOT NULL DEFAULT 1,
  quantity DECIMAL(10,3) NOT NULL,
  create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 店铺表
CREATE TABLE t_store (
  id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  enterprise_id BIGINT,
  store_code VARCHAR(50),
  store_name VARCHAR(100),
  store_status SMALLINT DEFAULT -1,
  min_delivery_amount DECIMAL(9,0),
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_id BIGINT NOT NULL,
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 店铺订货时间段表
CREATE TABLE t_store_duration (
  id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  begin_time VARCHAR(20),
  end_time VARCHAR(20),
  store_id BIGINT,
  store_code VARCHAR(20),
  create_time DATETIME,
  update_time DATETIME,
  create_id BIGINT,
  create_name VARCHAR(30)
);

-- 商品表
CREATE TABLE t_commodity (
  id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  commodity_code VARCHAR(50),
  commodity_name VARCHAR(100),
  commodity_state SMALLINT DEFAULT 1,
  commodity_price DECIMAL(10,2),
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 凑整商品配置表
CREATE TABLE t_commodity_freeze_group (
  id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  commodity_id BIGINT NOT NULL,
  create_id BIGINT NOT NULL,
  create_time DATETIME NOT NULL,
  update_id BIGINT NOT NULL,
  update_time DATETIME NOT NULL
);

-- 订单明细表
CREATE TABLE t_order_list (
  id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  order_id BIGINT,
  commodity_id BIGINT,
  commodity_num DECIMAL(19,3),
  real_quantity DECIMAL(14,3) DEFAULT 0.000,
  original_price DECIMAL(14,2),
  special_price DECIMAL(14,3),
  commodity_price DECIMAL(19,2),
  total_price DECIMAL(15,2) DEFAULT 0.00,
  type SMALLINT DEFAULT 1,
  promotion_id BIGINT,
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
