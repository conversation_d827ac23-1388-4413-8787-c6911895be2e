-- H2 测试数据库表结构
-- 为 BuildXdaShoppingCartV4Test 单元测试创建

-- 店铺表
CREATE TABLE t_store (
                         id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                         enterprise_id BIGINT DEFAULT NULL,
                         store_code VARCHAR(50) DEFAULT NULL,
                         store_name VARCHAR(100) DEFAULT NULL,
                         store_short_name VARCHAR(100) DEFAULT NULL,
                         store_aid VARCHAR(100) DEFAULT NULL,
                         store_old_code VARCHAR(50) DEFAULT NULL,
                         store_destribe VARCHAR(1000) DEFAULT NULL,
                         store_type_id BIGINT DEFAULT NULL,
                         uscc VARCHAR(100) DEFAULT NULL,
                         store_level_id BIGINT DEFAULT NULL,
                         store_category_id BIGINT DEFAULT NULL,
                         process_status SMALLINT DEFAULT 0,
                         store_status SMALLINT DEFAULT -1,
                         store_open_time TIMESTAMP DEFAULT NULL,
                         store_close_time TIMESTAMP DEFAULT NULL,
                         store_company_id BIGINT DEFAULT NULL,
                         department_id BIGINT DEFAULT NULL,
                         store_district_id BIGINT DEFAULT NULL,
                         settlement_customer_id BIGINT DEFAULT NULL,
                         store_channel_id BIGINT DEFAULT NULL,
                         store_line_id BIGINT DEFAULT NULL,
                         lower_rate DECIMAL(16,2) DEFAULT NULL,
                         store_is_prepay SMALLINT DEFAULT NULL,
                         store_is_billing SMALLINT DEFAULT NULL,
                         deliveryman_id BIGINT DEFAULT NULL,
                         salesman_id BIGINT DEFAULT NULL,
                         supervisor_id BIGINT DEFAULT NULL,
                         office_director_id BIGINT DEFAULT NULL,
                         deposit DECIMAL(16,2) DEFAULT NULL,
                         region_manager_id BIGINT DEFAULT NULL,
                         store_is_alert SMALLINT DEFAULT NULL,
                         order_close_is_alert SMALLINT DEFAULT NULL,
                         order_method VARCHAR(32) DEFAULT NULL,
                         first_alert_time VARCHAR(50) DEFAULT NULL,
                         alert_times INT DEFAULT NULL,
                         alert_interval VARCHAR(50) DEFAULT NULL,
                         stop_monile_time VARCHAR(20) DEFAULT NULL,
                         net_order_time VARCHAR(20) DEFAULT NULL,
                         tel_stop_order VARCHAR(20) DEFAULT NULL,
                         not_order_remark VARCHAR(1000) DEFAULT NULL,
                         print_copies DECIMAL(16,2) DEFAULT NULL,
                         print_backup VARCHAR(100) DEFAULT NULL,
                         print_order_title VARCHAR(100) DEFAULT NULL,
                         show_price_retail SMALLINT DEFAULT NULL,
                         money_is_show SMALLINT DEFAULT NULL,
                         order_sign_in SMALLINT DEFAULT NULL,
                         max_mobile_order DECIMAL(16,2) DEFAULT NULL,
                         max_order DECIMAL(16,2) DEFAULT NULL,
                         country_id BIGINT DEFAULT NULL,
                         province_id BIGINT DEFAULT NULL,
                         city_id BIGINT DEFAULT NULL,
                         area_id BIGINT DEFAULT NULL,
                         town_id BIGINT DEFAULT NULL,
                         detail_address VARCHAR(100) DEFAULT NULL,
                         business_circles_id BIGINT DEFAULT NULL,
                         store_address VARCHAR(100) DEFAULT NULL,
                         delivery_address VARCHAR(100) DEFAULT NULL,
                         store_linkman VARCHAR(100) DEFAULT NULL,
                         linkman_tel VARCHAR(100) DEFAULT NULL,
                         linkman_mobile VARCHAR(100) DEFAULT NULL,
                         linkman_fax VARCHAR(100) DEFAULT NULL,
                         linkman_email VARCHAR(100) DEFAULT NULL,
                         receive_time VARCHAR(32) DEFAULT '无',
                         is_test_report SMALLINT DEFAULT 1,
                         test_report_remark VARCHAR(100) DEFAULT NULL,
                         is_receive_consumables SMALLINT DEFAULT NULL,
                         kingdee_interface VARCHAR(100) DEFAULT NULL,
                         is_quick_freeze SMALLINT DEFAULT NULL,
                         quick_freeze_amount DECIMAL(16,2) DEFAULT NULL,
                         print_delivery_batch VARCHAR(50) DEFAULT NULL,
                         settlement_remark VARCHAR(50) DEFAULT NULL,
                         print_delivery_queue INT DEFAULT NULL,
                         create_user_id BIGINT DEFAULT NULL,
                         create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                         update_id BIGINT NOT NULL DEFAULT 0,
                         update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                         settlement_status BIGINT DEFAULT -1,
                         rewriteable INT NOT NULL DEFAULT 1,
                         store_type SMALLINT DEFAULT 2,
                         down_price_status INT DEFAULT 1,
                         min_delivery_amount DECIMAL(9,0) DEFAULT NULL,
                         is_recharge BIGINT DEFAULT 1,
                         baidu_longitude DECIMAL(12,6) DEFAULT NULL,
                         baidu_latitude DECIMAL(12,6) DEFAULT NULL,
                         business_type TINYINT DEFAULT NULL,
                         business_license_url VARCHAR(255) DEFAULT NULL,
                         store_photo_url VARCHAR(255) DEFAULT NULL,
                         id_card_people_url VARCHAR(255) DEFAULT NULL,
                         id_card_national_url VARCHAR(255) DEFAULT NULL,
                         id_card VARCHAR(255) DEFAULT NULL
);

-- 店铺时间段表
CREATE TABLE t_store_duration (
                                  id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                                  begin_time VARCHAR(20) DEFAULT NULL,
                                  end_time VARCHAR(20) DEFAULT NULL,
                                  store_id BIGINT DEFAULT NULL,
                                  store_code VARCHAR(20) DEFAULT NULL,
                                  create_time TIMESTAMP DEFAULT NULL,
                                  update_time TIMESTAMP DEFAULT NULL,
                                  create_id BIGINT DEFAULT NULL,
                                  create_name VARCHAR(30) DEFAULT NULL,
                                  print_status TINYINT DEFAULT NULL,
                                  printTime TIMESTAMP DEFAULT NULL,
                                  old_line_group_time VARCHAR(50) DEFAULT NULL,
                                  line_group_id BIGINT DEFAULT NULL
);

-- 购物车表
CREATE TABLE t_xda_shopping_cart (
                                     id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                                     store_id BIGINT NOT NULL,
                                     commodity_id BIGINT NOT NULL,
                                     commodity_type INT NOT NULL DEFAULT 1,
                                     quantity DECIMAL(10,3) NOT NULL,
                                     create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                     update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 商品凑整分组表
CREATE TABLE t_commodity_freeze_group (
                                          id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                                          commodity_id BIGINT NOT NULL,
                                          create_id BIGINT NOT NULL,
                                          create_time TIMESTAMP NOT NULL,
                                          update_id BIGINT NOT NULL,
                                          update_time TIMESTAMP NOT NULL
);

-- 订单表
CREATE TABLE t_order (
                         id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                         enterprise_id BIGINT DEFAULT NULL,
                         company_id BIGINT DEFAULT NULL,
                         order_code VARCHAR(20) DEFAULT NULL,
                         store_id BIGINT DEFAULT NULL,
                         real_order_time DATE DEFAULT NULL,
                         order_time DATE DEFAULT NULL,
                         mode_type INT DEFAULT 0,
                         order_type INT DEFAULT NULL,
                         print_num INT DEFAULT NULL,
                         print_type INT DEFAULT NULL,
                         total_amount DECIMAL(15,2) DEFAULT 0.00,
                         real_total_price DECIMAL(15,2) DEFAULT 0.00,
                         final_amount DECIMAL(15,2) DEFAULT 0.00,
                         order_amount DECIMAL(19,2) DEFAULT NULL,
                         freight_amount DECIMAL(6,2) DEFAULT 0.00,
                         promotion_amount DECIMAL(15,2) DEFAULT 0.00,
                         order_remark VARCHAR(1000) DEFAULT NULL,
                         create_time TIMESTAMP DEFAULT NULL,
                         update_id BIGINT DEFAULT NULL,
                         update_time TIMESTAMP DEFAULT NULL,
                         order_status INT DEFAULT NULL,
                         process_status INT DEFAULT NULL,
                         order_duration_time TIMESTAMP DEFAULT NULL,
                         create_id BIGINT DEFAULT NULL,
                         sync_status INT NOT NULL DEFAULT 0,
                         settle_status INT DEFAULT 0,
                         delivery_batch VARCHAR(2) DEFAULT '0',
                         delivery_batch_remark VARCHAR(30) DEFAULT '0_',
                         change_price_status TINYINT DEFAULT 0,
                         cacel_reason_id BIGINT DEFAULT 0,
                         consignment_id BIGINT DEFAULT -1,
                         real_sub_order_done_status TINYINT DEFAULT NULL,
                         presale_status TINYINT DEFAULT 1,
                         delivery_time_range VARCHAR(100) DEFAULT NULL,
                         logistics_center_id BIGINT DEFAULT NULL,
                         logistics_center VARCHAR(100) DEFAULT NULL,
                         business_type TINYINT DEFAULT NULL,
                         settle_order_time DATE DEFAULT NULL,
                         store_type_id BIGINT DEFAULT NULL,
                         driver_id BIGINT DEFAULT NULL,
                         stall_id BIGINT DEFAULT -1,
                         direct_status TINYINT NOT NULL DEFAULT 0,
                         logistics_carrier_code VARCHAR(20) DEFAULT NULL
);

-- 订单明细表
CREATE TABLE t_order_list (
                              id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                              order_id BIGINT DEFAULT NULL,
                              commodity_id BIGINT DEFAULT NULL,
                              commodity_num DECIMAL(19,3) DEFAULT NULL,
                              real_quantity DECIMAL(14,3) DEFAULT 0.000,
                              original_price DECIMAL(14,2) DEFAULT NULL,
                              special_price DECIMAL(14,3) DEFAULT NULL,
                              original_total_price DECIMAL(15,2) DEFAULT NULL,
                              commodity_price DECIMAL(19,2) DEFAULT NULL,
                              total_price DECIMAL(15,2) DEFAULT 0.00,
                              real_total_price DECIMAL(15,2) DEFAULT 0.00,
                              type SMALLINT DEFAULT 1,
                              gift_model_id BIGINT DEFAULT NULL,
                              promotion_id BIGINT DEFAULT NULL,
                              specials_id BIGINT DEFAULT NULL,
                              remark VARCHAR(1000) DEFAULT NULL,
                              presale_status TINYINT DEFAULT 0,
                              comb_type TINYINT DEFAULT 1,
                              update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              comb_commodity_id BIGINT DEFAULT NULL,
                              price_promotion_id BIGINT DEFAULT NULL,
                              coupon_discount_amount DECIMAL(15,2) DEFAULT 0.00,
                              coupon_id BIGINT DEFAULT NULL,
                              coupon_user_id BIGINT DEFAULT NULL
);


-- data.sql (H2测试数据)

-- 店铺数据
INSERT INTO t_store (id, store_code, store_name, min_delivery_amount, update_id) VALUES
                                                                                     (10001, 'STORE001', '测试店铺1', 50, 1),
                                                                                     (10002, 'STORE002', '测试店铺2', NULL, 1),
                                                                                     (10003, 'STORE003', '测试店铺3', 30, 1);

-- 店铺订货时间段数据
INSERT INTO t_store_duration (id, store_id, store_code, begin_time, end_time, create_id, create_name) VALUES
                                                                                                          (1, 10001, 'STORE001', '09:00', '18:00', 1, 'admin'),
                                                                                                          (2, 10003, 'STORE003', '08:00', '20:00', 1, 'admin');
-- 注意：10002店铺故意不插入时间段数据，用于测试无时间段场景

-- 购物车数据
INSERT INTO t_xda_shopping_cart (id, store_id, commodity_id, commodity_type, quantity) VALUES
-- 店铺10001的购物车（用于测试起送金额场景）
(1, 10001, 1001, 1, 3.000),  -- 普通商品
(2, 10001, 1002, 2, 2.000),  -- 特惠商品
(3, 10001, 1003, 1, 1.000),  -- 普通商品

-- 店铺10002的购物车（用于测试无起送金额场景）
(4, 10002, 1004, 1, 2.000),  -- 普通商品
(5, 10002, 1005, 2, 1.000),  -- 特惠商品

-- 店铺10003的购物车（用于测试低金额场景）
(6, 10003, 1006, 1, 1.000);  -- 普通商品

-- 凑整商品配置
INSERT INTO t_commodity_freeze_group (id, commodity_id, create_id, update_id, create_time, update_time) VALUES
                                                                                                            (1, 1001, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                                                                                                            (2, 1004, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 订单明细数据（用于限量查询测试）
INSERT INTO t_order_list (id, order_id, commodity_id, commodity_num, commodity_price, total_price, type) VALUES
                                                                                                             (1, 20001, 1001, 5.000, 10.00, 50.00, 1),
                                                                                                             (2, 20001, 1002, 3.000, 8.00, 24.00, 5),
                                                                                                             (3, 20002, 1003, 2.000, 15.00, 30.00, 1);