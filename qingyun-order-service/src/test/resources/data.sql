-- data.sql (H2测试数据)

-- 店铺数据
INSERT INTO t_store (id, store_code, store_name, min_delivery_amount, update_id) VALUES
(10001, 'STORE001', '测试店铺1', 50, 1),
(10002, 'STORE002', '测试店铺2', NULL, 1),
(10003, 'STORE003', '测试店铺3', 30, 1);

-- 店铺订货时间段数据
INSERT INTO t_store_duration (id, store_id, store_code, begin_time, end_time, create_id, create_name) VALUES
(1, 10001, 'STORE001', '09:00', '18:00', 1, 'admin'),
(2, 10003, 'STORE003', '08:00', '20:00', 1, 'admin');

-- 商品数据
INSERT INTO t_commodity (id, commodity_code, commodity_name, commodity_state, commodity_price) VALUES
(1001, 'C001', '苹果', 1, 10.00),
(1002, 'C002', '香蕉', 1, 8.00),
(1003, 'C003', '橙子', 1, 15.00),
(1004, 'C004', '葡萄', 1, 12.00),
(1005, 'C005', '西瓜', 1, 20.00),
(1006, 'C006', '草莓', 1, 25.00);

-- 购物车数据
INSERT INTO t_xda_shopping_cart (id, store_id, commodity_id, commodity_type, quantity) VALUES
-- 店铺10001的购物车（用于测试起送金额场景）
(1, 10001, 1001, 1, 3.000),  -- 普通商品
(2, 10001, 1002, 2, 2.000),  -- 特惠商品
(3, 10001, 1003, 1, 1.000),  -- 普通商品

-- 店铺10002的购物车（用于测试无起送金额场景）
(4, 10002, 1004, 1, 2.000),  -- 普通商品
(5, 10002, 1005, 2, 1.000),  -- 特惠商品

-- 店铺10003的购物车（用于测试低金额场景）
(6, 10003, 1006, 1, 1.000);  -- 普通商品

-- 凑整商品配置
INSERT INTO t_commodity_freeze_group (id, commodity_id, create_id, update_id, create_time, update_time) VALUES
(1, 1001, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 1004, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 订单明细数据（用于限量查询测试）
INSERT INTO t_order_list (id, order_id, commodity_id, commodity_num, commodity_price, total_price, type) VALUES
(1, 20001, 1001, 5.000, 10.00, 50.00, 1),
(2, 20001, 1002, 3.000, 8.00, 24.00, 5),
(3, 20002, 1003, 2.000, 15.00, 30.00, 1);
